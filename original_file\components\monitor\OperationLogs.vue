<template>
  <div class="operation-logs">
    <!-- 日志过滤器 -->
    <div class="log-filters">
      <n-space :size="16" align="center">
        <n-select
          v-model:value="filterType"
          :options="typeOptions"
          placeholder="选择日志类型"
          style="width: 150px"
          clearable
        />
        
        <n-select
          v-model:value="filterAction"
          :options="actionOptions"
          placeholder="选择操作类型"
          style="width: 150px"
          clearable
        />
        
        <n-date-picker
          v-model:value="dateRange"
          type="datetimerange"
          clearable
          placeholder="选择时间范围"
        />
        
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索设备或消息"
          style="width: 200px"
          clearable
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
        
        <n-button @click="clearFilters">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          清除筛选
        </n-button>
      </n-space>
    </div>

    <!-- 日志列表 -->
    <div class="log-list">
      <n-virtual-list
        :items="filteredLogs"
        :item-size="80"
        style="height: 500px"
      >
        <template #default="{ item }">
          <div class="log-item" :class="`log-${item.type}`">
            <div class="log-header">
              <div class="log-info">
                <n-icon 
                  :size="16" 
                  :color="getLogTypeColor(item.type)"
                  style="margin-right: 8px"
                >
                  <component :is="getLogTypeIcon(item.type)" />
                </n-icon>
                <span class="log-action">{{ item.action }}</span>
                <n-tag 
                  :type="getLogTypeTagType(item.type)" 
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ getLogTypeLabel(item.type) }}
                </n-tag>
              </div>
              
              <div class="log-meta">
                <span class="log-time">{{ formatTime(item.timestamp) }}</span>
                <span class="log-user">{{ item.user }}</span>
              </div>
            </div>
            
            <div class="log-content">
              <div class="log-device" v-if="item.device">
                <n-icon size="14" style="margin-right: 4px">
                  <HardwareChipOutline />
                </n-icon>
                {{ item.device }}
              </div>
              <div class="log-message">{{ item.message }}</div>
            </div>
          </div>
        </template>
      </n-virtual-list>
    </div>

    <!-- 日志统计 -->
    <div class="log-statistics">
      <n-card title="日志统计" size="small">
        <n-grid :cols="24" :x-gap="16">
          <n-grid-item :span="6">
            <n-statistic label="总日志数" :value="logs.length" />
          </n-grid-item>
          <n-grid-item :span="6">
            <n-statistic 
              label="错误日志" 
              :value="getLogCountByType('error')"
              :value-style="{ color: '#FF3B30' }"
            />
          </n-grid-item>
          <n-grid-item :span="6">
            <n-statistic 
              label="警告日志" 
              :value="getLogCountByType('warning')"
              :value-style="{ color: '#FF9500' }"
            />
          </n-grid-item>
          <n-grid-item :span="6">
            <n-statistic 
              label="成功操作" 
              :value="getLogCountByType('success')"
              :value-style="{ color: '#34C759' }"
            />
          </n-grid-item>
        </n-grid>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  SearchOutline,
  RefreshOutline,
  HardwareChipOutline,
  InformationCircleOutline,
  CheckmarkCircleOutline,
  WarningOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'

const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const filterType = ref(null)
const filterAction = ref(null)
const dateRange = ref(null)
const searchKeyword = ref('')

// 过滤选项
const typeOptions = [
  { label: '信息', value: 'info' },
  { label: '成功', value: 'success' },
  { label: '警告', value: 'warning' },
  { label: '错误', value: 'error' }
]

const actionOptions = computed(() => {
  const actions = [...new Set(props.logs.map(log => log.action))]
  return actions.map(action => ({ label: action, value: action }))
})

// 计算属性
const filteredLogs = computed(() => {
  let filtered = [...props.logs]

  // 按类型过滤
  if (filterType.value) {
    filtered = filtered.filter(log => log.type === filterType.value)
  }

  // 按操作过滤
  if (filterAction.value) {
    filtered = filtered.filter(log => log.action === filterAction.value)
  }

  // 按时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    filtered = filtered.filter(log => 
      log.timestamp >= start && log.timestamp <= end
    )
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(log => 
      (log.device && log.device.toLowerCase().includes(keyword)) ||
      log.message.toLowerCase().includes(keyword) ||
      log.action.toLowerCase().includes(keyword)
    )
  }

  // 按时间倒序排列
  return filtered.sort((a, b) => b.timestamp - a.timestamp)
})

// 方法
const getLogTypeIcon = (type) => {
  const iconMap = {
    info: InformationCircleOutline,
    success: CheckmarkCircleOutline,
    warning: WarningOutline,
    error: CloseCircleOutline
  }
  return iconMap[type] || InformationCircleOutline
}

const getLogTypeColor = (type) => {
  const colorMap = {
    info: '#007AFF',
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30'
  }
  return colorMap[type] || '#007AFF'
}

const getLogTypeTagType = (type) => {
  const tagTypeMap = {
    info: 'info',
    success: 'success',
    warning: 'warning',
    error: 'error'
  }
  return tagTypeMap[type] || 'default'
}

const getLogTypeLabel = (type) => {
  const labelMap = {
    info: '信息',
    success: '成功',
    warning: '警告',
    error: '错误'
  }
  return labelMap[type] || '未知'
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLogCountByType = (type) => {
  return props.logs.filter(log => log.type === type).length
}

const clearFilters = () => {
  filterType.value = null
  filterAction.value = null
  dateRange.value = null
  searchKeyword.value = ''
}
</script>
