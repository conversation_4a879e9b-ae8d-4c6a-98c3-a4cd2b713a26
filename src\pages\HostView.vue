<template>
  <div class="host-view-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">主机视角控制</h1>
        <p class="page-description">Unity视角控制中心 - {{ hosts.length }}台主机实时状态监控与视角切换</p>
      </div>
      <div class="header-actions">
        <n-button type="primary" @click="manualRefresh">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新状态
        </n-button>
      </div>
    </div>

    <!-- 主机网格 -->
    <div class="hosts-grid">
      <div
        v-for="host in hosts"
        :key="host.id"
        class="host-card"
        :class="{ 'host-online': host.online, 'host-offline': !host.online }"
      >
        <!-- 主机状态指示器 -->
        <div class="host-status">
          <div class="status-dot" :class="host.online ? 'status-online' : 'status-offline'"></div>
          <span class="status-text">{{ host.online ? '在线' : '离线' }}</span>
        </div>

        <!-- 主机信息 -->
        <div class="host-info">
          <h3 class="host-name">{{ host.name }}</h3>
          <p class="host-ip">{{ host.ip }}</p>
          <div class="host-stats">
            <span class="stat-item">
              <n-icon size="14"><EyeOutline /></n-icon>
              当前视角: {{ host.currentView }}
            </span>
            <span class="stat-item">
              <n-icon size="14"><TimeOutline /></n-icon>
              {{ host.lastUpdate }}
            </span>
          </div>
        </div>

        <!-- Unity视角控制 -->
        <div class="view-controls">
          <n-dropdown
            :options="dropdownOptions"
            @select="(key) => switchView(host.id, key)"
            trigger="click"
            :disabled="!host.online"
          >
            <n-button
              type="primary"
              :disabled="!host.online"
              :loading="host.switching"
              class="view-select-btn"
            >
              <template #icon>
                <n-icon><VideocamOutline /></n-icon>
              </template>
              选择视角
            </n-button>
          </n-dropdown>
        </div>

        <!-- 快捷视角按钮 -->
        <div class="quick-views">
          <div class="view-grid">
            <n-button
              v-for="view in quickViews"
              :key="view.key"
              size="small"
              :type="host.currentView === view.label ? 'primary' : 'default'"
              :disabled="!host.online"
              :loading="host.switching && host.targetView === view.label"
              @click="switchView(host.id, view.key)"
              class="quick-view-btn"
            >
              {{ view.shortLabel }}
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-controls">
      <n-card title="批量操作">
        <div class="batch-actions">
          <n-button-group>
            <n-button
              v-for="view in quickViews"
              :key="view.key"
              @click="batchSwitchView(view.key)"
              :disabled="onlineHostsCount === 0"
            >
              全部{{ view.shortLabel }}
            </n-button>
          </n-button-group>
        </div>
        <div class="batch-info">
          <n-text depth="3">
            在线主机: {{ onlineHostsCount }}/{{ hosts.length }}
          </n-text>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useDeviceStore } from '@/store/device'
import {
  RefreshOutline,
  EyeOutline,
  TimeOutline,
  VideocamOutline
} from '@vicons/ionicons5'

const message = useMessage()
const deviceStore = useDeviceStore()

// 响应式数据 - 从设备存储获取真实设备
const hosts = computed(() => {
  return deviceStore.devices.map((device, index) => ({
    id: device.id || `host_${index + 1}`,
    name: device.name || `主机-${String(index + 1).padStart(2, '0')}`,
    ip: device.ip,
    port: device.port,
    online: device.connected || false,
    currentView: device.currentView || '中视角',
    targetView: '',
    switching: false,
    lastUpdate: device.connected ? new Date().toLocaleTimeString('zh-CN') : '未连接',
    device: device // 保存原始设备引用
  }))
})

// 视角选项配置 - 对应小键盘数字键
const viewOptions = [
  {
    label: '左上视角',
    key: 'top_left',
    icon: '↖',
    keyCode: 'numpad7',
    description: '小键盘7 - 左上视角'
  },
  {
    label: '中上视角',
    key: 'top_center',
    icon: '↑',
    keyCode: 'numpad8',
    description: '小键盘8 - 中上视角'
  },
  {
    label: '右上视角',
    key: 'top_right',
    icon: '↗',
    keyCode: 'numpad9',
    description: '小键盘9 - 右上视角'
  },
  {
    label: '左下视角',
    key: 'bottom_left',
    icon: '↙',
    keyCode: 'numpad4',
    description: '小键盘4 - 左下视角'
  },
  {
    label: '中下视角',
    key: 'bottom_center',
    icon: '↓',
    keyCode: 'numpad5',
    description: '小键盘5 - 中下视角'
  },
  {
    label: '右下视角',
    key: 'bottom_right',
    icon: '↘',
    keyCode: 'numpad6',
    description: '小键盘6 - 右下视角'
  }
]

// 快捷视角按钮配置
const quickViews = [
  { key: 'top_left', label: '左上视角', shortLabel: '左上' },
  { key: 'top_center', label: '中上视角', shortLabel: '中上' },
  { key: 'top_right', label: '右上视角', shortLabel: '右上' },
  { key: 'bottom_left', label: '左下视角', shortLabel: '左下' },
  { key: 'bottom_center', label: '中下视角', shortLabel: '中下' },
  { key: 'bottom_right', label: '右下视角', shortLabel: '右下' }
]

// 计算属性
const onlineHostsCount = computed(() => {
  return hosts.value.filter(host => host.online).length
})

// 下拉菜单选项
const dropdownOptions = computed(() => {
  return viewOptions.map(option => ({
    label: `${option.icon} ${option.label}`,
    key: option.key,
    props: {
      title: option.description
    }
  }))
})

// 方法
const refreshAllHosts = async () => {
  console.log('正在刷新主机状态...')

  // 由于hosts现在是计算属性，会自动根据deviceStore的变化更新
  // 这里主要用于触发设备存储的刷新
  console.log(`主机状态更新完成，在线主机: ${onlineHostsCount.value}/${hosts.value.length}台`)
}

const switchView = async (hostId, viewKey) => {
  const host = hosts.value.find(h => h.id === hostId)
  if (!host || !host.online) {
    message.error('主机离线，无法切换视角')
    return
  }

  // 获取原始设备对象
  const device = host.device

  const viewOption = viewOptions.find(v => v.key === viewKey)
  if (!viewOption) {
    message.error('无效的视角选项')
    return
  }

  host.switching = true
  host.targetView = viewOption.label

  try {
    // 使用主机关联的设备对象
    if (!device || !device.ws || device.ws.readyState !== WebSocket.OPEN) {
      throw new Error('设备连接不可用')
    }

    // 发送视角切换命令到设备
    const command = {
      type: 'unityViewControl',
      hostId: hostId,
      viewKey: viewKey,
      viewName: viewOption.label
    }

    console.log('发送Unity视角切换命令:', command)
    device.ws.send(JSON.stringify(command))

    // 监听响应
    const handleViewResponse = (event) => {
      try {
        const response = JSON.parse(event.data)
        if (response.type === 'unityViewControlResponse' && response.hostId === hostId) {
          if (response.success) {
            host.currentView = viewOption.label
            host.lastUpdate = new Date().toLocaleTimeString('zh-CN')
            message.success(`主机 ${host.name} 已切换到${viewOption.label}`)
          } else {
            message.error(`视角切换失败: ${response.message}`)
          }

          host.switching = false
          host.targetView = ''
          device.ws.removeEventListener('message', handleViewResponse)
        }
      } catch (error) {
        console.error('解析视角切换响应失败:', error)
      }
    }

    device.ws.addEventListener('message', handleViewResponse)

    // 设置超时处理
    setTimeout(() => {
      if (host.switching) {
        host.switching = false
        host.targetView = ''
        device.ws.removeEventListener('message', handleViewResponse)
        message.error('视角切换超时')
      }
    }, 10000)

  } catch (error) {
    console.error('视角切换失败:', error)
    message.error(`视角切换失败: ${error.message}`)
    host.switching = false
    host.targetView = ''
  }
}

const batchSwitchView = async (viewKey) => {
  const onlineHosts = hosts.value.filter(h => h.online)
  if (onlineHosts.length === 0) {
    message.error('没有在线的主机')
    return
  }

  const viewOption = viewOptions.find(v => v.key === viewKey)
  message.info(`正在将 ${onlineHosts.length} 台主机切换到${viewOption.label}...`)

  const promises = onlineHosts.map(host => switchView(host.id, viewKey))
  
  try {
    await Promise.all(promises)
    message.success(`批量切换完成，${onlineHosts.length}台主机已切换到${viewOption.label}`)
  } catch (error) {
    message.error('批量切换过程中出现错误')
  }
}

// 生命周期
onMounted(() => {
  // 初始化主机状态
  refreshAllHosts()

  // 定时刷新主机状态 - 更频繁的检查
  const interval = setInterval(refreshAllHosts, 5000) // 每5秒刷新一次

  // 监听设备存储的变化
  const unwatch = deviceStore.$subscribe(() => {
    // 当设备状态发生变化时，立即刷新主机状态
    refreshAllHosts()
  })

  onUnmounted(() => {
    clearInterval(interval)
    unwatch()
  })
})

// 添加手动刷新方法供按钮调用
const manualRefresh = () => {
  message.info('正在刷新主机状态...')
  refreshAllHosts()
  setTimeout(() => {
    message.success(`状态刷新完成，在线主机: ${onlineHostsCount.value}台`)
  }, 500)
}
</script>

<style scoped>
/* 主机视角控制页面样式 */
.host-view-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

/* 主机网格 - 优化12台设备布局 */
.hosts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* 大屏幕优化 - 4列布局 */
@media (min-width: 1400px) {
  .hosts-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
}

/* 中等屏幕 - 3列布局 */
@media (min-width: 1024px) and (max-width: 1399px) {
  .hosts-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
  }
}

.host-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.host-card.host-online {
  border-color: #52c41a;
}

.host-card.host-offline {
  border-color: #ff4d4f;
  opacity: 0.7;
}

.host-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 主机状态 */
.host-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-online {
  background: #52c41a;
}

.status-offline {
  background: #ff4d4f;
  animation: none;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 600;
  font-size: 14px;
}

/* 主机信息 */
.host-info {
  margin-bottom: 16px;
}

.host-name {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.host-ip {
  font-size: 14px;
  color: #86868b;
  margin: 0 0 12px 0;
  font-family: 'Courier New', monospace;
}

.host-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

/* 视角控制 */
.view-controls {
  margin-bottom: 16px;
}

.view-select-btn {
  width: 100%;
}

/* 快捷视角按钮 */
.quick-views {
  margin-top: 12px;
}

.view-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.quick-view-btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* 批量操作 */
.batch-controls {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.batch-actions {
  margin-bottom: 12px;
}

.batch-info {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hosts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
  }

  .host-card {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .host-view-page {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .view-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
