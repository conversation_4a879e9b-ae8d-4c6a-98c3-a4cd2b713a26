/**
 * Windows按键模拟脚本
 * 用于控制本地应用的全屏、播放、音量等功能
 */

const { spawn, exec } = require('child_process')
const path = require('path')
const fs = require('fs')

class KeySimulator {
  constructor() {
    this.isWindows = process.platform === 'win32'
    this.currentApp = null
    this.currentProcess = null // 当前运行的进程
    this.currentAppType = null // 当前应用类型
    this.defaultVolume = 50
    this.isFullscreen = false
    this.lastFullscreenTime = 0 // 记录最后一次全屏时间
    this.deviceType = this.detectDeviceType() // 检测设备类型
    
    // 初始化PowerShell脚本路径
    this.scriptsPath = path.join(__dirname, 'powershell')
    this.ensureScriptsDirectory()
  }

  /**
   * 检测设备类型（笔记本或台式机）
   */
  detectDeviceType() {
    try {
      // 通过检测电池来判断是否为笔记本
      const os = require('os')
      const { execSync } = require('child_process')

      // 使用wmic命令检测电池
      const batteryCheck = execSync('wmic path Win32_Battery get BatteryStatus /value', { encoding: 'utf8' })

      if (batteryCheck.includes('BatteryStatus=')) {
        console.log('检测到笔记本电脑，使用 Fn+F11 全屏')
        return 'laptop'
      } else {
        console.log('检测到台式机，使用 F11 全屏')
        return 'desktop'
      }
    } catch (error) {
      // 如果检测失败，默认为台式机
      console.log('设备类型检测失败，默认使用 F11 全屏')
      return 'desktop'
    }
  }

  /**
   * 浏览器全屏控制
   * 当打开播放器页面时自动触发全屏
   */
  async triggerBrowserFullscreen() {
    try {
      console.log(`触发浏览器全屏 (设备类型: ${this.deviceType})`)

      if (this.deviceType === 'laptop') {
        // 笔记本：使用 Fn + F11
        await this.sendLaptopFullscreenKey()
      } else {
        // 台式机：使用 F11
        await this.sendDesktopFullscreenKey()
      }

      console.log('浏览器全屏命令已发送')
      return { success: true, message: '浏览器全屏命令已发送' }
    } catch (error) {
      console.error('触发浏览器全屏失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 发送笔记本全屏按键 (Fn + F11)
   * 注意：Fn键通常无法直接模拟，但F11在大多数笔记本上仍然有效
   */
  async sendLaptopFullscreenKey() {
    return new Promise((resolve, reject) => {
      // 方案1：直接发送F11（在大多数笔记本上仍然有效）
      const script = `
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.SendKeys]::SendWait("{F11}")
Start-Sleep -Milliseconds 500

# 备用方案：尝试组合键
[System.Windows.Forms.SendKeys]::SendWait("^{F11}")
Start-Sleep -Milliseconds 500

# 再次尝试F11
[System.Windows.Forms.SendKeys]::SendWait("{F11}")
      `

      this.executePowerShellCommand(script, (error, result) => {
        if (error) {
          reject(error)
        } else {
          console.log('笔记本全屏按键已发送 (F11 + 备用组合键)')
          resolve(result)
        }
      })
    })
  }

  /**
   * 发送台式机全屏按键 (F11)
   */
  async sendDesktopFullscreenKey() {
    return new Promise((resolve, reject) => {
      const script = `
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.SendKeys]::SendWait("{F11}")
Start-Sleep -Milliseconds 200

# 确保按键生效，再次发送
[System.Windows.Forms.SendKeys]::SendWait("{F11}")
      `

      this.executePowerShellCommand(script, (error, result) => {
        if (error) {
          reject(error)
        } else {
          console.log('台式机全屏按键已发送 (F11)')
          resolve(result)
        }
      })
    })
  }

  /**
   * 延迟触发浏览器全屏
   * 给浏览器一些时间加载页面
   */
  async delayedBrowserFullscreen(delay = 3000) {
    console.log(`${delay}ms后触发浏览器全屏...`)

    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          const result = await this.triggerBrowserFullscreen()
          resolve(result)
        } catch (error) {
          console.error('延迟全屏失败:', error)
          resolve({ success: false, message: error.message })
        }
      }, delay)
    })
  }

  /**
   * 直接执行PowerShell命令
   */
  executePowerShellCommand(script, callback) {
    if (!this.isWindows) {
      return callback(new Error('此功能仅支持Windows系统'))
    }

    // 使用PowerShell直接执行命令
    const command = `powershell.exe -Command "${script.replace(/"/g, '\\"')}"`

    exec(command, { timeout: 10000 }, (error, stdout, stderr) => {
      if (error) {
        console.error('PowerShell命令执行失败:', error)
        return callback(error)
      }

      if (stderr) {
        console.warn('PowerShell警告:', stderr)
      }

      console.log('PowerShell命令执行成功:', stdout)
      callback(null, stdout)
    })
  }

  /**
   * 确保PowerShell脚本目录存在
   */
  ensureScriptsDirectory() {
    if (!fs.existsSync(this.scriptsPath)) {
      fs.mkdirSync(this.scriptsPath, { recursive: true })
    }
    this.createPowerShellScripts()
  }

  /**
   * 创建PowerShell脚本文件
   */
  createPowerShellScripts() {
    // 全屏控制脚本 - 增强版本，支持置顶和持续全屏
    const fullscreenScript = `param([string]$action = "toggle")

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class WindowController {
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

    [DllImport("user32.dll")]
    public static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll")]
    public static extern bool BringWindowToTop(IntPtr hWnd);

    public const int KEYEVENTF_KEYUP = 0x0002;
    public const int VK_F11 = 0x7A;
    public const int VK_ESCAPE = 0x1B;
    public const int VK_CONTROL = 0x11;
    public const int SW_MAXIMIZE = 3;
    public const int SW_RESTORE = 9;
    public const int SW_SHOWMAXIMIZED = 3;

    // 置顶相关常量
    public static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
    public static readonly IntPtr HWND_NOTOPMOST = new IntPtr(-2);
    public const uint SWP_NOMOVE = 0x0002;
    public const uint SWP_NOSIZE = 0x0001;
    public const uint SWP_SHOWWINDOW = 0x0040;

    public static void SendF11() {
        keybd_event(VK_F11, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event(VK_F11, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendCtrlF11() {
        // 按下Ctrl
        keybd_event(VK_CONTROL, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        // 按下F11
        keybd_event(VK_F11, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        // 释放F11
        keybd_event(VK_F11, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        // 释放Ctrl
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendEscape() {
        keybd_event(VK_ESCAPE, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event(VK_ESCAPE, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void MaximizeWindow() {
        IntPtr hwnd = GetForegroundWindow();
        ShowWindow(hwnd, SW_MAXIMIZE);
    }

    public static void SetTopMost() {
        IntPtr hwnd = GetForegroundWindow();
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }

    public static void EnterFullscreenAndStayOnTop() {
        IntPtr hwnd = GetForegroundWindow();
        // 先确保窗口获得焦点
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
        // 等待窗口获得焦点
        System.Threading.Thread.Sleep(500);

        // 先置顶
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        // 等待窗口稳定
        System.Threading.Thread.Sleep(800);

        // 根据设备类型发送不同的全屏快捷键
        SendF11(); // 默认F11，将通过参数控制

        // 等待全屏生效
        System.Threading.Thread.Sleep(1500);

        // 再次确保置顶和焦点
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }

    public static void EnterFullscreenAndStayOnTopLaptop() {
        IntPtr hwnd = GetForegroundWindow();
        // 先确保窗口获得焦点
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
        // 等待窗口获得焦点
        System.Threading.Thread.Sleep(500);

        // 先置顶
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        // 等待窗口稳定
        System.Threading.Thread.Sleep(800);

        // 笔记本使用Ctrl+F11
        SendCtrlF11();

        // 等待全屏生效
        System.Threading.Thread.Sleep(1500);

        // 再次确保置顶和焦点
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }
}
"@

switch ($action) {
    "enter" { [WindowController]::EnterFullscreenAndStayOnTop(); Write-Output "Fullscreen ON with TopMost" }
    "exit" { [WindowController]::SendEscape(); Write-Output "Fullscreen OFF" }
    "toggle" { [WindowController]::SendF11(); Write-Output "Fullscreen TOGGLE" }
    "maximize" { [WindowController]::MaximizeWindow(); Write-Output "Window MAXIMIZED" }
    "topmost" { [WindowController]::SetTopMost(); Write-Output "Window set to TOPMOST" }
    "fullscreen_topmost" { [WindowController]::EnterFullscreenAndStayOnTop(); Write-Output "Fullscreen with TopMost STABLE (F11)" }
    "fullscreen_topmost_laptop" { [WindowController]::EnterFullscreenAndStayOnTopLaptop(); Write-Output "Fullscreen with TopMost STABLE (Ctrl+F11)" }
    default { Write-Output "Invalid action" }
}
`

    // 媒体控制脚本
    const mediaControlScript = `
# 媒体控制脚本
param([string]$action = "play")

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class MediaKeys {
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    public const int KEYEVENTF_KEYUP = 0x0002;
    public const int VK_MEDIA_PLAY_PAUSE = 0xB3;
    public const int VK_MEDIA_STOP = 0xB2;
    public const int VK_MEDIA_PREV_TRACK = 0xB1;
    public const int VK_MEDIA_NEXT_TRACK = 0xB0;
    public const int VK_VOLUME_UP = 0xAF;
    public const int VK_VOLUME_DOWN = 0xAE;
    public const int VK_VOLUME_MUTE = 0xAD;
    public const int VK_SPACE = 0x20;
    public const int VK_CONTROL = 0x11;
    public const int VK_L = 0x4C;
    public const int VK_T = 0x54;
    public const int VK_R = 0x52;
    public const int VK_MENU = 0x12; // Alt key
    public const int VK_RETURN = 0x0D; // Enter key
    public const int VK_F4 = 0x73; // F4 key
    public const int VK_F9 = 0x78; // F9 key
    public const int VK_P = 0x50; // P key
    public const int VK_RBUTTON = 0x02; // Right mouse button
    public const int VK_APPS = 0x5D; // Application menu key

    // 小键盘数字键 - Unity视角控制
    public const int VK_NUMPAD4 = 0x64; // 小键盘4 - 左下视角
    public const int VK_NUMPAD5 = 0x65; // 小键盘5 - 中下视角
    public const int VK_NUMPAD6 = 0x66; // 小键盘6 - 右下视角
    public const int VK_NUMPAD7 = 0x67; // 小键盘7 - 左上视角
    public const int VK_NUMPAD8 = 0x68; // 小键盘8 - 中上视角
    public const int VK_NUMPAD9 = 0x69; // 小键盘9 - 右上视角

    public static void SendKey(int key) {
        keybd_event((byte)key, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event((byte)key, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendKeyCombo(int key1, int key2) {
        keybd_event((byte)key1, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        keybd_event((byte)key2, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event((byte)key2, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        keybd_event((byte)key1, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void EnableLoop() {
        // 专门针对Windows Media Player的循环播放
        Console.WriteLine("开始为Windows Media Player启用循环播放...");

        // 方法1: Ctrl+T (Windows Media Player的标准重复快捷键)
        Console.WriteLine("尝试Ctrl+T (WMP标准重复键)...");
        SendKeyCombo(VK_CONTROL, VK_T);
        System.Threading.Thread.Sleep(800);

        // 方法2: 再次按Ctrl+T切换到"重复播放列表"模式
        Console.WriteLine("再次按Ctrl+T切换重复模式...");
        SendKeyCombo(VK_CONTROL, VK_T);
        System.Threading.Thread.Sleep(800);

        // 方法3: 通过菜单访问 - Alt+P打开播放菜单
        Console.WriteLine("尝试通过菜单访问重复选项...");
        SendKeyCombo(VK_MENU, VK_P); // Alt+P (播放菜单)
        System.Threading.Thread.Sleep(400);
        SendKey(VK_R); // 选择重复选项
        System.Threading.Thread.Sleep(400);
        SendKey(VK_RETURN); // 确认选择
        System.Threading.Thread.Sleep(400);

        // 方法4: 右键菜单方式
        Console.WriteLine("尝试右键菜单...");
        SendKey(VK_APPS); // VK_APPS (应用程序菜单键)
        System.Threading.Thread.Sleep(400);
        SendKey(VK_R); // 选择重复选项
        System.Threading.Thread.Sleep(300);
        SendKey(VK_RETURN); // 确认
        System.Threading.Thread.Sleep(300);

        // 方法5: F9键 (某些版本的WMP)
        Console.WriteLine("尝试F9键...");
        SendKey(VK_F9); // VK_F9
        System.Threading.Thread.Sleep(500);

        // 方法6: 备用的L键
        Console.WriteLine("尝试L键作为备用...");
        SendKey(VK_L);
        System.Threading.Thread.Sleep(500);

        Console.WriteLine("Windows Media Player循环播放设置完成");
    }

    public static void SendAltF4() {
        // 发送Alt+F4关闭窗口
        SendKeyCombo(VK_MENU, VK_F4);
    }
}
"@

switch ($action) {
    "play" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PLAY_PAUSE); Write-Output "PLAY" }
    "pause" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PLAY_PAUSE); Write-Output "PAUSE" }
    "stop" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_STOP); Write-Output "STOP" }
    "space" { [MediaKeys]::SendKey([MediaKeys]::VK_SPACE); Write-Output "SPACE" }
    "next" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_NEXT_TRACK); Write-Output "NEXT" }
    "prev" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PREV_TRACK); Write-Output "PREV" }
    "volumeup" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_UP); Write-Output "VOL_UP" }
    "volumedown" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_DOWN); Write-Output "VOL_DOWN" }
    "mute" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_MUTE); Write-Output "MUTE" }
    "loop" { [MediaKeys]::EnableLoop(); Write-Output "LOOP_ENABLED" }
    "enter" { [MediaKeys]::SendKey([MediaKeys]::VK_RETURN); Write-Output "ENTER_SENT" }
    "ctrl_r" { [MediaKeys]::SendKeyCombo([MediaKeys]::VK_CONTROL, [MediaKeys]::VK_R); Write-Output "CTRL_R_SENT" }
    "numpad4" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD4); Write-Output "NUMPAD4_SENT" }
    "numpad5" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD5); Write-Output "NUMPAD5_SENT" }
    "numpad6" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD6); Write-Output "NUMPAD6_SENT" }
    "numpad7" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD7); Write-Output "NUMPAD7_SENT" }
    "numpad8" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD8); Write-Output "NUMPAD8_SENT" }
    "numpad9" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD9); Write-Output "NUMPAD9_SENT" }
    "altf4" { [MediaKeys]::SendAltF4(); Write-Output "ALT_F4_SENT" }
    "close" { [MediaKeys]::SendAltF4(); Write-Output "WINDOW_CLOSED" }
    default { Write-Output "Invalid action" }
}
`

    // 音量控制脚本
    const volumeControlScript = `
# 音量控制脚本
param([int]$volume = 50)

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class VolumeControl {
    [DllImport("winmm.dll")]
    public static extern int waveOutSetVolume(IntPtr hwo, uint dwVolume);
    
    [DllImport("winmm.dll")]
    public static extern int waveOutGetVolume(IntPtr hwo, out uint dwVolume);
    
    public static void SetVolume(int volume) {
        if (volume < 0) volume = 0;
        if (volume > 100) volume = 100;
        
        uint vol = (uint)((volume * 0xFFFF) / 100);
        uint stereoVol = (vol << 16) | vol;
        waveOutSetVolume(IntPtr.Zero, stereoVol);
    }
    
    public static int GetVolume() {
        uint volume;
        waveOutGetVolume(IntPtr.Zero, out volume);
        return (int)((volume & 0xFFFF) * 100 / 0xFFFF);
    }
}
"@

if ($volume -eq -1) {
    $currentVolume = [VolumeControl]::GetVolume()
    Write-Output "Current volume: $currentVolume"
} else {
    [VolumeControl]::SetVolume($volume)
    Write-Output "Volume set to: $volume"
}
`

    // 写入脚本文件
    fs.writeFileSync(path.join(this.scriptsPath, 'fullscreen.ps1'), fullscreenScript)
    fs.writeFileSync(path.join(this.scriptsPath, 'mediacontrol.ps1'), mediaControlScript)
    fs.writeFileSync(path.join(this.scriptsPath, 'volume.ps1'), volumeControlScript)
    
    console.log('PowerShell脚本已创建')
  }

  /**
   * 执行PowerShell脚本
   */
  async executePowerShell(scriptName, params = '') {
    if (!this.isWindows) {
      throw new Error('此功能仅支持Windows系统')
    }

    const scriptPath = path.join(this.scriptsPath, `${scriptName}.ps1`)
    const command = `powershell.exe -ExecutionPolicy Bypass -File "${scriptPath}" ${params}`

    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error(`PowerShell执行错误: ${error}`)
          reject(error)
          return
        }
        
        if (stderr) {
          console.warn(`PowerShell警告: ${stderr}`)
        }
        
        console.log(`PowerShell输出: ${stdout}`)
        resolve(stdout.trim())
      })
    })
  }

  /**
   * 全屏控制
   */
  async toggleFullscreen(action = 'toggle') {
    try {
      const now = Date.now()

      // 根据设备类型调整全屏操作
      if (action === 'fullscreen_topmost') {
        if (this.deviceType === 'laptop') {
          action = 'fullscreen_topmost_laptop'
          console.log(`检测到笔记本，使用 Ctrl+F11 全屏`)
        } else {
          console.log(`检测到台式机，使用 F11 全屏`)
        }
      }

      // 只对连续的相同全屏操作进行防重复检查，缩短时间间隔
      if ((action === 'fullscreen_topmost' || action === 'fullscreen_topmost_laptop' || action === 'enter') &&
          (now - this.lastFullscreenTime < 2000)) {
        console.log('防止重复全屏调用，跳过此次操作')
        return { success: true, message: 'Skipped duplicate fullscreen call', isFullscreen: this.isFullscreen }
      }

      console.log(`执行全屏操作: ${action}`)
      const result = await this.executePowerShell('fullscreen', action)

      // 更新全屏状态和时间
      if (action === 'fullscreen_topmost' || action === 'fullscreen_topmost_laptop' || action === 'enter') {
        this.isFullscreen = true
        this.lastFullscreenTime = now
      } else if (action === 'exit') {
        this.isFullscreen = false
      }

      return { success: true, message: result, isFullscreen: this.isFullscreen }
    } catch (error) {
      console.error('全屏控制失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 媒体播放控制
   */
  async mediaControl(action) {
    try {
      const result = await this.executePowerShell('mediacontrol', action)
      return { success: true, message: result }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 音量控制
   */
  async setVolume(volume) {
    try {
      const result = await this.executePowerShell('volume', volume.toString())
      this.defaultVolume = volume
      return { success: true, message: result, volume }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取当前音量
   */
  async getVolume() {
    try {
      const result = await this.executePowerShell('volume', '-1')
      const match = result.match(/当前音量: (\d+)%/)
      const volume = match ? parseInt(match[1]) : this.defaultVolume
      return { success: true, volume }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 智能关闭当前应用 - 多种方式确保应用被关闭
   */
  async closeCurrentApp() {
    if (!this.currentApp) {
      console.log('没有当前运行的应用需要关闭')
      return { success: true, message: '没有应用需要关闭' }
    }

    try {
      console.log(`关闭当前应用: ${this.currentApp} (类型: ${this.currentAppType})`)

      // 方法1: 如果是全屏状态，先退出全屏
      if (this.isFullscreen) {
        console.log('退出全屏模式...')
        await this.toggleFullscreen('exit')
        await this.sleep(800)
      }

      // 方法2: 优雅关闭 - 发送Alt+F4关闭当前活动窗口
      console.log('发送Alt+F4关闭窗口...')
      await this.sendAltF4()
      await this.sleep(1000)

      // 方法3: 再次尝试Alt+F4（确保关闭）
      console.log('再次尝试Alt+F4...')
      await this.sendAltF4()
      await this.sleep(800)

      // 方法4: 发送ESC键（某些应用需要）
      console.log('发送ESC键...')
      await this.toggleFullscreen('exit')
      await this.sleep(500)

      // 方法5: 如果还是关不掉，尝试强制关闭常见的应用进程（作为最后手段）
      if (this.currentAppType) {
        console.log('尝试强制关闭相关进程...')
        const processNames = this.getProcessNamesByFileType(path.extname(this.currentApp).toLowerCase())

        // 只关闭最常见的几个进程，避免影响其他应用
        const safeProcesses = ['wmplayer.exe', 'photos.exe', 'photoviewer.exe']
        for (const processName of processNames) {
          if (safeProcesses.includes(processName)) {
            try {
              await this.killProcess(processName)
              await this.sleep(200)
            } catch (error) {
              // 忽略错误，继续尝试其他进程
            }
          }
        }
      }

      // 清理状态
      const oldApp = this.currentApp
      this.currentApp = null
      this.currentProcess = null
      this.currentAppType = null
      this.isFullscreen = false

      console.log(`应用 ${oldApp} 已关闭`)
      return { success: true, message: '应用已关闭' }
    } catch (error) {
      console.error('关闭应用失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 发送Alt+F4关闭窗口
   */
  async sendAltF4() {
    try {
      const result = await this.executePowerShell('mediacontrol', 'altf4')
      return { success: true, message: result }
    } catch (error) {
      console.error('发送Alt+F4失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 测试循环播放功能
   */
  async testLoop() {
    try {
      console.log('开始测试循环播放功能...')
      const result = await this.executePowerShell('mediacontrol', 'loop')
      console.log('循环播放测试结果:', result)
      return { success: true, message: result }
    } catch (error) {
      console.error('循环播放测试失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * Unity视角控制
   */
  async unityViewControl(viewKey) {
    try {
      console.log(`Unity视角控制: ${viewKey}`)

      // 视角键映射
      const viewKeyMap = {
        'top_left': 'numpad7',      // 左上视角 - 小键盘7
        'top_center': 'numpad8',    // 中上视角 - 小键盘8
        'top_right': 'numpad9',     // 右上视角 - 小键盘9
        'bottom_left': 'numpad4',   // 左下视角 - 小键盘4
        'bottom_center': 'numpad5', // 中下视角 - 小键盘5
        'bottom_right': 'numpad6'   // 右下视角 - 小键盘6
      }

      const keyAction = viewKeyMap[viewKey]
      if (!keyAction) {
        throw new Error(`无效的视角键: ${viewKey}`)
      }

      console.log(`执行按键: ${keyAction}`)
      const result = await this.executePowerShell('mediacontrol', keyAction)
      console.log('Unity视角控制结果:', result)

      return {
        success: true,
        message: `视角已切换到${viewKey}`,
        keyAction: keyAction,
        result: result
      }
    } catch (error) {
      console.error('Unity视角控制失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 根据文件类型获取可能的进程名
   */
  getProcessNamesByFileType(fileExt) {
    const processMap = {
      '.mp4': ['wmplayer.exe', 'vlc.exe', 'potplayer.exe', 'mpc-hc.exe', 'mpc-hc64.exe'],
      '.avi': ['wmplayer.exe', 'vlc.exe', 'potplayer.exe', 'mpc-hc.exe', 'mpc-hc64.exe'],
      '.mkv': ['wmplayer.exe', 'vlc.exe', 'potplayer.exe', 'mpc-hc.exe', 'mpc-hc64.exe'],
      '.mov': ['wmplayer.exe', 'vlc.exe', 'potplayer.exe', 'quicktime.exe'],
      '.webm': ['chrome.exe', 'firefox.exe', 'msedge.exe', 'vlc.exe'],
      '.jpg': ['photoviewer.exe', 'photos.exe', 'mspaint.exe'],
      '.jpeg': ['photoviewer.exe', 'photos.exe', 'mspaint.exe'],
      '.png': ['photoviewer.exe', 'photos.exe', 'mspaint.exe'],
      '.html': ['chrome.exe', 'firefox.exe', 'msedge.exe', 'iexplore.exe'],
      '.htm': ['chrome.exe', 'firefox.exe', 'msedge.exe', 'iexplore.exe'],
      '.pdf': ['acrord32.exe', 'acrobat.exe', 'foxitreader.exe', 'msedge.exe']
    }

    return processMap[fileExt] || []
  }

  /**
   * 强制关闭指定进程
   */
  async killProcess(processName) {
    return new Promise((resolve, reject) => {
      const command = `taskkill /f /im "${processName}"`

      exec(command, (error, stdout, stderr) => {
        if (error) {
          // 进程不存在时也会报错，这是正常的
          resolve({ success: false, message: `进程 ${processName} 不存在或已关闭` })
          return
        }

        console.log(`已关闭进程: ${processName}`)
        resolve({ success: true, message: `进程 ${processName} 已关闭` })
      })
    })
  }

  /**
   * 获取文件类型
   */
  getFileType(filePath) {
    const ext = path.extname(filePath).toLowerCase()
    if (['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'].includes(ext)) {
      return 'video'
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(ext)) {
      return 'image'
    } else if (['.html', '.htm'].includes(ext)) {
      return 'web'
    } else if (['.pdf'].includes(ext)) {
      return 'pdf'
    }
    return 'other'
  }

  /**
   * 判断是否需要关闭当前应用
   */
  shouldCloseCurrentApp(newAppType) {
    if (!this.currentApp || !this.currentAppType) {
      return false // 没有当前应用，不需要关闭
    }

    console.log(`当前应用类型: ${this.currentAppType}, 新应用类型: ${newAppType}`)

    // 定义可以在同一应用中切换的类型组合
    const compatibleTypes = {
      'image': ['image'], // 图片只能和图片兼容
      'video': ['video'], // 视频只能和视频兼容
      'web': ['web'],     // 网页只能和网页兼容
      'pdf': ['pdf'],     // PDF只能和PDF兼容
      'other': ['other']
    }

    // 检查是否兼容
    const currentCompatible = compatibleTypes[this.currentAppType] || []
    const isCompatible = currentCompatible.includes(newAppType)

    console.log(`类型兼容性检查: ${isCompatible ? '兼容' : '不兼容'}`)

    // 不兼容则需要关闭当前应用
    return !isCompatible
  }

  /**
   * 智能打开本地应用 - 根据内容类型决定是否需要关闭当前应用
   */
  async openLocalApp(filePath) {
    if (!this.isWindows) {
      throw new Error('此功能仅支持Windows系统')
    }

    const newAppType = this.getFileType(filePath)
    console.log(`准备打开文件: ${filePath}, 类型: ${newAppType}`)

    // 智能判断是否需要关闭当前应用
    const needClose = this.shouldCloseCurrentApp(newAppType)

    if (needClose && this.currentApp) {
      console.log(`需要关闭当前应用以打开新的${newAppType}文件`)
      await this.closeCurrentApp()
      await this.sleep(1000) // 等待关闭完成
    } else if (this.currentApp) {
      console.log(`当前应用类型兼容，直接切换内容`)
    }

    return new Promise((resolve, reject) => {
      // 使用Windows的start命令打开文件
      const command = `start "" "${filePath}"`
      
      exec(command, (error) => {
        if (error) {
          console.error(`打开应用失败: ${error}`)
          reject(error)
          return
        }

        console.log(`成功打开应用: ${filePath}`)
        this.currentApp = filePath
        this.currentAppType = newAppType

        // 延迟后自动全屏置顶和相应处理
        setTimeout(async () => {
          console.log('开始自动化处理...')

          // 调用全屏置顶
          console.log('执行全屏置顶...')
          await this.toggleFullscreen('fullscreen_topmost')
          await this.sleep(1500) // 等待全屏生效

          // 根据文件类型执行不同的操作
          if (newAppType === 'video') {
            console.log('Windows Media Player视频播放处理...')

            // 快速启动循环播放
            console.log('3秒后启用循环播放...')
            await this.sleep(3000) // 视频打开3秒后开启

            console.log('启用循环播放 (专为Windows Media Player优化)...')
            await this.mediaControl('loop')
            await this.sleep(1000) // 等待循环设置生效

            // 确保播放状态
            console.log('确保视频播放状态...')
            await this.mediaControl('play')
            await this.sleep(500)

            console.log('Windows Media Player设置完成')
          } else if (newAppType === 'image') {
            console.log('图片文件已打开，无需播放控制')
          } else if (newAppType === 'web') {
            console.log('网页文件已打开')
            // 网页可能需要一些时间加载
            await this.sleep(2000)
          } else if (newAppType === 'pdf') {
            console.log('PDF文件已打开')
          }

          // 最后确保置顶
          console.log('确保窗口置顶...')
          await this.toggleFullscreen('topmost')
          console.log('自动化处理完成')
        }, 2000) // 缩短启动延迟到2秒

        resolve({ success: true, message: `已打开: ${filePath}` })
      })
    })
  }

  /**
   * 处理内容推送后的自动化操作 - 优化版本
   */
  async handleContentPush(contentType, filePath) {
    console.log(`处理内容推送: ${contentType}, 文件: ${filePath}`)

    try {
      // 1. 打开本地应用
      await this.openLocalApp(filePath)

      // 2. 等待应用启动
      await this.sleep(4000)

      // 3. 进入全屏模式并置顶（防止通知遮挡）- 只调用一次
      await this.toggleFullscreen('fullscreen_topmost')

      // 4. 等待全屏完全生效
      await this.sleep(2500)

      // 5. 开始播放
      await this.mediaControl('play')

      // 6. 等待播放开始
      await this.sleep(1500)

      // 7. 启用循环播放（针对视频文件）
      if (contentType === 'video') {
        await this.mediaControl('loop')
        await this.sleep(1000)
      }

      // 8. 最后只确保置顶，不再调用全屏
      await this.toggleFullscreen('topmost')

      return {
        success: true,
        message: '内容推送处理完成',
        actions: ['打开应用', '全屏置顶显示', '开始播放', '启用循环', '保持置顶']
      }
    } catch (error) {
      console.error('内容推送处理失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

module.exports = KeySimulator
