<template>
  <div class="file-preview-card">
    <div class="file-header">
      <div class="file-info">
        <div class="file-icon" :class="`file-icon-${getFileTypeClass(file.type)}`">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
          </svg>
        </div>
        <span class="file-name" :title="file.name">{{ file.name }}</span>
      </div>

      <div class="file-actions">
        <div v-if="showPushButton && connectedDevices.length > 0" class="dropdown-wrapper">
          <button
            class="file-btn file-btn-primary"
            :disabled="file.status !== 'finished'"
            @click="toggleDeviceDropdown"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" fill="currentColor"/>
            </svg>
            推送
          </button>

          <div v-if="showDeviceDropdown" class="device-dropdown">
            <div
              v-for="device in connectedDevices"
              :key="`${device.ip}:${device.port}`"
              class="device-option"
              @click="handleDeviceSelect(device)"
            >
              {{ device.name || `设备 ${device.ip}` }}
            </div>
          </div>
        </div>

        <button
          v-else-if="showPushButton"
          class="file-btn file-btn-disabled"
          disabled
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" fill="currentColor"/>
          </svg>
          无设备
        </button>

        <button class="file-btn file-btn-danger" @click="handleRemove">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 文件预览内容 -->
    <div class="preview-content">
      <!-- 图片预览 -->
      <div v-if="isImageFile" class="image-preview">
        <img
          :src="getPreviewUrl()"
          :alt="file.name"
          @load="handleImageLoad"
          @error="handleImageError"
        />
      </div>

      <!-- 视频预览 -->
      <div v-else-if="isVideoFile" class="video-preview">
        <video
          :src="getPreviewUrl()"
          controls
          preload="metadata"
          @loadedmetadata="handleVideoLoad"
          @error="handleVideoError"
        >
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- HTML预览 -->
      <div v-else-if="isHtmlFile" class="html-preview">
        <iframe
          :src="getPreviewUrl()"
          frameborder="0"
          @load="handleIframeLoad"
          @error="handleIframeError"
        ></iframe>
      </div>

      <!-- 文档预览 -->
      <div v-else-if="isDocumentFile" class="document-preview">
        <div class="document-info">
          <div class="document-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="#ff9500"/>
            </svg>
          </div>
          <p class="document-name">{{ file.name }}</p>
          <p class="document-size">{{ formatFileSize(file.file?.size || 0) }}</p>
          <button class="preview-btn" @click="openDocument">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" fill="currentColor"/>
            </svg>
            打开预览
          </button>
        </div>
      </div>

      <!-- 其他文件类型 -->
      <div v-else class="other-preview">
        <div class="file-info">
          <div class="file-icon-large">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="#86868b"/>
            </svg>
          </div>
          <p class="file-name-display">{{ file.name }}</p>
          <p class="file-size">{{ formatFileSize(file.file?.size || 0) }}</p>
        </div>
      </div>
    </div>

    <!-- 文件状态 -->
    <div class="file-footer">
      <div class="file-status">
        <span class="status-badge" :class="`status-${file.status}`">
          {{ getStatusText(file.status) }}
        </span>
        <span class="file-size-text">
          {{ formatFileSize(file.file?.size || 0) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  showPushButton: {
    type: Boolean,
    default: true
  },
  connectedDevices: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['remove', 'push'])

// 响应式数据
const showDeviceDropdown = ref(false)

// 计算属性
const isImageFile = computed(() => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
  return imageTypes.includes(getFileExtension(props.file.name))
})

const isVideoFile = computed(() => {
  const videoTypes = ['mp4', 'webm', 'ogg', 'avi', 'mov']
  return videoTypes.includes(getFileExtension(props.file.name))
})

const isHtmlFile = computed(() => {
  const htmlTypes = ['html', 'htm']
  return htmlTypes.includes(getFileExtension(props.file.name))
})

const isDocumentFile = computed(() => {
  const docTypes = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']
  return docTypes.includes(getFileExtension(props.file.name))
})

// 方法
const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase()
}



const getPreviewUrl = () => {
  if (props.file.url) {
    return props.file.url
  }
  // 如果是本地文件，创建临时URL
  if (props.file.file) {
    return URL.createObjectURL(props.file.file)
  }
  return ''
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



const getStatusText = (status) => {
  const textMap = {
    pending: '等待上传',
    uploading: '上传中',
    finished: '上传完成',
    error: '上传失败',
    removed: '已移除'
  }
  return textMap[status] || '未知状态'
}



const toggleDeviceDropdown = () => {
  showDeviceDropdown.value = !showDeviceDropdown.value
}

const handleDeviceSelect = (device) => {
  showDeviceDropdown.value = false
  const deviceKey = `${device.ip}:${device.port}`
  emit('push', props.file, [deviceKey])
}

const getFileTypeClass = (type) => {
  const typeMap = {
    'image': 'image',
    'video': 'video',
    'document': 'document',
    'html': 'html'
  }
  return typeMap[type] || 'other'
}

const handleRemove = () => {
  emit('remove', { file: props.file })
}



const openDocument = () => {
  const url = getPreviewUrl()
  if (url) {
    window.open(url, '_blank')
  } else {
    message.warning('无法预览此文件')
  }
}

const handleImageLoad = () => {
  console.log('图片加载成功:', props.file.name)
}

const handleImageError = () => {
  console.error('图片加载失败:', props.file.name)
  message.error(`图片 ${props.file.name} 加载失败`)
}

const handleVideoLoad = () => {
  console.log('视频加载成功:', props.file.name)
}

const handleVideoError = () => {
  console.error('视频加载失败:', props.file.name)
  message.error(`视频 ${props.file.name} 加载失败`)
}

const handleIframeLoad = () => {
  console.log('HTML加载成功:', props.file.name)
}

const handleIframeError = () => {
  console.error('HTML加载失败:', props.file.name)
  message.error(`HTML ${props.file.name} 加载失败`)
}
</script>

<style scoped>
.file-preview-card {
  background: #fff;
  border: 1px solid #e5e5e7;
  border-radius: 16px;
  padding: 20px;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
}

.file-preview-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.file-icon-image {
  background: #34c759;
}

.file-icon-video {
  background: #007aff;
}

.file-icon-document {
  background: #ff9500;
}

.file-icon-html {
  background: #ff3b30;
}

.file-icon-other {
  background: #86868b;
}

.file-name {
  font-weight: 500;
  color: #1d1d1f;
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.file-actions {
  display: flex;
  gap: 8px;
  position: relative;
}

.file-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d2d2d7;
  background: #fff;
  color: #1d1d1f;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.file-btn:hover:not(:disabled) {
  border-color: #a1a1a6;
}

.file-btn-primary {
  background: #007aff;
  color: #fff;
  border-color: #007aff;
}

.file-btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.file-btn-danger {
  background: #ff3b30;
  color: #fff;
  border-color: #ff3b30;
}

.file-btn-danger:hover {
  background: #d70015;
}

.file-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-wrapper {
  position: relative;
}

.device-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  margin-top: 4px;
}

.device-option {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #1d1d1f;
  border-bottom: 1px solid #f5f5f7;
}

.device-option:last-child {
  border-bottom: none;
}

.device-option:hover {
  background: #f5f5f7;
}

/* 预览内容 */
.preview-content {
  margin-bottom: 16px;
}

.image-preview img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.video-preview video {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.html-preview iframe {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.document-preview,
.other-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #f5f5f7;
  border-radius: 8px;
}

.document-info,
.file-info {
  text-align: center;
}

.document-icon,
.file-icon-large {
  margin-bottom: 12px;
}

.document-name,
.file-name-display {
  font-size: 15px;
  font-weight: 500;
  color: #1d1d1f;
  margin: 8px 0 4px 0;
}

.document-size,
.file-size {
  font-size: 13px;
  color: #86868b;
  margin: 0 0 12px 0;
}

.preview-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d2d2d7;
  background: #fff;
  color: #1d1d1f;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.preview-btn:hover {
  border-color: #a1a1a6;
}

/* 文件状态 */
.file-footer {
  border-top: 1px solid #f5f5f7;
  padding-top: 16px;
}

.file-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background: #e3f2fd;
  color: #1976d2;
}

.status-uploading {
  background: #fff3cd;
  color: #856404;
}

.status-finished {
  background: #d4edda;
  color: #155724;
}

.status-error {
  background: #f8d7da;
  color: #721c24;
}

.file-size-text {
  font-size: 12px;
  color: #86868b;
}

.preview-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 4px;
  overflow: hidden;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.video-preview video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.html-preview iframe {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.document-preview,
.other-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.document-info,
.file-info {
  text-align: center;
}

.document-name,
.file-name-display {
  margin: 8px 0 4px 0;
  font-weight: 500;
  font-size: 14px;
  word-break: break-all;
}

.document-size,
.file-size {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 12px;
}

.file-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-size-text {
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .preview-content {
    height: 150px;
  }
  
  .file-header {
    gap: 4px;
  }
  
  .file-name {
    font-size: 14px;
  }
}
</style>
