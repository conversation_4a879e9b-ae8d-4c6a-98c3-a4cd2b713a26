
# 媒体控制脚本
param([string]$action = "play")

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class MediaKeys {
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    public const int KEYEVENTF_KEYUP = 0x0002;
    public const int VK_MEDIA_PLAY_PAUSE = 0xB3;
    public const int VK_MEDIA_STOP = 0xB2;
    public const int VK_MEDIA_PREV_TRACK = 0xB1;
    public const int VK_MEDIA_NEXT_TRACK = 0xB0;
    public const int VK_VOLUME_UP = 0xAF;
    public const int VK_VOLUME_DOWN = 0xAE;
    public const int VK_VOLUME_MUTE = 0xAD;
    public const int VK_SPACE = 0x20;
    public const int VK_CONTROL = 0x11;
    public const int VK_L = 0x4C;
    public const int VK_T = 0x54;
    public const int VK_R = 0x52;
    public const int VK_MENU = 0x12; // Alt key
    public const int VK_RETURN = 0x0D; // Enter key
    public const int VK_F4 = 0x73; // F4 key
    public const int VK_F9 = 0x78; // F9 key
    public const int VK_P = 0x50; // P key
    public const int VK_RBUTTON = 0x02; // Right mouse button
    public const int VK_APPS = 0x5D; // Application menu key

    // 小键盘数字键 - Unity视角控制
    public const int VK_NUMPAD4 = 0x64; // 小键盘4 - 左下视角
    public const int VK_NUMPAD5 = 0x65; // 小键盘5 - 中下视角
    public const int VK_NUMPAD6 = 0x66; // 小键盘6 - 右下视角
    public const int VK_NUMPAD7 = 0x67; // 小键盘7 - 左上视角
    public const int VK_NUMPAD8 = 0x68; // 小键盘8 - 中上视角
    public const int VK_NUMPAD9 = 0x69; // 小键盘9 - 右上视角

    public static void SendKey(int key) {
        keybd_event((byte)key, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event((byte)key, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendKeyCombo(int key1, int key2) {
        keybd_event((byte)key1, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        keybd_event((byte)key2, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event((byte)key2, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        keybd_event((byte)key1, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void EnableLoop() {
        // 专门针对Windows Media Player的循环播放
        Console.WriteLine("开始为Windows Media Player启用循环播放...");

        // 方法1: Ctrl+T (Windows Media Player的标准重复快捷键)
        Console.WriteLine("尝试Ctrl+T (WMP标准重复键)...");
        SendKeyCombo(VK_CONTROL, VK_T);
        System.Threading.Thread.Sleep(800);

        // 方法2: 再次按Ctrl+T切换到"重复播放列表"模式
        Console.WriteLine("再次按Ctrl+T切换重复模式...");
        SendKeyCombo(VK_CONTROL, VK_T);
        System.Threading.Thread.Sleep(800);

        // 方法3: 通过菜单访问 - Alt+P打开播放菜单
        Console.WriteLine("尝试通过菜单访问重复选项...");
        SendKeyCombo(VK_MENU, VK_P); // Alt+P (播放菜单)
        System.Threading.Thread.Sleep(400);
        SendKey(VK_R); // 选择重复选项
        System.Threading.Thread.Sleep(400);
        SendKey(VK_RETURN); // 确认选择
        System.Threading.Thread.Sleep(400);

        // 方法4: 右键菜单方式
        Console.WriteLine("尝试右键菜单...");
        SendKey(VK_APPS); // VK_APPS (应用程序菜单键)
        System.Threading.Thread.Sleep(400);
        SendKey(VK_R); // 选择重复选项
        System.Threading.Thread.Sleep(300);
        SendKey(VK_RETURN); // 确认
        System.Threading.Thread.Sleep(300);

        // 方法5: F9键 (某些版本的WMP)
        Console.WriteLine("尝试F9键...");
        SendKey(VK_F9); // VK_F9
        System.Threading.Thread.Sleep(500);

        // 方法6: 备用的L键
        Console.WriteLine("尝试L键作为备用...");
        SendKey(VK_L);
        System.Threading.Thread.Sleep(500);

        Console.WriteLine("Windows Media Player循环播放设置完成");
    }

    public static void SendAltF4() {
        // 发送Alt+F4关闭窗口
        SendKeyCombo(VK_MENU, VK_F4);
    }
}
"@

switch ($action) {
    "play" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PLAY_PAUSE); Write-Output "PLAY" }
    "pause" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PLAY_PAUSE); Write-Output "PAUSE" }
    "stop" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_STOP); Write-Output "STOP" }
    "space" { [MediaKeys]::SendKey([MediaKeys]::VK_SPACE); Write-Output "SPACE" }
    "next" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_NEXT_TRACK); Write-Output "NEXT" }
    "prev" { [MediaKeys]::SendKey([MediaKeys]::VK_MEDIA_PREV_TRACK); Write-Output "PREV" }
    "volumeup" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_UP); Write-Output "VOL_UP" }
    "volumedown" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_DOWN); Write-Output "VOL_DOWN" }
    "mute" { [MediaKeys]::SendKey([MediaKeys]::VK_VOLUME_MUTE); Write-Output "MUTE" }
    "loop" { [MediaKeys]::EnableLoop(); Write-Output "LOOP_ENABLED" }
    "enter" { [MediaKeys]::SendKey([MediaKeys]::VK_RETURN); Write-Output "ENTER_SENT" }
    "ctrl_r" { [MediaKeys]::SendKeyCombo([MediaKeys]::VK_CONTROL, [MediaKeys]::VK_R); Write-Output "CTRL_R_SENT" }
    "numpad4" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD4); Write-Output "NUMPAD4_SENT" }
    "numpad5" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD5); Write-Output "NUMPAD5_SENT" }
    "numpad6" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD6); Write-Output "NUMPAD6_SENT" }
    "numpad7" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD7); Write-Output "NUMPAD7_SENT" }
    "numpad8" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD8); Write-Output "NUMPAD8_SENT" }
    "numpad9" { [MediaKeys]::SendKey([MediaKeys]::VK_NUMPAD9); Write-Output "NUMPAD9_SENT" }
    "altf4" { [MediaKeys]::SendAltF4(); Write-Output "ALT_F4_SENT" }
    "close" { [MediaKeys]::SendAltF4(); Write-Output "WINDOW_CLOSED" }
    default { Write-Output "Invalid action" }
}
