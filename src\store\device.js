import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'

export const useDeviceStore = defineStore('device', () => {
  const devices = ref([
    {
      ip: '*************',
      port: 9000,
      online: false,
      name: '主服务器',
      volume: 60,
      selected: false,
      connected: false,
      ws: null,
      connectionState: 'disconnected',
      lastSeen: null
    },
    {
      ip: '*************',
      port: 9000,
      online: false,
      name: '从服务器',
      volume: 60,
      selected: false,
      connected: false,
      ws: null,
      connectionState: 'disconnected',
      lastSeen: null
    }
  ])

  function updateDeviceIpPort(index, ip, port) {
    devices.value[index].ip = ip
    devices.value[index].port = port
  }

  function toggleSelectDevice(index) {
    devices.value[index].selected = !devices.value[index].selected
  }

  function setDeviceConnected(index, connected, ws = null, connectionState = null) {
    devices.value[index].connected = connected
    devices.value[index].ws = ws
    if (connected) {
      devices.value[index].lastSeen = Date.now()
    }
    if (connectionState) {
      devices.value[index].connectionState = connectionState
    }
  }

  function setDeviceConnectionState(index, connectionState) {
    devices.value[index].connectionState = connectionState
  }

  // 发送命令，改为store内部方法，方便调用
  function sendCommand(device, command) {
    // 支持传入设备对象或索引
    const targetDevice = typeof device === 'number' ? devices.value[device] : device
    const ws = targetDevice?.ws

    if (ws && ws.readyState === WebSocket.OPEN) {
      const message = {
        id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: Date.now(),
        ...command
      }
      ws.send(JSON.stringify(message))
      return Promise.resolve({ success: true })
    } else {
      console.warn('WebSocket 未连接，无法发送指令')
      return Promise.reject(new Error('设备未连接'))
    }
  }

  // 设置设备音量
  function setDeviceVolume(device, volume) {
    return sendCommand(device, {
      type: 'setVolume',
      volume: volume
    })
  }

  // 控制设备全屏
  function toggleDeviceFullscreen(device, action = 'toggle') {
    return sendCommand(device, {
      type: 'toggleFullscreen',
      action: action
    })
  }

  // 控制设备播放
  function controlDevicePlayback(device, action) {
    return sendCommand(device, {
      type: action // play, pause, stop
    })
  }

  // 刷新所有设备状态
  async function refreshAllDevices() {
    const promises = devices.value
      .filter(device => device.connected)
      .map(device => sendCommand(device, { type: 'getStatus' }))

    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.warn('部分设备状态刷新失败:', error)
    }
  }

  const deviceFiles = ref({}) // { 'ip:port': { video:[], image:[], html:[] } }

  function updateDeviceFiles(deviceKey, files) {
    deviceFiles.value[deviceKey] = files
  }

  function addDevice(device) {
    devices.value.push({
      ...device,
      online: false,
      volume: 60,
      ws: null
    })
  }

  function removeDevice(index) {
    devices.value.splice(index, 1)
  }

  function setSelectedDevice(index) {
    devices.value.forEach((device, i) => {
      device.selected = i === index
    })
  }

  // 连接设备的方法
  function connectDevice(index) {
    const device = devices.value[index]
    if (!device) return null

    // 创建设备的响应式引用
    const deviceRef = ref(device)

    // 创建 WebSocket 连接，传入设备索引
    const wsConnection = useWebSocket(deviceRef, null, {
      setDeviceConnected,
      setDeviceConnectionState
    }, index)

    // 存储连接实例以便后续管理
    device.wsConnection = wsConnection

    return wsConnection
  }

  // 断开设备连接
  function disconnectDevice(index) {
    const device = devices.value[index]
    if (device && device.wsConnection) {
      device.wsConnection.disconnect()
      device.wsConnection.cleanup()
      device.wsConnection = null
    }
    setDeviceConnected(index, false, null, 'disconnected')
  }

  return {
    devices,
    updateDeviceIpPort,
    toggleSelectDevice,
    setDeviceConnected,
    setDeviceConnectionState,
    sendCommand,
    setDeviceVolume,
    toggleDeviceFullscreen,
    controlDevicePlayback,
    refreshAllDevices,
    deviceFiles,
    updateDeviceFiles,
    addDevice,
    removeDevice,
    setSelectedDevice,
    connectDevice,
    disconnectDevice
  }
})
