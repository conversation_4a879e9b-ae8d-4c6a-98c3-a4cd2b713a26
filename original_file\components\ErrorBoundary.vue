<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-content">
      <div class="error-icon">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" fill="#ff3b30"/>
        </svg>
      </div>

      <h2 class="error-title">页面加载出错</h2>
      <p class="error-message">{{ errorMessage }}</p>

      <div class="error-actions">
        <button class="error-btn error-btn-primary" @click="retry">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
          </svg>
          重试
        </button>

        <button class="error-btn" @click="goHome">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          返回首页
        </button>
      </div>
      
      <details class="error-details" v-if="errorDetails">
        <summary>错误详情</summary>
        <pre>{{ errorDetails }}</pre>
      </details>
    </div>
    
    <slot v-else />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')

// 捕获错误
onErrorCaptured((error, _instance, info) => {
  console.error('ErrorBoundary 捕获到错误:', error)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorDetails.value = `${error.stack}\n\n组件信息: ${info}`
  
  // 返回 false 阻止错误继续传播
  return false
})

// 重试
const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  
  // 重新加载当前路由
  router.go(0)
}

// 返回首页
const goHome = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  
  router.push('/dashboard')
}
</script>
