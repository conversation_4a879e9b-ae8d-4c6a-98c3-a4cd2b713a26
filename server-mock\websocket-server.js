// 简单的 WebSocket 服务端模拟器
// 用于演示 Pad 控制端的完整工作流程

const WebSocket = require('ws');
const path = require('path');

// 模拟的共享文件夹内容
const mockFiles = [
  {
    name: '公司介绍视频.mp4',
    path: '/shared/videos/company-intro.mp4',
    size: '156.8 MB',
    type: 'video',
    lastModified: '2024-01-20 10:30:00'
  },
  {
    name: '产品演示.mp4',
    path: '/shared/videos/product-demo.mp4',
    size: '89.2 MB',
    type: 'video',
    lastModified: '2024-01-19 15:45:00'
  },
  {
    name: '培训课件.pptx',
    path: '/shared/documents/training.pptx',
    size: '12.5 MB',
    type: 'document',
    lastModified: '2024-01-18 09:20:00'
  },
  {
    name: '公司Logo.png',
    path: '/shared/images/logo.png',
    size: '512 KB',
    type: 'image',
    lastModified: '2024-01-17 14:15:00'
  },
  {
    name: '用户手册.pdf',
    path: '/shared/documents/manual.pdf',
    size: '8.9 MB',
    type: 'document',
    lastModified: '2024-01-16 11:30:00'
  },
  {
    name: '宣传图片.jpg',
    path: '/shared/images/promotion.jpg',
    size: '2.1 MB',
    type: 'image',
    lastModified: '2024-01-15 16:45:00'
  }
];

// 创建 WebSocket 服务器
const wss = new WebSocket.Server({ 
  port: 8080,
  host: '0.0.0.0'
});

console.log('🚀 WebSocket 服务器启动在端口 8080');
console.log('📁 模拟共享文件夹包含', mockFiles.length, '个文件');

wss.on('connection', function connection(ws, req) {
  const clientIP = req.socket.remoteAddress;
  console.log(`📱 新的 Pad 控制端连接: ${clientIP}`);

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '欢迎连接到服务端设备',
    timestamp: new Date().toISOString()
  }));

  ws.on('message', function incoming(data) {
    try {
      const message = JSON.parse(data);
      console.log(`📨 收到命令:`, message);

      // 处理不同类型的命令
      switch (message.type) {
        case 'heartbeat':
          // 心跳响应
          ws.send(JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          }));
          break;

        case 'listFiles':
          // 返回文件列表
          console.log('📂 返回共享文件夹内容');
          setTimeout(() => {
            ws.send(JSON.stringify({
              type: 'fileList',
              files: mockFiles,
              timestamp: new Date().toISOString()
            }));
          }, 500); // 模拟网络延迟
          break;

        case 'playFile':
          // 播放文件
          const file = message.file;
          console.log(`🎬 播放文件: ${file.name}`);
          console.log(`📍 文件路径: ${file.path}`);
          
          // 模拟播放过程
          setTimeout(() => {
            ws.send(JSON.stringify({
              type: 'playResult',
              success: true,
              message: `文件 ${file.name} 开始播放`,
              file: file,
              timestamp: new Date().toISOString()
            }));
          }, 800);

          // 模拟播放状态更新
          setTimeout(() => {
            ws.send(JSON.stringify({
              type: 'playStatus',
              status: 'playing',
              file: file,
              progress: 0,
              timestamp: new Date().toISOString()
            }));
          }, 1500);
          break;

        case 'play':
        case 'pause':
        case 'stop':
          // 播放控制命令
          console.log(`🎮 播放控制: ${message.type}`);
          ws.send(JSON.stringify({
            type: 'controlResult',
            command: message.type,
            success: true,
            message: `${message.type} 命令执行成功`,
            timestamp: new Date().toISOString()
          }));
          break;

        case 'volume':
          // 音量控制
          console.log(`🔊 设置音量: ${message.value}%`);
          ws.send(JSON.stringify({
            type: 'volumeResult',
            volume: message.value,
            success: true,
            timestamp: new Date().toISOString()
          }));
          break;

        default:
          console.log(`❓ 未知命令类型: ${message.type}`);
          ws.send(JSON.stringify({
            type: 'error',
            message: `未知命令类型: ${message.type}`,
            timestamp: new Date().toISOString()
          }));
      }

    } catch (error) {
      console.error('❌ 解析消息失败:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: '消息格式错误',
        timestamp: new Date().toISOString()
      }));
    }
  });

  ws.on('close', function close() {
    console.log(`📱 Pad 控制端断开连接: ${clientIP}`);
  });

  ws.on('error', function error(err) {
    console.error(`❌ WebSocket 错误:`, err);
  });
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  wss.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

console.log('\n📋 支持的命令:');
console.log('  - heartbeat: 心跳检测');
console.log('  - listFiles: 获取文件列表');
console.log('  - playFile: 播放指定文件');
console.log('  - play/pause/stop: 播放控制');
console.log('  - volume: 音量控制');
console.log('\n💡 使用 Ctrl+C 停止服务器');
