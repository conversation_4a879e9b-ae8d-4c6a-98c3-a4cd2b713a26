<template>
  <n-config-provider :theme="lightTheme" :theme-overrides="themeOverrides">
    <n-message-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <div class="app-layout">
            <!-- 顶部导航栏 - 针对11寸平板横屏优化 -->
            <header class="app-header">
              <div class="header-container">
                <!-- Logo -->
                <div class="logo-section">
                  <n-icon size="18" color="#000">
                    <DesktopOutline />
                  </n-icon>
                  <span class="logo-text">控制中心</span>
                </div>

                <!-- 导航菜单 -->
                <div class="nav-section">
                  <div class="nav-menu">
                    <div
                      v-for="item in menuOptions"
                      :key="item.key"
                      class="nav-item"
                      :class="{ 'nav-item-active': activeMenu === item.key }"
                      @click="handleMenuSelect(item.key)"
                    >
                      {{ item.label }}
                    </div>
                  </div>
                </div>

                <!-- 右侧工具栏 -->
                <div class="tools-section">
                  <n-space align="center" :size="12">
                    <n-badge :value="onlineDeviceCount" :max="99" type="success">
                      <n-button quaternary circle size="small">
                        <template #icon>
                          <n-icon size="14">
                            <WifiOutline />
                          </n-icon>
                        </template>
                      </n-button>
                    </n-badge>
                    <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
                      <n-button quaternary circle size="small">
                        <template #icon>
                          <n-icon size="14">
                            <PersonOutline />
                          </n-icon>
                        </template>
                      </n-button>
                    </n-dropdown>
                  </n-space>
                </div>
              </div>
            </header>

            <!-- 主内容 -->
            <main class="app-content">
              <div class="content-wrapper">
                <ErrorBoundary>
                  <router-view v-slot="{ Component }">
                    <transition name="fade" mode="out-in">
                      <component :is="Component" />
                    </transition>
                  </router-view>
                </ErrorBoundary>
              </div>
            </main>
          </div>
        </n-notification-provider>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup>
import { ref, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { lightTheme } from 'naive-ui'
import { themeOverrides } from './theme'
import {
  DesktopOutline,
  WifiOutline,
  PersonOutline,
  SettingsOutline,
  LogOutOutline
} from '@vicons/ionicons5'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const route = useRoute()
const router = useRouter()

// 响应式状态
const activeMenu = ref(route.path)

// 监听路由变化
watch(route, (val) => {
  activeMenu.value = val.path
})

// 菜单配置
const menuOptions = [
  {
    label: '主页',
    key: '/dashboard'
  },
  {
    label: '设备管理',
    key: '/devices'
  },
  {
    label: '内容管理',
    key: '/contents'
  },
  {
    label: '文件上传',
    key: '/upload'
  },
  {
    label: '播放控制',
    key: '/control'
  },
  {
    label: '系统监控',
    key: '/monitor'
  }
]

// 用户菜单配置


const onlineDeviceCount = computed(() => {
  // 这里应该从store获取在线设备数量
  return 3
})

// 事件处理
function handleMenuSelect(key) {
  if (key !== route.path) {
    router.push(key)
  }
}


</script>
