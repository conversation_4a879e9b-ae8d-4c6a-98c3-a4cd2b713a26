
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1 - 看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }

        .dashboard-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .loading-url {
            font-size: 12px;
            opacity: 0.7;
            max-width: 80%;
            text-align: center;
            word-break: break-all;
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            display: none;
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载看板内容...</div>
            <div class="loading-url">https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=47&userName=80415865&token=fclAIJn3VmLYpy%2F4xj%2Fu3Fr8s0%2BNkQMnyBFg0AiyZyHVVmBWamj27EKgL3dB4rQ7dnRhh4s1%2BPwYNfq3YPr5XFhMdbqkXCQey9vhJ3dlbp4B56p10vLUYXfExp9ytSSrEz1C6X7NRJUefe8nQ%2B0eRNsg%2FTUCHC53pvd%2BtgWlwYEdNiL8Ho060FJgU%2B0QUWvC89HoBoJbnHCIIQODFVbngrU%2BeVviuISgT9MjiMz7%2B%2BNqUYC%2BgyF057K5uohPuPpvHmnnK68xCZBev2%2FN60tsbn34MaDE3CyL3xpcs2QRTtn2hlqFLVwLEb4KZE9G2n1n2ft%2FwzdXHic5V1Gekl3zrmJGNRxLRZwFZTRNk9dfoELjV%2BQgFCdRug6Y1Px0gRv4c7zmy9cv38LRYiGNDWGP0zcvnxPemyM7l3vbzggZ4lzuvhN32pQDX0r%2F8UL%2FGnmACt3mv1PJsjs2hshfFvKY5SyqfYt1OPOf4%2F427RiX9a4LHhGR08WkuwDK36NGTzDDsW7qObLh%2FnsiAnjnOIaKFa7oWF8Q7rs1pyaWbL8lV0teg%2Fu%2Fn%2BtECteBzDhFJMhfI%2BxsNreO56RXSCpvgOsqw7e%2Fkv%2FYFAj%2FUi1FgHB6fgTDX%2BHizOz8PV6lai29sF%2FqJHoSVJPaEH52JiXRmn876TU08ejpcJXSOFVZ%2F3S8%2BAPMOO9RXfiYTJ2FzR8mx4DNjcPPrrX9vmtbrdiF%2F2XQkowb3GmxRPugQa1pghybcZRV1JTANprT0k97UGgG9up0iPhIdA1VNfBq9RMnxnzKxUiIgh3%2BI4Kz8%2Fp5j0vvK1pZU%2BGnnuONYxjzi%2F3CrfYcCLHgxxFz4%2FwPLjD64SnV2w%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1</div>
            <div class="error-message" id="errorMessage">
                看板加载失败，可能是网络问题或需要登录验证
            </div>
        </div>

        <iframe
            id="dashboardFrame"
            class="dashboard-iframe"
            src="https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=47&userName=80415865&token=fclAIJn3VmLYpy%2F4xj%2Fu3Fr8s0%2BNkQMnyBFg0AiyZyHVVmBWamj27EKgL3dB4rQ7dnRhh4s1%2BPwYNfq3YPr5XFhMdbqkXCQey9vhJ3dlbp4B56p10vLUYXfExp9ytSSrEz1C6X7NRJUefe8nQ%2B0eRNsg%2FTUCHC53pvd%2BtgWlwYEdNiL8Ho060FJgU%2B0QUWvC89HoBoJbnHCIIQODFVbngrU%2BeVviuISgT9MjiMz7%2B%2BNqUYC%2BgyF057K5uohPuPpvHmnnK68xCZBev2%2FN60tsbn34MaDE3CyL3xpcs2QRTtn2hlqFLVwLEb4KZE9G2n1n2ft%2FwzdXHic5V1Gekl3zrmJGNRxLRZwFZTRNk9dfoELjV%2BQgFCdRug6Y1Px0gRv4c7zmy9cv38LRYiGNDWGP0zcvnxPemyM7l3vbzggZ4lzuvhN32pQDX0r%2F8UL%2FGnmACt3mv1PJsjs2hshfFvKY5SyqfYt1OPOf4%2F427RiX9a4LHhGR08WkuwDK36NGTzDDsW7qObLh%2FnsiAnjnOIaKFa7oWF8Q7rs1pyaWbL8lV0teg%2Fu%2Fn%2BtECteBzDhFJMhfI%2BxsNreO56RXSCpvgOsqw7e%2Fkv%2FYFAj%2FUi1FgHB6fgTDX%2BHizOz8PV6lai29sF%2FqJHoSVJPaEH52JiXRmn876TU08ejpcJXSOFVZ%2F3S8%2BAPMOO9RXfiYTJ2FzR8mx4DNjcPPrrX9vmtbrdiF%2F2XQkowb3GmxRPugQa1pghybcZRV1JTANprT0k97UGgG9up0iPhIdA1VNfBq9RMnxnzKxUiIgh3%2BI4Kz8%2Fp5j0vvK1pZU%2BGnnuONYxjzi%2F3CrfYcCLHgxxFz4%2FwPLjD64SnV2w%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1"
            frameborder="0"
            allowfullscreen
            allow="fullscreen; autoplay; encrypted-media"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads"
        ></iframe>
    </div>

    <script>
        const iframe = document.getElementById('dashboardFrame');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const errorMessage = document.getElementById('errorMessage');
        let loadTimeout;

        // 设置加载超时
        loadTimeout = setTimeout(() => {
            console.warn('看板加载超时');
            errorMessage.style.display = 'block';
            errorMessage.textContent = '看板加载超时，请检查网络连接或网站是否可访问';
        }, 30000); // 30秒超时

        // iframe加载完成
        iframe.addEventListener('load', function() {
            console.log('看板加载完成');
            clearTimeout(loadTimeout);

            // 延迟隐藏加载界面，确保内容完全加载
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }, 2000);
        });

        // iframe加载错误
        iframe.addEventListener('error', function() {
            console.error('看板加载失败');
            clearTimeout(loadTimeout);
            errorMessage.style.display = 'block';
            errorMessage.textContent = '看板加载失败，请检查网址是否正确或网站是否可访问';
        });

        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 禁用某些快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });

        // 尝试与iframe内容通信（如果同源）
        try {
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        console.log('可以访问iframe内容');

                        // 隐藏iframe内可能的滚动条
                        const style = iframeDoc.createElement('style');
                        style.textContent = `
                            ::-webkit-scrollbar { display: none; }
                            body { overflow: hidden; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                } catch (e) {
                    console.log('无法访问iframe内容（跨域限制）:', e.message);
                }
            });
        } catch (e) {
            console.log('iframe通信设置失败:', e.message);
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏');
            } else {
                console.log('页面显示');
            }
        });

        console.log('看板页面初始化完成');
        console.log('目标URL:', 'https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=47&userName=80415865&token=fclAIJn3VmLYpy%2F4xj%2Fu3Fr8s0%2BNkQMnyBFg0AiyZyHVVmBWamj27EKgL3dB4rQ7dnRhh4s1%2BPwYNfq3YPr5XFhMdbqkXCQey9vhJ3dlbp4B56p10vLUYXfExp9ytSSrEz1C6X7NRJUefe8nQ%2B0eRNsg%2FTUCHC53pvd%2BtgWlwYEdNiL8Ho060FJgU%2B0QUWvC89HoBoJbnHCIIQODFVbngrU%2BeVviuISgT9MjiMz7%2B%2BNqUYC%2BgyF057K5uohPuPpvHmnnK68xCZBev2%2FN60tsbn34MaDE3CyL3xpcs2QRTtn2hlqFLVwLEb4KZE9G2n1n2ft%2FwzdXHic5V1Gekl3zrmJGNRxLRZwFZTRNk9dfoELjV%2BQgFCdRug6Y1Px0gRv4c7zmy9cv38LRYiGNDWGP0zcvnxPemyM7l3vbzggZ4lzuvhN32pQDX0r%2F8UL%2FGnmACt3mv1PJsjs2hshfFvKY5SyqfYt1OPOf4%2F427RiX9a4LHhGR08WkuwDK36NGTzDDsW7qObLh%2FnsiAnjnOIaKFa7oWF8Q7rs1pyaWbL8lV0teg%2Fu%2Fn%2BtECteBzDhFJMhfI%2BxsNreO56RXSCpvgOsqw7e%2Fkv%2FYFAj%2FUi1FgHB6fgTDX%2BHizOz8PV6lai29sF%2FqJHoSVJPaEH52JiXRmn876TU08ejpcJXSOFVZ%2F3S8%2BAPMOO9RXfiYTJ2FzR8mx4DNjcPPrrX9vmtbrdiF%2F2XQkowb3GmxRPugQa1pghybcZRV1JTANprT0k97UGgG9up0iPhIdA1VNfBq9RMnxnzKxUiIgh3%2BI4Kz8%2Fp5j0vvK1pZU%2BGnnuONYxjzi%2F3CrfYcCLHgxxFz4%2FwPLjD64SnV2w%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1');
        console.log('看板名称:', '1');
    </script>
</body>
</html>