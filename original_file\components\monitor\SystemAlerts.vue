<template>
  <div class="system-alerts">
    <!-- 警告统计 -->
    <div class="alert-summary">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <n-grid-item :span="24" :md="8">
          <n-card class="summary-card error-card">
            <n-statistic 
              label="严重警告" 
              :value="getAlertCountByLevel('error')"
              :value-style="{ color: '#FF3B30' }"
            >
              <template #prefix>
                <n-icon size="20" color="#FF3B30">
                  <AlertCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        
        <n-grid-item :span="24" :md="8">
          <n-card class="summary-card warning-card">
            <n-statistic 
              label="一般警告" 
              :value="getAlertCountByLevel('warning')"
              :value-style="{ color: '#FF9500' }"
            >
              <template #prefix>
                <n-icon size="20" color="#FF9500">
                  <WarningOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
        
        <n-grid-item :span="24" :md="8">
          <n-card class="summary-card info-card">
            <n-statistic 
              label="信息提示" 
              :value="getAlertCountByLevel('info')"
              :value-style="{ color: '#007AFF' }"
            >
              <template #prefix>
                <n-icon size="20" color="#007AFF">
                  <InformationCircleOutline />
                </n-icon>
              </template>
            </n-statistic>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 警告过滤器 -->
    <div class="alert-filters">
      <n-space :size="16" align="center">
        <n-select
          v-model:value="filterLevel"
          :options="levelOptions"
          placeholder="选择警告级别"
          style="width: 150px"
          clearable
        />
        
        <n-select
          v-model:value="filterStatus"
          :options="statusOptions"
          placeholder="选择处理状态"
          style="width: 150px"
          clearable
        />
        
        <n-button @click="clearFilters">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          清除筛选
        </n-button>
        
        <n-button @click="resolveAllAlerts" type="primary">
          <template #icon>
            <n-icon><CheckmarkCircleOutline /></n-icon>
          </template>
          标记全部已解决
        </n-button>
      </n-space>
    </div>

    <!-- 警告列表 -->
    <div class="alert-list">
      <n-space vertical :size="12">
        <div 
          v-for="alert in filteredAlerts" 
          :key="alert.id"
          class="alert-item"
          :class="`alert-${alert.level}`"
        >
          <div class="alert-header">
            <div class="alert-info">
              <n-icon 
                :size="20" 
                :color="getAlertLevelColor(alert.level)"
                style="margin-right: 12px"
              >
                <component :is="getAlertLevelIcon(alert.level)" />
              </n-icon>
              
              <div class="alert-content">
                <h4 class="alert-title">{{ alert.title }}</h4>
                <p class="alert-message">{{ alert.message }}</p>
              </div>
            </div>
            
            <div class="alert-actions">
              <n-tag 
                :type="alert.resolved ? 'success' : getAlertLevelTagType(alert.level)"
                size="small"
                style="margin-bottom: 8px"
              >
                {{ alert.resolved ? '已解决' : '待处理' }}
              </n-tag>
              
              <n-space :size="8">
                <n-button 
                  v-if="!alert.resolved"
                  size="small"
                  type="primary"
                  @click="resolveAlert(alert.id)"
                >
                  标记已解决
                </n-button>
                
                <n-button 
                  size="small"
                  @click="viewAlertDetails(alert)"
                >
                  查看详情
                </n-button>
                
                <n-button 
                  size="small"
                  type="error"
                  @click="dismissAlert(alert.id)"
                >
                  忽略
                </n-button>
              </n-space>
            </div>
          </div>
          
          <div class="alert-footer">
            <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
            <span class="alert-id">ID: {{ alert.id }}</span>
          </div>
        </div>
      </n-space>
      
      <n-empty v-if="filteredAlerts.length === 0" description="暂无警告信息">
        <template #icon>
          <n-icon size="48" color="#909090">
            <CheckmarkCircleOutline />
          </n-icon>
        </template>
      </n-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import {
  AlertCircleOutline,
  WarningOutline,
  InformationCircleOutline,
  CheckmarkCircleOutline,
  RefreshOutline
} from '@vicons/ionicons5'

const props = defineProps({
  alerts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['alert-resolved', 'alert-dismissed'])

const message = useMessage()

// 响应式数据
const filterLevel = ref(null)
const filterStatus = ref(null)

// 过滤选项
const levelOptions = [
  { label: '严重', value: 'error' },
  { label: '警告', value: 'warning' },
  { label: '信息', value: 'info' }
]

const statusOptions = [
  { label: '已解决', value: 'resolved' },
  { label: '待处理', value: 'pending' }
]

// 计算属性
const filteredAlerts = computed(() => {
  let filtered = [...props.alerts]

  // 按级别过滤
  if (filterLevel.value) {
    filtered = filtered.filter(alert => alert.level === filterLevel.value)
  }

  // 按状态过滤
  if (filterStatus.value) {
    if (filterStatus.value === 'resolved') {
      filtered = filtered.filter(alert => alert.resolved)
    } else if (filterStatus.value === 'pending') {
      filtered = filtered.filter(alert => !alert.resolved)
    }
  }

  // 按时间倒序排列，未解决的在前
  return filtered.sort((a, b) => {
    if (a.resolved !== b.resolved) {
      return a.resolved ? 1 : -1
    }
    return b.timestamp - a.timestamp
  })
})

// 方法
const getAlertLevelIcon = (level) => {
  const iconMap = {
    error: AlertCircleOutline,
    warning: WarningOutline,
    info: InformationCircleOutline
  }
  return iconMap[level] || InformationCircleOutline
}

const getAlertLevelColor = (level) => {
  const colorMap = {
    error: '#FF3B30',
    warning: '#FF9500',
    info: '#007AFF'
  }
  return colorMap[level] || '#007AFF'
}

const getAlertLevelTagType = (level) => {
  const tagTypeMap = {
    error: 'error',
    warning: 'warning',
    info: 'info'
  }
  return tagTypeMap[level] || 'default'
}

const getAlertCountByLevel = (level) => {
  return props.alerts.filter(alert => alert.level === level && !alert.resolved).length
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const resolveAlert = (alertId) => {
  emit('alert-resolved', alertId)
  message.success('警告已标记为已解决')
}

const dismissAlert = (alertId) => {
  emit('alert-dismissed', alertId)
  message.info('警告已忽略')
}

const resolveAllAlerts = () => {
  const unresolvedAlerts = props.alerts.filter(alert => !alert.resolved)
  unresolvedAlerts.forEach(alert => {
    emit('alert-resolved', alert.id)
  })
  message.success(`已标记 ${unresolvedAlerts.length} 个警告为已解决`)
}

const viewAlertDetails = (alert) => {
  message.info(`查看警告详情: ${alert.title}`)
  // 这里可以打开详情模态框或跳转到详情页面
}

const clearFilters = () => {
  filterLevel.value = null
  filterStatus.value = null
}
</script>
