<template>
  <div class="contents-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">内容管理</h1>
        <p class="page-description">管理和分发多媒体内容到各个设备</p>
      </div>
      <div class="header-actions">
        <button class="action-btn" @click="refreshFiles">刷新</button>
        <button class="action-btn action-btn-primary" @click="$router.push('/upload')">上传文件</button>
      </div>
    </div>

    <!-- 设备选择 -->
    <div class="device-selection">
      <div class="device-tabs">
        <div 
          v-for="device in connectedDevices" 
          :key="`${device.ip}:${device.port}`"
          class="device-tab"
          :class="{ 'device-tab-active': currentDeviceKey === `${device.ip}:${device.port}` }"
          @click="selectDevice(`${device.ip}:${device.port}`)"
        >
          <div class="device-tab-content">
            <div class="device-status-dot"></div>
            <span class="device-name">{{ device.name || `${device.ip}:${device.port}` }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="connectedDevices.length === 0" class="no-devices">
        <p>暂无已连接的设备</p>
        <button class="action-btn action-btn-primary" @click="$router.push('/devices')">连接设备</button>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="currentDevice" class="files-section">
      <div class="section-header">
        <h2 class="section-title">设备文件</h2>
        <div class="section-actions">
          <button class="action-btn" @click="fetchDeviceFiles(currentDevice)" :disabled="loadingFiles">
            {{ loadingFiles ? '加载中...' : '刷新文件' }}
          </button>
        </div>
      </div>

      <!-- 文件分类和统计 -->
      <div v-if="!loadingFiles && deviceFiles.length > 0" class="file-filters">
        <div class="filter-tabs">
          <div
            v-for="category in fileCategories"
            :key="category.type"
            class="filter-tab"
            :class="{ 'filter-tab-active': currentFilter === category.type }"
            @click="setFilter(category.type)"
          >
            <span class="filter-icon">{{ category.icon }}</span>
            <span class="filter-label">{{ category.label }}</span>
            <span class="filter-count">{{ category.count }}</span>
          </div>
        </div>

        <div class="file-stats">
          <span class="stats-item">
            总计: {{ filteredFiles.length }} 个文件
          </span>
          <span class="stats-item">
            大小: {{ formatFileSize(totalFilteredSize) }}
          </span>
        </div>
      </div>

      <!-- 文件网格 -->
      <div class="files-grid">
        <div
          v-for="file in filteredFiles"
          :key="file.name"
          class="file-card"
          :class="{ 'file-selected': selectedFiles.includes(getOriginalIndex(file)) }"
        >
          <!-- 文件选择框 -->
          <div class="file-checkbox">
            <input
              type="checkbox"
              :checked="selectedFiles.includes(getOriginalIndex(file))"
              @change="toggleFileSelection(getOriginalIndex(file))"
              class="checkbox-input"
            />
          </div>

          <!-- 文件预览 -->
          <div class="file-preview">
            <div class="file-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path v-if="getFileType(file.name) === 'image'" d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="#007aff"/>
                <path v-else-if="getFileType(file.name) === 'video'" d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" fill="#ff9500"/>
                <path v-else d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="#86868b"/>
              </svg>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="file-info">
            <h3 class="file-name">{{ file.name }}</h3>
            <p class="file-size">{{ formatFileSize(file.size) }}</p>
            <div class="file-type">
              <span class="type-badge" :class="`type-${getFileType(file.name)}`">
                {{ getFileTypeLabel(file.name) }}
              </span>
            </div>
          </div>

          <!-- 文件操作 -->
          <div class="file-actions">
            <button class="file-btn file-btn-primary" @click="handlePreview(file)">预览</button>
            <button class="file-btn file-btn-success" @click="pushToPlayer(file)">推送浏览器大屏</button>
            <button class="file-btn file-btn-info" @click="openWithLocalApp(file)">推送到本地播放</button>
          </div>
        </div>
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedFiles.length > 0" class="batch-actions">
        <div class="batch-content">
          <div class="batch-info">
            <span class="batch-count">已选择 {{ selectedFiles.length }} 个文件</span>
          </div>
          <div class="batch-buttons">
            <button class="action-btn" @click="batchPlay">推送到大屏</button>
            <button class="action-btn" @click="batchDownload">推送到本地</button>
            <button class="action-btn file-btn-danger" @click="batchDelete">批量删除</button>
            <button class="action-btn" @click="clearSelection">清除选择</button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loadingFiles && deviceFiles.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" fill="#86868b"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无文件</h3>
        <p class="empty-description">该设备上还没有任何文件</p>
        <button class="action-btn action-btn-primary" @click="$router.push('/upload')">上传文件</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDeviceStore } from '@/store/device'

const router = useRouter()
const deviceStore = useDeviceStore()

// 响应式数据
const currentDeviceKey = ref('')
const deviceFiles = ref([])
const selectedFiles = ref([])
const loadingFiles = ref(false)
const currentFilter = ref('all')

// 计算属性
const connectedDevices = computed(() => 
  deviceStore.devices.filter(device => device.connected)
)

const currentDevice = computed(() =>
  connectedDevices.value.find(device => `${device.ip}:${device.port}` === currentDeviceKey.value)
)

// 文件分类
const fileCategories = computed(() => {
  const categories = [
    { type: 'all', label: '全部', icon: '📁', count: deviceFiles.value.length },
    { type: 'video', label: '视频', icon: '🎬', count: 0 },
    { type: 'image', label: '图片', icon: '🖼️', count: 0 },
    { type: 'audio', label: '音频', icon: '🎵', count: 0 },
    { type: 'html', label: 'HTML', icon: '🌐', count: 0 },
    { type: 'pdf', label: 'PDF', icon: '📄', count: 0 },
    { type: 'word', label: 'Word', icon: '📝', count: 0 },
    { type: 'excel', label: 'Excel', icon: '📊', count: 0 },
    { type: 'ppt', label: 'PPT', icon: '📽️', count: 0 },
    { type: 'text', label: '文本', icon: '📋', count: 0 },
    { type: 'other', label: '其他', icon: '📦', count: 0 }
  ]

  // 计算每个分类的文件数量
  deviceFiles.value.forEach(file => {
    const category = categories.find(cat => cat.type === file.type)
    if (category) {
      category.count++
    }
  })

  // 只返回有文件的分类（除了"全部"）
  return categories.filter(cat => cat.type === 'all' || cat.count > 0)
})

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (currentFilter.value === 'all') {
    return deviceFiles.value
  }
  return deviceFiles.value.filter(file => file.type === currentFilter.value)
})

// 过滤后文件的总大小
const totalFilteredSize = computed(() => {
  return filteredFiles.value.reduce((total, file) => total + (file.size || 0), 0)
})

// 方法
const selectDevice = (deviceKey) => {
  currentDeviceKey.value = deviceKey
  const device = connectedDevices.value.find(d => `${d.ip}:${d.port}` === deviceKey)
  if (device) {
    fetchDeviceFiles(device)
  }
}

// 设置文件类型过滤器
const setFilter = (filterType) => {
  currentFilter.value = filterType
  selectedFiles.value = [] // 清除选择
}

// 获取文件在原始列表中的索引
const getOriginalIndex = (file) => {
  return deviceFiles.value.findIndex(f => f.name === file.name)
}

const fetchDeviceFiles = async (device) => {
  if (!device.connected) {
    alert('设备未连接，无法获取文件列表')
    return
  }

  loadingFiles.value = true
  console.log('正在获取设备文件列表...')

  try {
    const command = { type: 'listFiles' }

    if (device.ws && device.ws.readyState === WebSocket.OPEN) {
      device.ws.send(JSON.stringify(command))

      // 设置监听器等待服务端响应
      const handleMessage = (event) => {
        try {
          const response = JSON.parse(event.data)

          if (response.type === 'fileList') {
            // 优先使用新格式的完整文件列表
            if (response.allFiles && Array.isArray(response.allFiles)) {
              deviceFiles.value = response.allFiles.map(file => ({
                ...file,
                lastModified: file.modified ? new Date(file.modified).toLocaleString('zh-CN') : '未知'
              }))
            } else {
              // 兼容旧格式
              const files = response.files
              const allFilesList = []

              // 合并所有类型的文件
              Object.keys(files).forEach(type => {
                files[type].forEach(file => {
                  allFilesList.push({
                    ...file,
                    type: type,
                    size: file.size || getFileSize(file.name), // 优先使用服务端返回的大小
                    lastModified: file.modified ? new Date(file.modified).toLocaleString('zh-CN') : new Date().toLocaleString('zh-CN')
                  })
                })
              })

              deviceFiles.value = allFilesList
            }

            loadingFiles.value = false
            console.log(`获取到 ${deviceFiles.value.length} 个文件`)

            // 显示统计信息
            if (response.statistics) {
              const totalSizeMB = (response.totalSize / 1024 / 1024).toFixed(2)
              console.log(`文件统计: 总计${response.totalFiles}个文件，${totalSizeMB}MB`)
            }

            device.ws.removeEventListener('message', handleMessage)
          } else if (response.type === 'error') {
            loadingFiles.value = false
            alert(`获取文件列表失败: ${response.message}`)
            device.ws.removeEventListener('message', handleMessage)
          }
        } catch (error) {
          console.error('解析服务端响应失败:', error)
        }
      }

      device.ws.addEventListener('message', handleMessage)

      setTimeout(() => {
        if (loadingFiles.value) {
          loadingFiles.value = false
          alert('获取文件列表超时')
          device.ws.removeEventListener('message', handleMessage)
        }
      }, 10000)

    } else {
      loadingFiles.value = false
      alert('设备连接已断开')
    }

  } catch (error) {
    loadingFiles.value = false
    alert(`获取文件列表失败: ${error.message}`)
  }
}

const getFileType = (filename) => {
  const ext = filename.split('.').pop().toLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) return 'image'
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) return 'video'
  if (['mp3', 'wav', 'flac', 'aac'].includes(ext)) return 'audio'
  return 'document'
}

const getFileTypeLabel = (filename) => {
  const type = getFileType(filename)
  const labels = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  return labels[type] || '文件'
}

const getAppType = (fileType) => {
  const appTypeMap = {
    'video': 'video',
    'image': 'image',
    'html': 'browser',
    'ppt': 'powerpoint',
    'word': 'word',
    'pdf': 'pdf',
    'excel': 'excel',
    'text': 'notepad'
  }
  return appTypeMap[fileType] || 'default'
}

const getFileSize = (filename) => {
  // 模拟文件大小，实际应该从服务器获取
  const sizeMap = {
    'mp4': 15728640,
    'avi': 52428800,
    'png': 2048576,
    'jpg': 3145728,
    'jpeg': 3145728,
    'pdf': 5242880,
    'docx': 1048576,
    'txt': 512000
  }
  const ext = filename.split('.').pop().toLowerCase()
  return sizeMap[ext] || 1024000
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const toggleFileSelection = (index) => {
  const selectedIndex = selectedFiles.value.indexOf(index)
  if (selectedIndex > -1) {
    selectedFiles.value.splice(selectedIndex, 1)
  } else {
    selectedFiles.value.push(index)
  }
}

const clearSelection = () => {
  selectedFiles.value = []
}

const handlePreview = (file) => {
  if (!currentDevice.value) return

  const baseUrl = `http://${currentDevice.value.ip}:9004`
  let fileName = file.name || file.path?.split('/').pop() || file.path?.split('\\').pop()

  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，直接使用原文件名
  }

  // 使用正确的共享文件路径
  const fileUrl = `${baseUrl}/shared/${encodeURIComponent(fileName)}`
  console.log('预览文件URL:', fileUrl)
  window.open(fileUrl, '_blank')
}

const pushToPlayer = (file) => {
  if (!currentDevice.value) return

  console.log('推送到浏览器大屏:', file.name)

  try {
    // 确定文件类型
    const fileType = getFileType(file.name)

    // 发送推送内容命令 - 使用与原始版本相同的格式
    const pushCommand = {
      type: 'pushContent',
      content: {
        type: fileType,
        path: file.name, // 只发送文件名，服务端会处理完整路径
        name: file.name
      }
    }

    if (currentDevice.value.ws && currentDevice.value.ws.readyState === WebSocket.OPEN) {
      currentDevice.value.ws.send(JSON.stringify(pushCommand))
      console.log('已发送推送命令:', pushCommand)

      // 监听推送结果
      const handlePushResponse = (event) => {
        try {
          const response = JSON.parse(event.data)

          if (response.type === 'pushContentResponse') {
            if (response.success) {
              console.log(`文件 ${file.name} 已推送到大屏播放器`)
              alert(`文件 ${file.name} 已推送到大屏播放器`)
            } else {
              console.error('推送失败:', response.message)
              alert(`推送失败: ${response.message}`)
            }
            currentDevice.value.ws.removeEventListener('message', handlePushResponse)
          } else if (response.type === 'error') {
            console.error('推送错误:', response.message)
            alert(`推送失败: ${response.message}`)
            currentDevice.value.ws.removeEventListener('message', handlePushResponse)
          }
        } catch (error) {
          console.error('解析推送响应失败:', error)
        }
      }

      currentDevice.value.ws.addEventListener('message', handlePushResponse)

      // 设置超时清理
      setTimeout(() => {
        currentDevice.value.ws.removeEventListener('message', handlePushResponse)
      }, 10000)

    } else {
      alert('设备连接已断开')
    }
  } catch (error) {
    console.error('推送文件失败:', error)
    alert(`推送文件失败: ${error.message}`)
  }
}

const openWithLocalApp = (file) => {
  if (!currentDevice.value) return

  console.log('推送到本地播放:', file.name)

  try {
    // 先获取服务端的共享路径
    const getPathCommand = {
      type: 'getSharedPath'
    }

    if (currentDevice.value.ws && currentDevice.value.ws.readyState === WebSocket.OPEN) {
      currentDevice.value.ws.send(JSON.stringify(getPathCommand))

      // 监听共享路径响应
      const handlePathResponse = (event) => {
        try {
          const response = JSON.parse(event.data)

          if (response.type === 'sharedPathResponse') {
            // 构建本地文件路径
            const localFilePath = `${response.sharedPath}\\${file.name}`

            // 确定应用类型
            const appType = getAppType(file.type || file.name)

            // 发送打开本地应用命令
            const openCommand = {
              type: 'openWithLocalApp',
              filePath: localFilePath,
              appType: appType
            }

            currentDevice.value.ws.send(JSON.stringify(openCommand))
            console.log('已发送本地应用打开命令:', openCommand)

            // 监听打开结果
            const handleOpenResponse = (event) => {
              try {
                const openResponse = JSON.parse(event.data)

                if (openResponse.type === 'openWithLocalAppResponse') {
                  if (openResponse.success) {
                    console.log(`文件 ${file.name} 已用本地应用打开`)
                    alert(`文件 ${file.name} 已用本地应用打开`)
                  } else {
                    console.error('打开失败:', openResponse.message)
                    alert(`打开失败: ${openResponse.message}`)
                  }
                  currentDevice.value.ws.removeEventListener('message', handleOpenResponse)
                } else if (openResponse.type === 'error') {
                  console.error('打开错误:', openResponse.message)
                  alert(`打开失败: ${openResponse.message}`)
                  currentDevice.value.ws.removeEventListener('message', handleOpenResponse)
                }
              } catch (error) {
                console.error('解析打开响应失败:', error)
              }
            }

            currentDevice.value.ws.addEventListener('message', handleOpenResponse)

            // 设置超时清理
            setTimeout(() => {
              currentDevice.value.ws.removeEventListener('message', handleOpenResponse)
            }, 10000)

            currentDevice.value.ws.removeEventListener('message', handlePathResponse)
          } else if (response.type === 'error') {
            console.error('获取共享路径失败:', response.message)
            alert(`获取共享路径失败: ${response.message}`)
            currentDevice.value.ws.removeEventListener('message', handlePathResponse)
          }
        } catch (error) {
          console.error('解析路径响应失败:', error)
        }
      }

      currentDevice.value.ws.addEventListener('message', handlePathResponse)

      // 设置超时清理
      setTimeout(() => {
        currentDevice.value.ws.removeEventListener('message', handlePathResponse)
      }, 10000)

    } else {
      alert('设备连接已断开')
    }
  } catch (error) {
    console.error('推送到本地应用失败:', error)
    alert(`推送到本地应用失败: ${error.message}`)
  }
}

const batchPlay = () => {
  const selectedFileObjects = selectedFiles.value.map(i => deviceFiles.value[i])
  console.log('批量推送到浏览器大屏:', selectedFileObjects)

  selectedFileObjects.forEach(file => {
    pushToPlayer(file)
  })

  clearSelection()
}

const batchDownload = () => {
  const selectedFileObjects = selectedFiles.value.map(i => deviceFiles.value[i])
  console.log('批量推送到本地播放:', selectedFileObjects)

  selectedFileObjects.forEach(file => {
    openWithLocalApp(file)
  })

  clearSelection()
}

const batchDelete = () => {
  if (confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`)) {
    console.log('批量删除功能暂未实现')
    // 实际项目中需要实现删除逻辑
    clearSelection()
  }
}

const refreshFiles = () => {
  if (currentDevice.value) {
    fetchDeviceFiles(currentDevice.value)
  }
}

// 生命周期
onMounted(() => {
  if (connectedDevices.value.length > 0) {
    selectDevice(`${connectedDevices.value[0].ip}:${connectedDevices.value[0].port}`)
  }
})
</script>

<style scoped>
/* 内容管理页面样式 */
.contents-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f7;
}

.action-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn-primary:hover {
  background: #0056cc;
}

/* 设备选择 */
.device-selection {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-tabs {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.device-tab {
  padding: 12px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-tab:hover {
  background: #f5f5f7;
}

.device-tab-active {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.device-tab-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #34c759;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
}

.no-devices {
  text-align: center;
  padding: 40px;
  color: #86868b;
}

.no-devices p {
  margin: 0 0 16px 0;
  font-size: 16px;
}

/* 文件列表 */
.files-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 文件过滤器 */
.file-filters {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #e5e5e7;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  font-size: 13px;
}

.filter-tab:hover {
  background: #f5f5f7;
  border-color: #007aff;
}

.filter-tab-active {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.filter-icon {
  font-size: 14px;
}

.filter-label {
  font-weight: 500;
}

.filter-count {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 1px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

.filter-tab-active .filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.file-stats {
  display: flex;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #e5e5e7;
  font-size: 13px;
  color: #86868b;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.file-card {
  border: 1px solid #e5e5e7;
  border-radius: 12px;
  padding: 16px;
  background: #f9f9f9;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.file-selected {
  border-color: #007aff;
  background: #f0f8ff;
}

.file-checkbox {
  position: absolute;
  top: 12px;
  right: 12px;
}

.checkbox-input {
  width: 16px;
  height: 16px;
}

.file-preview {
  text-align: center;
  margin-bottom: 16px;
}

.file-icon {
  margin-bottom: 12px;
}

.file-info {
  text-align: center;
  margin-bottom: 16px;
}

.file-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 14px;
  color: #86868b;
  margin: 0 0 8px 0;
}

.file-type {
  margin-bottom: 8px;
}

.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-image {
  background: #e1f5fe;
  color: #0277bd;
}

.type-video {
  background: #fff3e0;
  color: #ef6c00;
}

.type-document {
  background: #f3e5f5;
  color: #7b1fa2;
}

.file-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-btn-primary {
  background: #007aff;
  color: white;
}

.file-btn-primary:hover {
  background: #0056cc;
}

.file-btn-success {
  background: #34c759;
  color: white;
}

.file-btn-success:hover {
  background: #28a745;
}

.file-btn-info {
  background: #5ac8fa;
  color: white;
}

.file-btn-info:hover {
  background: #007aff;
}

/* 批量操作 */
.batch-actions {
  background: #f0f8ff;
  border: 1px solid #007aff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-size: 14px;
  font-weight: 500;
  color: #007aff;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.batch-btn {
  padding: 6px 12px;
  border: 1px solid #007aff;
  background: white;
  color: #007aff;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-btn:hover {
  background: #007aff;
  color: white;
}

.batch-btn-primary {
  background: #007aff;
  color: white;
}

.batch-btn-primary:hover {
  background: #0056cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contents-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .device-tabs {
    flex-direction: column;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
