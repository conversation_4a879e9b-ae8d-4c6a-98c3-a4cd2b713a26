<template>
  <div class="monitor-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">系统监控</h1>
        <p class="page-description">实时监控设备状态和系统运行情况</p>
      </div>
      <div class="header-actions">
        <button class="action-btn" @click="refreshData" :disabled="refreshing">
          {{ refreshing ? '刷新中...' : '刷新数据' }}
        </button>
        <button class="action-btn action-btn-primary" @click="exportLogs">
          导出日志
        </button>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="overview-section">
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon online">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="currentColor"/>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ onlineDevicesCount }}</div>
            <div class="metric-label">在线设备</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon offline">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ offlineDevicesCount }}</div>
            <div class="metric-label">离线设备</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon total">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z" fill="currentColor"/>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ totalDevicesCount }}</div>
            <div class="metric-label">设备总数</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon uptime">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z" fill="currentColor"/>
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ systemUptime }}</div>
            <div class="metric-label">系统运行时间</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控标签页 -->
    <div class="tabs-section">
      <div class="tabs-header">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab-item"
          :class="{ 'tab-active': activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
        </div>
      </div>

      <div class="tab-content">
        <!-- 设备状态 -->
        <div v-if="activeTab === 'devices'" class="tab-panel">
          <DeviceStatusMonitor :devices="devices" />
        </div>

        <!-- 系统日志 -->
        <div v-if="activeTab === 'logs'" class="tab-panel">
          <div class="logs-container">
            <div class="logs-header">
              <h3 class="logs-title">系统日志</h3>
              <button class="action-btn" @click="clearLogs">清空日志</button>
            </div>
            <div class="logs-content">
              <div 
                v-for="(log, index) in systemLogs" 
                :key="index"
                class="log-item"
                :class="`log-${log.level}`"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-level">{{ log.level.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能监控 -->
        <div v-if="activeTab === 'performance'" class="tab-panel">
          <div class="performance-container">
            <h3 class="performance-title">性能监控</h3>
            <div class="performance-metrics">
              <div class="performance-item">
                <div class="performance-label">CPU使用率</div>
                <div class="performance-bar">
                  <div class="performance-fill" :style="{ width: performanceMetrics.cpu + '%' }"></div>
                </div>
                <div class="performance-value">{{ performanceMetrics.cpu }}%</div>
              </div>

              <div class="performance-item">
                <div class="performance-label">内存使用率</div>
                <div class="performance-bar">
                  <div class="performance-fill" :style="{ width: performanceMetrics.memory + '%' }"></div>
                </div>
                <div class="performance-value">{{ performanceMetrics.memory }}%</div>
              </div>

              <div class="performance-item">
                <div class="performance-label">网络使用率</div>
                <div class="performance-bar">
                  <div class="performance-fill" :style="{ width: performanceMetrics.network + '%' }"></div>
                </div>
                <div class="performance-value">{{ performanceMetrics.network }}%</div>
              </div>

              <div class="performance-item">
                <div class="performance-label">存储使用率</div>
                <div class="performance-bar">
                  <div class="performance-fill" :style="{ width: performanceMetrics.storage + '%' }"></div>
                </div>
                <div class="performance-value">{{ performanceMetrics.storage }}%</div>
              </div>

              <div class="performance-item">
                <div class="performance-label">网络延迟</div>
                <div class="performance-value">{{ networkLatency }}ms</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 连接状态 -->
        <div v-if="activeTab === 'connections'" class="tab-panel">
          <div class="connections-container">
            <h3 class="connections-title">连接状态</h3>
            <div class="connections-list">
              <div 
                v-for="device in devices" 
                :key="`${device.ip}:${device.port}`"
                class="connection-item"
                :class="{ 'connection-online': device.connected }"
              >
                <div class="connection-info">
                  <div class="connection-status" :class="{ 'status-online': device.connected }"></div>
                  <div class="connection-details">
                    <div class="connection-name">{{ device.name || `设备 ${device.ip}` }}</div>
                    <div class="connection-address">{{ device.ip }}:{{ device.port }}</div>
                  </div>
                </div>
                <div class="connection-stats">
                  <div class="stat-item">
                    <span class="stat-label">状态:</span>
                    <span class="stat-value" :class="{ 'status-text-online': device.connected }">
                      {{ device.connected ? '在线' : '离线' }}
                    </span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">延迟:</span>
                    <span class="stat-value">{{ device.connected ? '< 50ms' : 'N/A' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDeviceStore } from '@/store/device'
import DeviceStatusMonitor from '@/components/monitor/DeviceStatusMonitor.vue'

const deviceStore = useDeviceStore()

// 响应式数据
const refreshing = ref(false)
const activeTab = ref('devices')
const systemLogs = ref([])
const operationLogs = ref([])
const cpuUsage = ref(45)
const memoryUsage = ref(62)
const networkLatency = ref(23)
const storageUsage = ref(35)
const systemStartTime = ref(Date.now())
const refreshTimer = ref(null)

// 性能指标
const performanceMetrics = ref({
  cpu: 45,
  memory: 62,
  network: 78,
  storage: 35
})

// 标签页配置
const tabs = [
  { key: 'devices', label: '设备状态' },
  { key: 'logs', label: '系统日志' },
  { key: 'performance', label: '性能监控' },
  { key: 'connections', label: '连接状态' }
]

// 计算属性
const devices = computed(() => deviceStore.devices)
const onlineDevicesCount = computed(() => devices.value.filter(d => d.connected).length)
const offlineDevicesCount = computed(() => devices.value.filter(d => !d.connected).length)
const totalDevicesCount = computed(() => devices.value.length)

const systemUptime = computed(() => {
  const uptime = Date.now() - systemStartTime.value
  const hours = Math.floor(uptime / (1000 * 60 * 60))
  return `${hours}h`
})

// 方法
const refreshData = async () => {
  refreshing.value = true
  try {
    await deviceStore.refreshAllDevices()
    
    // 模拟刷新性能数据
    cpuUsage.value = Math.floor(Math.random() * 30) + 20
    memoryUsage.value = Math.floor(Math.random() * 40) + 40
    networkLatency.value = Math.floor(Math.random() * 50) + 10
    
    addLog('info', '数据刷新完成')
  } catch (error) {
    addLog('error', `数据刷新失败: ${error.message}`)
  } finally {
    refreshing.value = false
  }
}

const exportLogs = () => {
  try {
    // 合并系统日志和操作日志
    const allLogs = [
      ...systemLogs.value.map(log => ({ ...log, source: 'system' })),
      ...operationLogs.value.map(log => ({ ...log, source: 'operation' }))
    ].sort((a, b) => b.timestamp - a.timestamp)

    const logsText = allLogs
      .map(log => `[${formatTime(log.timestamp)}] [${log.source.toUpperCase()}] ${log.level.toUpperCase()}: ${log.message}`)
      .join('\n')

    const blob = new Blob([logsText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)

    addLog('info', '日志导出完成')
  } catch (error) {
    addLog('error', '导出日志失败: ' + error.message)
  }
}

const addLog = (level, message) => {
  systemLogs.value.unshift({
    timestamp: Date.now(),
    level,
    message
  })
  
  // 保持最多100条日志
  if (systemLogs.value.length > 100) {
    systemLogs.value = systemLogs.value.slice(0, 100)
  }
}

const clearLogs = () => {
  systemLogs.value = []
  addLog('info', '日志已清空')
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const startAutoRefresh = () => {
  refreshTimer.value = setInterval(() => {
    // 自动刷新设备状态
    deviceStore.refreshAllDevices()

    // 模拟更新性能指标
    performanceMetrics.value = {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      network: Math.floor(Math.random() * 100),
      storage: Math.floor(Math.random() * 100)
    }

    // 更新单独的性能指标
    cpuUsage.value = performanceMetrics.value.cpu
    memoryUsage.value = performanceMetrics.value.memory
    networkLatency.value = Math.floor(Math.random() * 50) + 10
    storageUsage.value = performanceMetrics.value.storage

    addLog('info', '自动刷新完成')
  }, 30000) // 每30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 定时器
let performanceTimer = null

// 生命周期
onMounted(() => {
  addLog('info', '系统监控页面已加载')
  refreshData()
  startAutoRefresh()

  // 定时更新性能数据
  performanceTimer = setInterval(() => {
    cpuUsage.value = Math.floor(Math.random() * 30) + 20
    memoryUsage.value = Math.floor(Math.random() * 40) + 40
    networkLatency.value = Math.floor(Math.random() * 50) + 10
    storageUsage.value = Math.floor(Math.random() * 50) + 20
  }, 5000)
})

onUnmounted(() => {
  stopAutoRefresh()
  if (performanceTimer) {
    clearInterval(performanceTimer)
  }
})
</script>
