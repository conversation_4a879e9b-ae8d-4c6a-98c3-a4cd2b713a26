<template>
  <div class="devices-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">设备管理</h1>
        <p class="page-description">连接和管理您的设备</p>
      </div>
      <div class="header-actions">
        <div class="mode-buttons">
          <button class="mode-btn mode-btn-active">单选模式</button>
          <button class="mode-btn" @click="$router.push('/devices-multi')">多选模式</button>
        </div>
        <div class="action-buttons">
          <button class="action-btn" @click="showConnectionTester = true">连接测试</button>
          <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
        </div>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="devices-section">
      <div class="devices-grid">
        <div
          v-for="(device, index) in devices"
          :key="`${device.ip}:${device.port}`"
          class="device-card"
          :class="{ 
            'device-connected': device.connected,
            'device-connecting': device.connecting
          }"
        >
          <!-- 设备状态指示器 -->
          <div class="device-status-indicator">
            <div 
              class="status-dot" 
              :class="{
                'status-connected': device.connected,
                'status-connecting': device.connecting,
                'status-disconnected': !device.connected && !device.connecting
              }"
            ></div>
          </div>

          <!-- 设备信息 -->
          <div class="device-info">
            <div class="device-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" :fill="device.connected ? '#000' : '#86868b'"/>
              </svg>
            </div>
            
            <h3 class="device-name">{{ device.name || `设备 ${index + 1}` }}</h3>
            <p class="device-address">{{ device.ip }}:{{ device.port }}</p>
            
            <div class="device-status">
              <span 
                class="status-badge"
                :class="{
                  'status-badge-connected': device.connected,
                  'status-badge-connecting': device.connecting,
                  'status-badge-disconnected': !device.connected && !device.connecting
                }"
              >
                {{ getStatusText(device) }}
              </span>
            </div>
          </div>

          <!-- 设备操作 -->
          <div class="device-actions">
            <button
              v-if="!device.connected && !device.connecting"
              class="connect-btn"
              @click="connectDevice(index)"
            >
              连接设备
            </button>
            
            <button
              v-else-if="device.connecting"
              class="connect-btn connect-btn-loading"
              disabled
            >
              连接中...
            </button>
            
            <div v-else class="connected-actions">
              <button
                class="action-btn action-btn-primary"
                @click="goToContents(index)"
              >
                内容管理
              </button>
              <button
                class="action-btn"
                @click="disconnectDevice(index)"
              >
                断开连接
              </button>
            </div>
          </div>

          <!-- 设备菜单 -->
          <div class="device-menu">
            <button class="menu-btn" @click="handleDeviceMenu(index)">⋯</button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="devices.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="#86868b"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无设备</h3>
        <p class="empty-description">请先添加设备以开始使用</p>
        <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <div v-if="showAddDialog" class="modal-overlay" @click="showAddDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">添加设备</h2>
          <button class="modal-close" @click="showAddDialog = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">设备名称</label>
            <input v-model="newDevice.name" class="form-input" placeholder="请输入设备名称" />
          </div>
          <div class="form-group">
            <label class="form-label">IP地址</label>
            <input v-model="newDevice.ip" class="form-input" placeholder="请输入IP地址" />
          </div>
          <div class="form-group">
            <label class="form-label">端口</label>
            <input v-model="newDevice.port" type="number" class="form-input" placeholder="请输入端口号" />
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showAddDialog = false">取消</button>
          <button class="action-btn action-btn-primary" @click="handleAddDevice">添加</button>
        </div>
      </div>
    </div>

    <!-- 连接测试对话框 -->
    <div v-if="showConnectionTester" class="modal-overlay" @click="showConnectionTester = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">连接测试</h2>
          <button class="modal-close" @click="showConnectionTester = false">×</button>
        </div>
        <div class="modal-body">
          <p>连接测试功能开发中...</p>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showConnectionTester = false">关闭</button>
        </div>
      </div>
    </div>

    <!-- 连接成功对话框 -->
    <div v-if="showSuccessModal" class="modal-overlay" @click="showSuccessModal = false">
      <div class="modal-content success-modal" @click.stop>
        <div class="modal-header">
          <div class="success-header">
            <div class="success-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#34c759" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <h2 class="modal-title">连接成功</h2>
          </div>
          <button class="modal-close" @click="showSuccessModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="success-content">
            <p class="success-message">设备 <strong>{{ connectedDeviceName }}</strong> 已成功连接！</p>
            <p class="success-question">是否立即进入内容管理页面获取设备内容？</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showSuccessModal = false">稍后再说</button>
          <button class="action-btn action-btn-primary" @click="goToContentsFromModal">立即进入</button>
        </div>
      </div>
    </div>

    <!-- 连接测试器 -->
    <ConnectionTester v-model="showConnectionTester" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDeviceStore } from '@/store/device'
import ConnectionTester from '@/components/ConnectionTester.vue'

const router = useRouter()
const deviceStore = useDeviceStore()
const { devices, addDevice, connectDevice: storeConnectDevice, disconnectDevice: storeDisconnectDevice } = deviceStore

// 响应式数据
const showAddDialog = ref(false)
const showConnectionTester = ref(false)
const showSuccessModal = ref(false)
const connectedDeviceName = ref('')
const connectedDeviceIndex = ref(-1)
const newDevice = ref({
  name: '',
  ip: '',
  port: 9000
})

// 方法
const getStatusText = (device) => {
  if (device.connecting) return '连接中'
  if (device.connected) return '已连接'
  return '未连接'
}

const connectDevice = async (index) => {
  const device = devices[index]
  device.connecting = true

  try {
    const wsConnection = storeConnectDevice(index)
    if (wsConnection) {
      wsConnection.connect()

      // 监听连接成功
      const checkConnection = setInterval(() => {
        if (device.connected) {
          clearInterval(checkConnection)
          device.connecting = false

          // 显示连接成功提示并询问是否进入内容管理
          showConnectionSuccessModal(device)
        }
      }, 500)

      // 超时处理
      setTimeout(() => {
        if (device.connecting) {
          clearInterval(checkConnection)
          device.connecting = false
          alert('连接超时，请检查设备状态')
        }
      }, 10000)
    }
  } catch (error) {
    device.connecting = false
    console.error('连接失败:', error)
    alert(`连接失败: ${error.message}`)
  }
}

const disconnectDevice = (index) => {
  try {
    storeDisconnectDevice(index)
  } catch (error) {
    console.error('断开失败:', error)
  }
}

const goToContents = () => {
  router.push('/contents')
}

const handleAddDevice = () => {
  if (!newDevice.value.name || !newDevice.value.ip || !newDevice.value.port) {
    alert('请填写完整的设备信息')
    return
  }
  
  // 检查设备是否已存在
  const exists = devices.some(d => d.ip === newDevice.value.ip && d.port === newDevice.value.port)
  if (exists) {
    alert('该设备已存在')
    return
  }
  
  addDevice({
    ...newDevice.value,
    connected: false,
    connecting: false,
    connectionState: 'disconnected'
  })
  
  showAddDialog.value = false
  
  // 重置表单
  newDevice.value = {
    name: '',
    ip: '',
    port: 9000
  }
}

const showConnectionSuccessModal = (device) => {
  connectedDeviceName.value = device.name || `${device.ip}:${device.port}`
  connectedDeviceIndex.value = devices.findIndex(d => d.ip === device.ip && d.port === device.port)
  showSuccessModal.value = true
}

const goToContentsFromModal = () => {
  showSuccessModal.value = false
  router.push('/contents')
}

const handleDeviceMenu = (index) => {
  // 设备菜单功能
  console.log('设备菜单:', index)
}
</script>
