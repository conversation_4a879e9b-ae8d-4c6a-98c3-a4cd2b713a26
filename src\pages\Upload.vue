<template>
  <div class="upload-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">文件上传</h1>
        <p class="page-description">上传多媒体文件到服务器，支持预览和推送</p>
      </div>
      <div class="header-actions">
        <button class="action-btn action-btn-primary" @click="$router.push('/contents')">
          内容管理
        </button>
      </div>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <n-card class="upload-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" style="margin-right: 8px">
              <CloudUploadOutline />
            </n-icon>
            本地文件上传
          </div>
        </template>

        <!-- 本地文件上传区域 -->
        <div class="upload-content">
          <n-upload
            ref="uploadRef"
            :file-list="[]"
            :on-before-upload="handleBeforeUpload"
            :on-remove="handleRemove"
            :custom-request="handleCustomUpload"
            :on-change="handleFileChange"
            multiple
            directory-dnd
            :max="20"
            :accept="acceptedTypes"
            :show-file-list="false"
            :default-upload="false"
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <CloudUploadOutline />
                </n-icon>
              </div>
              <n-text style="font-size: 16px">
                点击或拖拽文件到此区域上传
              </n-text>
              <n-p depth="3" style="margin: 8px 0 0 0">
                支持视频(MP4)、图片(JPG/PNG)、HTML、PPT、Word、PDF、Excel等格式<br/>
                最多可选择20个文件，单个文件不超过100MB
              </n-p>
            </n-upload-dragger>
          </n-upload>



          <!-- 文件预览区域 -->
          <div v-if="fileList.length > 0" class="preview-section">
            <n-divider>已选择的文件 ({{ fileList.length }})</n-divider>

            <!-- 简化的文件列表 -->
            <div class="simple-file-list">
              <n-card
                v-for="file in fileList"
                :key="file.id"
                class="file-item"
                style="margin-bottom: 12px;"
              >
                <template #header>
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <n-icon size="24" color="#00E6B8">
                      <DocumentOutline />
                    </n-icon>
                    <div>
                      <div style="font-weight: 600;">{{ file.name }}</div>
                      <div style="font-size: 12px; color: #666;">
                        {{ formatFileSize(file.size) }} | {{ file.type }}
                      </div>
                    </div>
                  </div>
                </template>

                <template #header-extra>
                  <n-space>
                    <n-dropdown
                      v-if="connectedDevices.length > 0"
                      trigger="click"
                      :options="getDeviceOptions()"
                      @select="(deviceKey) => handlePushSingleFile(file, [deviceKey])"
                    >
                      <n-button size="small" type="primary">
                        <template #icon>
                          <n-icon><SendOutline /></n-icon>
                        </template>
                        推送
                      </n-button>
                    </n-dropdown>
                    <n-button
                      size="small"
                      type="error"
                      @click="removeFile(file)"
                    >
                      <template #icon>
                        <n-icon><TrashOutline /></n-icon>
                      </template>
                    </n-button>
                  </n-space>
                </template>

                <!-- 文件预览 -->
                <div v-if="file.url" class="file-preview">
                  <img
                    v-if="file.type.startsWith('image/')"
                    :src="file.url"
                    :alt="file.name"
                    style="max-width: 100%; max-height: 200px; object-fit: contain;"
                  />
                  <video
                    v-else-if="file.type.startsWith('video/')"
                    :src="file.url"
                    controls
                    style="max-width: 100%; max-height: 200px;"
                  />
                  <div v-else class="file-icon" style="text-align: center; padding: 20px;">
                    <n-icon size="48" color="#666">
                      <DocumentOutline />
                    </n-icon>
                    <p style="margin: 8px 0 0 0; color: #666;">{{ file.type }}</p>
                  </div>
                </div>
              </n-card>
            </div>
          </div>

          <!-- 批量操作 -->
          <div v-if="fileList.length > 0" class="batch-actions">
            <n-space>
              <n-button
                @click="handleClearAll"
                :disabled="fileList.length === 0"
              >
                <template #icon>
                  <n-icon><TrashOutline /></n-icon>
                </template>
                清空列表
              </n-button>
            </n-space>
          </div>


        </div>
      </n-card>
    </div>

    <!-- 设备推送区域 -->
    <div v-if="fileList.length > 0" class="push-section">
      <n-card class="push-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" style="margin-right: 8px">
              <SendOutline />
            </n-icon>
            推送到设备
          </div>
        </template>

        <div v-if="connectedDevices.length > 0">
          <!-- 设备列表 -->
          <div class="device-list">
            <n-grid :cols="24" :x-gap="16" :y-gap="16">
              <n-grid-item
                v-for="device in connectedDevices"
                :key="`${device.ip}:${device.port}`"
                :span="24"
                :sm="12"
                :md="8"
                :lg="6"
              >
                <n-card
                  class="device-card"
                  :class="{ 'device-selected': selectedDevices.includes(getDeviceKey(device)) }"
                  hoverable
                  @click="toggleDeviceSelection(device)"
                >
                  <template #header>
                    <n-space align="center" justify="space-between">
                      <n-space align="center" :size="8">
                        <n-icon :color="device.connected ? '#00E6B8' : '#FF6B6B'">
                          <HardwareChipOutline />
                        </n-icon>
                        <span>{{ device.name || `${device.ip}:${device.port}` }}</span>
                      </n-space>
                      <n-checkbox
                        :checked="selectedDevices.includes(getDeviceKey(device))"
                        @click.stop
                        @update:checked="(checked) => updateDeviceSelection(device, checked)"
                      />
                    </n-space>
                  </template>

                  <div class="device-info">
                    <p><strong>IP:</strong> {{ device.ip }}</p>
                    <p><strong>端口:</strong> {{ device.port }}</p>
                    <p><strong>状态:</strong>
                      <n-tag :type="device.connected ? 'success' : 'error'" size="small">
                        {{ device.connected ? '已连接' : '未连接' }}
                      </n-tag>
                    </p>
                  </div>
                </n-card>
              </n-grid-item>
            </n-grid>
          </div>

          <!-- 推送操作 -->
          <div class="push-actions">
            <n-space>
              <n-button
                type="primary"
                @click="handleSelectAll"
                :disabled="connectedDevices.length === 0"
              >
                <template #icon>
                  <n-icon><CheckmarkCircleOutline /></n-icon>
                </template>
                全选设备
              </n-button>
              <n-button
                @click="handleDeselectAll"
                :disabled="selectedDevices.length === 0"
              >
                <template #icon>
                  <n-icon><CloseCircleOutline /></n-icon>
                </template>
                取消全选
              </n-button>
              <n-button
                type="success"
                @click="handleBatchPush"
                :disabled="selectedDevices.length === 0 || !hasValidFiles"
                :loading="isPushing"
              >
                <template #icon>
                  <n-icon><SendOutline /></n-icon>
                </template>
                推送到选中设备 ({{ selectedDevices.length }})
              </n-button>
            </n-space>

            <!-- 推送进度 -->
            <div v-if="pushProgress.show" class="push-progress">
              <n-progress
                type="line"
                :percentage="pushProgress.percentage"
                :status="pushProgress.status"
                :show-indicator="true"
              />
              <p class="progress-text">{{ pushProgress.text }}</p>
            </div>
          </div>
        </div>

        <div v-else class="no-devices">
          <n-empty description="没有连接的设备">
            <template #extra>
              <n-button @click="$router.push('/devices')">
                前往设备管理
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useDeviceStore } from '@/store/device'
import {
  HardwareChipOutline,
  CloudUploadOutline,
  SendOutline,
  TrashOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  DocumentOutline
} from '@vicons/ionicons5'


const message = useMessage()
const deviceStore = useDeviceStore()

// 响应式数据
const uploadRef = ref(null)
const fileList = ref([])
const selectedDevices = ref([])
const isPushing = ref(false)
const pushProgress = ref({
  show: false,
  percentage: 0,
  status: 'default',
  text: ''
})

// 计算属性
const connectedDevices = computed(() =>
  deviceStore.devices.filter(device => device.connected)
)

const hasValidFiles = computed(() =>
  fileList.value.length > 0
)

// 上传配置
const acceptedTypes = '.mp4,.jpg,.jpeg,.png,.gif,.html,.htm,.ppt,.pptx,.doc,.docx,.pdf,.xls,.xlsx,.txt'

// 工具方法
const getDeviceKey = (device) => `${device.ip}:${device.port}`

// 方法
const handleBeforeUpload = (data) => {
  const { file } = data
  console.log('handleBeforeUpload 被调用:', file.name, file)
  console.log('文件对象keys:', Object.keys(file))

  // 获取原始文件对象
  const rawFile = file.file || file

  // 文件大小检查 (100MB)
  if (rawFile && rawFile.size > 100 * 1024 * 1024) {
    message.error('文件大小不能超过100MB')
    console.log('文件太大，拒绝上传')
    return false
  }

  console.log('文件通过验证，允许处理')
  console.log('原始文件大小:', rawFile ? rawFile.size : '未知')
  return true
}

// 文件变化处理
const handleFileChange = (options) => {
  console.log('handleFileChange 被调用:', options)

  if (options.fileList && options.fileList.length > 0) {
    const latestFile = options.fileList[options.fileList.length - 1]
    console.log('最新文件:', latestFile)

    // 如果custom-request没有被调用，我们在这里手动处理
    if (latestFile.status === 'pending') {
      console.log('文件状态为pending，手动处理文件')
      handleCustomUpload({
        file: latestFile,
        onFinish: () => {
          console.log('手动处理完成')
        },
        onError: () => {
          console.log('手动处理失败')
        }
      })
    }
  }
}

// 自定义上传处理（不实际上传，只是保存文件信息）
const handleCustomUpload = ({ file, onFinish, onError }) => {
  console.log('handleCustomUpload 被调用:', file)
  console.log('文件对象详细信息:', JSON.stringify(file, null, 2))

  try {
    // 检查文件对象 - 在naive-ui中，原始文件在file.file属性中
    const rawFile = file.file || file

    if (!rawFile) {
      console.error('无法找到原始文件对象:', file)
      throw new Error('文件对象不存在')
    }

    console.log('原始文件对象:', rawFile)

    // 创建文件URL用于预览
    const fileUrl = URL.createObjectURL(rawFile)
    console.log('创建文件URL成功:', fileUrl)

    // 创建文件信息对象
    const fileInfo = {
      id: Date.now() + Math.random(),
      name: file.name,
      type: file.type || rawFile.type || getFileTypeFromName(file.name),
      size: rawFile.size,
      url: fileUrl,
      file: rawFile, // 保存原始文件对象
      status: 'finished'
    }

    console.log('创建文件信息对象:', fileInfo)

    // 将文件信息添加到文件列表
    fileList.value.push(fileInfo)
    console.log('文件已添加到列表，当前列表长度:', fileList.value.length)
    console.log('当前文件列表:', fileList.value)

    // 更新文件状态
    file.status = 'finished'

    message.success(`文件 ${file.name} 已添加到列表`)

    onFinish()
  } catch (error) {
    console.error('处理文件失败:', error)
    message.error(`处理文件 ${file.name} 失败: ${error.message}`)
    onError()
  }
}

// 根据文件名获取文件类型
const getFileTypeFromName = (fileName) => {
  const ext = fileName.toLowerCase().split('.').pop()
  const typeMap = {
    'mp4': 'video/mp4',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'html': 'text/html',
    'htm': 'text/html',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }
  return typeMap[ext] || 'application/octet-stream'
}

const handleRemove = (options) => {
  const { file } = options
  console.log('移除文件:', file.name)

  // 释放文件URL
  if (file.url && file.url.startsWith('blob:')) {
    URL.revokeObjectURL(file.url)
  }

  return true
}

// 设备选择相关方法
const toggleDeviceSelection = (device) => {
  const deviceKey = getDeviceKey(device)
  const index = selectedDevices.value.indexOf(deviceKey)

  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(deviceKey)
  }
}

const updateDeviceSelection = (device, checked) => {
  const deviceKey = getDeviceKey(device)

  if (checked && !selectedDevices.value.includes(deviceKey)) {
    selectedDevices.value.push(deviceKey)
  } else if (!checked) {
    const index = selectedDevices.value.indexOf(deviceKey)
    if (index > -1) {
      selectedDevices.value.splice(index, 1)
    }
  }
}

const handleSelectAll = () => {
  selectedDevices.value = connectedDevices.value.map(device => getDeviceKey(device))
  message.info(`已选择所有 ${selectedDevices.value.length} 个设备`)
}

const handleDeselectAll = () => {
  selectedDevices.value = []
  message.info('已取消选择所有设备')
}

// 单个文件推送
const handlePushSingleFile = async (file, targetDevices) => {
  // 如果targetDevices是字符串，转换为数组
  if (typeof targetDevices === 'string') {
    if (targetDevices === 'all') {
      targetDevices = connectedDevices.value.map(device => getDeviceKey(device))
    } else {
      targetDevices = [targetDevices]
    }
  }

  if (!targetDevices || targetDevices.length === 0) {
    message.error('请选择要推送的设备')
    return
  }

  try {
    message.info(`开始推送文件 ${file.name} 到 ${targetDevices.length} 个设备`)

    let successCount = 0
    let errorCount = 0

    // 遍历目标设备
    for (const deviceKey of targetDevices) {
      const device = connectedDevices.value.find(d => getDeviceKey(d) === deviceKey)
      if (!device || !device.connected) {
        console.warn(`设备 ${deviceKey} 未连接，跳过`)
        errorCount++
        continue
      }

      try {
        await pushFileToDevice(file, device)
        successCount++
      } catch (error) {
        console.error(`推送文件 ${file.name} 到设备 ${deviceKey} 失败:`, error)
        errorCount++
      }
    }

    if (successCount > 0) {
      message.success(`文件 ${file.name} 推送完成！成功: ${successCount}, 失败: ${errorCount}`)
    } else {
      message.error(`文件 ${file.name} 推送失败！`)
    }

  } catch (error) {
    console.error('推送文件失败:', error)
    message.error(`推送文件失败: ${error.message}`)
  }
}

// 批量推送到选中设备
const handleBatchPush = async () => {
  if (selectedDevices.value.length === 0) {
    message.error('请先选择要推送的设备')
    return
  }

  if (fileList.value.length === 0) {
    message.error('没有可推送的文件')
    return
  }

  isPushing.value = true
  pushProgress.value = {
    show: true,
    percentage: 0,
    status: 'active',
    text: '准备推送...'
  }

  try {
    const totalTasks = selectedDevices.value.length * fileList.value.length
    let completedTasks = 0
    let successCount = 0
    let errorCount = 0

    message.info(`开始推送 ${fileList.value.length} 个文件到 ${selectedDevices.value.length} 个设备`)

    // 遍历选中的设备
    for (const deviceKey of selectedDevices.value) {
      const device = connectedDevices.value.find(d => getDeviceKey(d) === deviceKey)
      if (!device || !device.connected) {
        console.warn(`设备 ${deviceKey} 未连接，跳过`)
        completedTasks += fileList.value.length
        errorCount += fileList.value.length

        // 更新进度
        pushProgress.value.percentage = Math.round((completedTasks / totalTasks) * 100)
        pushProgress.value.text = `设备 ${deviceKey} 未连接，跳过所有文件`
        continue
      }

      // 遍历文件列表
      for (const file of fileList.value) {
        try {
          pushProgress.value.text = `推送 ${file.name} 到 ${device.name || device.ip}...`

          await pushFileToDevice(file, device)
          successCount++
          console.log(`✅ ${file.name} → ${device.ip} 成功`)
        } catch (error) {
          console.error(`❌ ${file.name} → ${device.ip} 失败:`, error)
          errorCount++
        }

        completedTasks++
        pushProgress.value.percentage = Math.round((completedTasks / totalTasks) * 100)
      }
    }

    // 完成推送
    pushProgress.value.status = successCount > 0 ? 'success' : 'error'
    pushProgress.value.text = `推送完成！成功: ${successCount}, 失败: ${errorCount}`

    if (successCount > 0) {
      message.success(`推送完成！成功: ${successCount}, 失败: ${errorCount}`)
    } else {
      message.error(`推送失败！所有文件推送都失败了`)
    }

  } catch (error) {
    console.error('批量推送失败:', error)
    message.error(`批量推送失败: ${error.message}`)
    pushProgress.value.status = 'error'
    pushProgress.value.text = `推送失败: ${error.message}`
  } finally {
    isPushing.value = false

    // 3秒后隐藏进度条
    setTimeout(() => {
      pushProgress.value.show = false
    }, 3000)
  }
}

// 推送单个文件到设备
const pushFileToDevice = async (file, device) => {
  return new Promise((resolve, reject) => {
    try {
      // 检查文件对象
      if (!file.file) {
        reject(new Error('文件对象不存在，无法推送'))
        return
      }

      // 创建FormData
      const formData = new FormData()

      // 确保文件名正确编码
      const encodedFileName = encodeURIComponent(file.name)
      console.log(`原始文件名: ${file.name}`)
      console.log(`编码后文件名: ${encodedFileName}`)

      // 创建一个新的File对象，确保文件名正确
      const newFile = new File([file.file], encodedFileName, {
        type: file.file.type,
        lastModified: file.file.lastModified
      })

      formData.append('file', newFile)
      formData.append('deviceId', device.id || '')
      formData.append('timestamp', Date.now())

      // 发送到设备服务器
      const uploadUrl = `http://${device.ip}:9004/upload`

      console.log(`开始推送文件 ${file.name} 到 ${uploadUrl}`)

      fetch(uploadUrl, {
        method: 'POST',
        body: formData
      })
      .then(response => {
        if (response.ok) {
          console.log(`文件 ${file.name} 推送到 ${device.ip} 成功`)
          resolve()
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      })
      .catch(error => {
        console.error(`推送文件 ${file.name} 到 ${device.ip} 失败:`, error)
        reject(error)
      })

    } catch (error) {
      reject(error)
    }
  })
}

const handleClearAll = () => {
  // 释放所有文件URL
  fileList.value.forEach(file => {
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
  })

  fileList.value = []
  selectedDevices.value = []
  message.info('已清空文件列表')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取设备选择选项
const getDeviceOptions = () => {
  const options = []

  // 添加全部设备选项
  if (connectedDevices.value.length > 1) {
    options.push({
      label: `推送到所有设备 (${connectedDevices.value.length}个)`,
      key: 'all',
      icon: () => '📡'
    })

    options.push({
      type: 'divider'
    })
  }

  // 添加单个设备选项
  connectedDevices.value.forEach(device => {
    options.push({
      label: device.name || `${device.ip}:${device.port}`,
      key: `${device.ip}:${device.port}`,
      icon: () => device.connected ? '🟢' : '🔴'
    })
  })

  return options
}

// 移除文件
const removeFile = (file) => {
  const index = fileList.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    // 释放文件URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
    fileList.value.splice(index, 1)
    message.info(`已移除文件 ${file.name}`)
  }
}



// 生命周期
onMounted(() => {
  // 自动选择第一个连接的设备
  if (connectedDevices.value.length > 0) {
    const firstDevice = connectedDevices.value[0]
    selectedDevices.value = [getDeviceKey(firstDevice)]
  }
})
</script>

<style scoped>
/* 文件上传页面样式 */
.upload-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f7;
}

.action-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn-primary:hover {
  background: #0056cc;
}

/* 设备选择 */
.device-selection {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 16px 0;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.device-checkbox {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-checkbox:hover {
  background: #f5f5f7;
}

.device-checkbox-checked {
  border-color: #007aff;
  background: #f0f8ff;
}

.checkbox-input {
  margin-right: 12px;
  width: 16px;
  height: 16px;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  font-weight: 500;
  color: #1d1d1f;
  margin: 0 0 4px 0;
}

.device-address {
  font-size: 14px;
  color: #86868b;
  font-family: 'SF Mono', Monaco, monospace;
  margin: 0;
}

.no-devices {
  text-align: center;
  padding: 40px;
  color: #86868b;
}

.no-devices p {
  margin: 0 0 16px 0;
  font-size: 16px;
}

/* 文件上传区域 */
.upload-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-area {
  border: 2px dashed #d2d2d7;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #007aff;
  background: #f0f8ff;
}

.upload-area-dragover {
  border-color: #007aff;
  background: #f0f8ff;
}

.upload-icon {
  margin-bottom: 16px;
}

.upload-text {
  font-size: 18px;
  font-weight: 500;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #86868b;
  margin: 0;
}

.file-input {
  display: none;
}

/* 文件列表 */
.files-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.file-card {
  border: 1px solid #e5e5e7;
  border-radius: 12px;
  padding: 16px;
  background: #f9f9f9;
  transition: all 0.2s ease;
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.file-selected {
  border-color: #007aff;
  background: #f0f8ff;
}

.file-checkbox {
  position: absolute;
  top: 12px;
  right: 12px;
}

.file-preview {
  text-align: center;
  margin-bottom: 16px;
}

.file-icon {
  margin-bottom: 12px;
}

.file-info {
  text-align: center;
  margin-bottom: 16px;
}

.file-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 14px;
  color: #86868b;
  margin: 0 0 8px 0;
}

.file-type {
  margin-bottom: 8px;
}

.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-image {
  background: #e1f5fe;
  color: #0277bd;
}

.type-video {
  background: #fff3e0;
  color: #ef6c00;
}

.type-document {
  background: #f3e5f5;
  color: #7b1fa2;
}

.file-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-btn-primary {
  background: #007aff;
  color: white;
}

.file-btn-primary:hover {
  background: #0056cc;
}

.file-btn-success {
  background: #34c759;
  color: white;
}

.file-btn-success:hover {
  background: #28a745;
}

.file-btn-danger {
  background: #ff3b30;
  color: white;
}

.file-btn-danger:hover {
  background: #d70015;
}

/* 批量操作 */
.batch-actions {
  background: #f0f8ff;
  border: 1px solid #007aff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-size: 14px;
  font-weight: 500;
  color: #007aff;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.batch-btn {
  padding: 6px 12px;
  border: 1px solid #007aff;
  background: white;
  color: #007aff;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-btn:hover {
  background: #007aff;
  color: white;
}

.batch-btn-primary {
  background: #007aff;
  color: white;
}

.batch-btn-primary:hover {
  background: #0056cc;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #86868b;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  margin: 0 0 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .device-grid {
    grid-template-columns: 1fr;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .upload-area {
    padding: 20px;
  }
}
</style>
