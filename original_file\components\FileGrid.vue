<template>
  <div class="file-grid">
    <!-- 分类标签 -->
    <div class="category-tabs" v-if="files.length > 0">
      <n-tabs v-model:value="activeCategory" type="line" size="large">
        <n-tab-pane
          v-for="category in categories"
          :key="category.key"
          :name="category.key"
          :tab="category.label"
        >
          <template #tab>
            <n-space align="center" :size="8">
              <n-icon :color="category.color">
                <component :is="category.icon" />
              </n-icon>
              {{ category.label }}
              <n-tag size="small" :type="category.tagType">{{ category.count }}</n-tag>
            </n-space>
          </template>
        </n-tab-pane>
      </n-tabs>
    </div>

    <!-- 文件网格 -->
    <div class="files-container" v-if="files.length > 0">
      <n-grid :cols="24" :x-gap="16" :y-gap="16" responsive="screen">
        <n-grid-item
          v-for="file in filteredFiles"
          :key="file.path"
          :span="24" :sm="12" :md="8" :lg="6" :xl="4" :xxl="3"
        >
        <n-card
          class="file-card"
          hoverable
          @click="handleFileClick(file)"
        >
          <!-- 文件预览区域 -->
          <div class="file-preview">
            <!-- 图片预览 -->
            <div v-if="isImageFile(file)" class="image-preview">
              <img
                :src="getFilePreviewUrl(file)"
                :alt="file.name"
                @load="handleImageLoad"
                @error="handleImageError"
                class="preview-image"
              />
            </div>

            <!-- 视频预览 -->
            <div v-else-if="isVideoFile(file)" class="video-preview">
              <video
                :src="getFilePreviewUrl(file)"
                :poster="getVideoThumbnail(file)"
                preload="metadata"
                muted
                class="preview-video"
                @loadedmetadata="handleVideoLoad"
                @error="handleVideoError"
              >
                您的浏览器不支持视频预览
              </video>
              <div class="video-overlay">
                <n-icon size="24" color="white">
                  <PlayOutline />
                </n-icon>
              </div>
            </div>

            <!-- HTML预览 -->
            <div v-else-if="isHtmlFile(file)" class="html-preview">
              <iframe
                :src="getFilePreviewUrl(file)"
                frameborder="0"
                class="preview-iframe"
                @load="handleIframeLoad"
                @error="handleIframeError"
              ></iframe>
            </div>

            <!-- 文档预览 -->
            <div v-else-if="isDocumentFile(file)" class="document-preview">
              <div class="document-thumbnail">
                <n-icon size="48" :color="getFileIconColor(file.type)">
                  <component :is="getFileIcon(file.type)" />
                </n-icon>
                <div class="document-info">
                  <span class="doc-type">{{ getFileTypeLabel(file.type) }}</span>
                  <span class="doc-pages" v-if="file.pages">{{ file.pages }} 页</span>
                </div>
              </div>
            </div>

            <!-- 默认文件图标 -->
            <div v-else class="file-icon">
              <n-icon size="48" :color="getFileIconColor(file.type)">
                <component :is="getFileIcon(file.type)" />
              </n-icon>
            </div>
          </div>
          
          <!-- 文件信息 -->
          <div class="file-info">
            <h4 class="file-name" :title="file.name">{{ file.name }}</h4>
            <div class="file-meta">
              <span class="file-size">{{ file.size }}</span>
              <span class="file-type">{{ getFileTypeLabel(file.type) }}</span>
            </div>
            <div class="file-date">{{ formatDate(file.lastModified) }}</div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="file-actions">
            <n-space :size="8" vertical>
              <n-button
                type="primary"
                size="small"
                block
                @click.stop="handlePreview(file)"
              >
                <template #icon>
                  <n-icon>
                    <EyeOutline />
                  </n-icon>
                </template>
                预览
              </n-button>
              <n-button
                type="success"
                size="small"
                block
                @click.stop="emit('pushToPlayer', file)"
              >
                <template #icon>
                  <n-icon>
                    <TvOutline />
                  </n-icon>
                </template>
                推送浏览器大屏
              </n-button>
              <n-button
                type="info"
                size="small"
                block
                @click.stop="emit('openLocal', file)"
              >
                <template #icon>
                  <n-icon>
                    <PlayOutline />
                  </n-icon>
                </template>
                推送到本地播放
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>
    </div>

    <!-- 空状态 -->
    <n-empty v-else description="此设备暂无文件">
      <template #icon>
        <n-icon size="48" color="#909090">
          <FolderOutline />
        </n-icon>
      </template>
    </n-empty>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  VideocamOutline,
  ImageOutline,
  DocumentOutline,
  FolderOutline,
  ArchiveOutline,
  CodeOutline,
  PlayOutline,
  EyeOutline,
  TvOutline,
  MusicalNotesOutline,
  DocumentTextOutline,
  GridOutline
} from '@vicons/ionicons5'

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  device: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['openLocal', 'pushToPlayer', 'play', 'preview'])

// 响应式数据
const activeCategory = ref('all')

// 文件分类定义
const categories = computed(() => {
  const filesByType = props.files.reduce((acc, file) => {
    const type = file.type || 'other'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {})

  return [
    {
      key: 'all',
      label: '全部',
      icon: GridOutline,
      color: '#007AFF',
      tagType: 'info',
      count: props.files.length
    },
    {
      key: 'video',
      label: '视频',
      icon: VideocamOutline,
      color: '#FF3B30',
      tagType: 'error',
      count: filesByType.video || 0
    },
    {
      key: 'image',
      label: '图片',
      icon: ImageOutline,
      color: '#34C759',
      tagType: 'success',
      count: filesByType.image || 0
    },
    {
      key: 'audio',
      label: '音频',
      icon: MusicalNotesOutline,
      color: '#FF9500',
      tagType: 'warning',
      count: filesByType.audio || 0
    },
    {
      key: 'document',
      label: '文档',
      icon: DocumentTextOutline,
      color: '#9C88FF',
      tagType: 'info',
      count: (filesByType.document || 0) + (filesByType.pdf || 0) + (filesByType.text || 0)
    },
    {
      key: 'other',
      label: '其他',
      icon: FolderOutline,
      color: '#909090',
      tagType: 'default',
      count: filesByType.other || 0
    }
  ].filter(category => category.key === 'all' || category.count > 0)
})

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (activeCategory.value === 'all') {
    return props.files
  }

  return props.files.filter(file => {
    const type = file.type || 'other'
    if (activeCategory.value === 'document') {
      return ['document', 'pdf', 'text'].includes(type)
    }
    return type === activeCategory.value
  })
})

// 获取文件图标
const getFileIcon = (type) => {
  const iconMap = {
    video: VideocamOutline,
    image: ImageOutline,
    document: DocumentOutline,
    archive: ArchiveOutline,
    code: CodeOutline
  }
  return iconMap[type] || DocumentOutline
}

// 获取文件图标颜色
const getFileIconColor = (type) => {
  const colorMap = {
    video: '#FF3B30',
    image: '#34C759',
    document: '#007AFF',
    archive: '#FF9500',
    code: '#9C88FF'
  }
  return colorMap[type] || '#86868B'
}

// 获取文件类型标签
const getFileTypeLabel = (type) => {
  const labelMap = {
    video: '视频',
    image: '图片',
    document: '文档',
    archive: '压缩包',
    code: '代码'
  }
  return labelMap[type] || '文件'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 文件类型判断方法
const isImageFile = (file) => {
  const imageTypes = ['image', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
  return imageTypes.includes(file.type) || imageTypes.includes(getFileExtension(file.name))
}

const isVideoFile = (file) => {
  const videoTypes = ['video', 'mp4', 'webm', 'ogg', 'avi', 'mov']
  return videoTypes.includes(file.type) || videoTypes.includes(getFileExtension(file.name))
}

const isHtmlFile = (file) => {
  const htmlTypes = ['html', 'htm']
  return htmlTypes.includes(file.type) || htmlTypes.includes(getFileExtension(file.name))
}

const isDocumentFile = (file) => {
  const docTypes = ['document', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']
  return docTypes.includes(file.type) || docTypes.includes(getFileExtension(file.name))
}

const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase()
}

// 获取文件预览URL
const getFilePreviewUrl = (file) => {
  if (!props.device) return ''

  // 构建预览URL - 直接使用shared路径
  const baseUrl = `http://${props.device.ip}:9004`
  // 从完整路径中提取文件名，避免双重编码
  let fileName = file.name || file.path.split('/').pop() || file.path.split('\\').pop()

  // 如果文件名已经是编码的，先解码再重新编码
  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，说明不是编码的文件名，直接使用
  }

  return `${baseUrl}/shared/${encodeURIComponent(fileName)}`
}

// 获取视频缩略图
const getVideoThumbnail = (file) => {
  if (!props.device) return ''

  const baseUrl = `http://${props.device.ip}:9004`
  // 从完整路径中提取文件名，避免双重编码
  let fileName = file.name || file.path.split('/').pop() || file.path.split('\\').pop()

  // 如果文件名已经是编码的，先解码再重新编码
  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，说明不是编码的文件名，直接使用
  }

  return `${baseUrl}/thumbnail/${encodeURIComponent(fileName)}`
}

// 事件处理方法
const handleFileClick = (file) => {
  emit('play', file)
}

const handlePreview = (file) => {
  emit('preview', file)
}

const handleImageLoad = () => {
  console.log('图片加载成功')
}

const handleImageError = () => {
  console.error('图片加载失败')
}

const handleVideoLoad = () => {
  console.log('视频加载成功')
}

const handleVideoError = () => {
  console.error('视频加载失败')
}

const handleIframeLoad = () => {
  console.log('HTML加载成功')
}

const handleIframeError = () => {
  console.error('HTML加载失败')
}
</script>

