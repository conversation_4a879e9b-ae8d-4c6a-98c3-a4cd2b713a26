<template>
  <!-- 仅在文件信息有效时渲染 -->
  <div v-if="isFileValid" class="file-item">
    <n-card class="file-card" hoverable>
      <div class="file-name">{{ file.name }}</div>
      <div class="file-preview">
        <!-- 视频预览 -->
        <video
          v-if="type === 'video'"
          :src="file.path"
          controls
          class="preview-media"
          @error="handleMediaError"
        />

        <!-- 图片预览 -->
        <img
          v-else-if="type === 'image'"
          :src="file.path"
          class="preview-media"
          :alt="file.name"
          @error="handleMediaError"
        />

        <!-- 文件图标 -->
        <div v-else class="file-icon">
          <n-icon size="48" :color="getFileIconColor(type)">
            <component :is="getFileIcon(type)" />
          </n-icon>
          <span class="file-type-label">{{ getFileTypeLabel(type) }}</span>
        </div>
      </div>

      <!-- 按钮组 -->
      <div class="button-group">
        <n-button
          type="primary"
          class="push-button"
          @click="pushAndOpenContent"
          :disabled="!isDeviceConnected"
          :loading="isPushing"
          block
        >
          推送到设备并打开
        </n-button>
      </div>
    </n-card>
  </div>

  <!-- 无效文件提示 -->
  <div v-else class="file-item">
    <n-card class="invalid-card">
      <n-alert
        title="无效文件"
        type="warning"
        show-icon
      >
        文件信息不完整或格式错误
      </n-alert>
    </n-card>
  </div>
</template>

<script setup>
// 删除重复的导入
// import { ref, onMounted } from 'vue'

// 设备引用
// const deviceRef = ref({
//   sharedPath: null,
//   isConnected: false
// })

// 组件挂载时
// onMounted(() => {
//   // 发送获取共享路径的命令
//   sendGetSharedPathCommand()
// })

// 发送获取共享路径命令
// function sendGetSharedPathCommand() {
//   // 假设这里有WebSocket连接实例
//   if (ws && ws.readyState === WebSocket.OPEN) {
//     ws.send(JSON.stringify({
//       type: 'getSharedPath'
//     }))
//   }

//   // 监听共享路径响应
//   ws.addEventListener('message', (event) => {
//     try {
//       const data = JSON.parse(event.data)
//       if (data.type === 'sharedPathResponse') {
//         // 保存共享路径
//         deviceRef.value.sharedPath = data.sharedPath
//         deviceRef.value.isConnected = true
//         console.log(`获取共享路径成功：${data.sharedPath}`)
//       }
//     } catch (e) {
//       console.error('解析共享路径响应失败:', e)
//     }
//   })
// }
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useWebSocket } from '@/composables/useWebSocket'
import {
  DocumentOutline,
  VideocamOutline,
  ImageOutline,
  CodeOutline,
  DocumentTextOutline,
  ArchiveOutline
} from '@vicons/ionicons5'

const message = useMessage()

// 文件图标映射
const getFileIcon = (type) => {
  const iconMap = {
    video: VideocamOutline,
    image: ImageOutline,
    html: CodeOutline,
    ppt: DocumentTextOutline,
    word: DocumentTextOutline,
    pdf: DocumentOutline,
    excel: DocumentTextOutline,
    text: DocumentOutline
  }
  return iconMap[type] || DocumentOutline
}

// 文件图标颜色
const getFileIconColor = (type) => {
  const colorMap = {
    video: '#FF3B30',
    image: '#34C759',
    html: '#FF9500',
    ppt: '#FF3B30',
    word: '#007AFF',
    pdf: '#FF3B30',
    excel: '#34C759',
    text: '#86868B'
  }
  return colorMap[type] || '#86868B'
}

// 文件类型标签
const getFileTypeLabel = (type) => {
  const labelMap = {
    video: '视频文件',
    image: '图片文件',
    html: 'HTML文件',
    ppt: 'PPT文件',
    word: 'Word文件',
    pdf: 'PDF文件',
    excel: 'Excel文件',
    text: '文本文件'
  }
  return labelMap[type] || '未知文件'
}

// 推送状态
const isPushing = ref(false)

// 接收父组件传递的参数
const props = defineProps({
  file: {
    type: Object,
    required: true,
    default: () => ({}) // 避免 undefined 导致的错误
  },
  device: {
    type: Object,
    required: true,
    default: () => ({})
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['video', 'image', 'html', 'ppt', 'text', 'word', 'pdf', 'excel'].includes(value)
  }
});

// 验证文件有效性（避免因文件信息缺失导致的渲染错误）
const isFileValid = computed(() => {
  return (
    typeof props.file === 'object' &&
    props.file !== null &&
    typeof props.file.name === 'string' &&
    props.file.name.trim() !== '' &&
    typeof props.file.path === 'string' &&
    props.file.path.trim() !== ''
  );
});

// 设备连接状态（从 device 对象或 WebSocket 状态获取）
const isDeviceConnected = computed(() => {
  // 优先从 device 对象获取连接状态，若不存在则从 WebSocket 状态判断
  return props.device.connected ?? (ws.value?.readyState === WebSocket.OPEN);
});

// 初始化 WebSocket（传入设备引用）
const deviceRef = ref(props.device);
const { sendCommand, ws, isConnected } = useWebSocket(deviceRef, (event) => {
  // 处理WebSocket消息
  try {
    const msg = JSON.parse(event.data);
    console.log('📥 收到WebSocket消息:', msg);
    
    if (msg.type === 'pushContentResponse') {
      console.log('📤 收到推送内容响应:', msg);
      
      // 检查响应状态
      if (msg.success) {
        message.success('推送成功');
        console.log('✅ 推送内容成功:', msg.content);
      } else {
        message.error(`推送失败: ${msg.error || '未知错误'}`);
        console.error('❌ 推送内容失败:', msg.error);
      }
      
      // 清除超时计时器
      if (pushTimeout.value) {
        clearTimeout(pushTimeout.value);
        pushTimeout.value = null;
        console.log('✅ 已清除超时计时器');
      }
    }
    
    // 保留打开本地应用响应处理代码
    if (msg.type === 'openWithLocalAppResponse') {
      console.log('📤 收到打开本地应用响应:', msg);
      
      if (msg.success) {
        message.success('成功打开本地应用');
        console.log('✅ 成功打开本地应用');
      } else {
        message.error(`打开本地应用失败: ${msg.error || '未知错误'}`);
        console.error('❌ 打开本地应用失败:', msg.error);
      }
    }
  } catch (e) {
    console.error('❌ 消息解析失败:', e);
  }
});

// 定义推送超时变量
const pushTimeout = ref(null);

// 推送文件到设备并打开本地应用
const pushAndOpenContent = () => {
  if (!isFileValid.value) {
    message.warning('无法推送无效文件');
    console.warn('无法推送无效文件');
    return;
  }

  if (!isDeviceConnected.value) {
    message.warning('设备未连接，无法推送');
    console.warn('设备未连接，无法推送');
    return;
  }

  isPushing.value = true;
  
  // 1. 推送内容
  const pushCommand = {
    type: 'pushContent',
    content: {
      type: props.type,
      path: props.file.path,
      deviceId: `${props.device.ip}:${props.device.port}`
    }
  };
  
  console.log('准备推送内容:', pushCommand);
  const pushSuccess = sendCommand(pushCommand);
  if (pushSuccess) {
    message.info('推送命令已发送，等待服务器响应...');
    console.log('推送命令发送成功，等待服务器响应...');

    // 清除之前可能存在的超时
    if (pushTimeout.value) {
      clearTimeout(pushTimeout.value);
      pushTimeout.value = null;
    }

    // 添加超时检测
    const timeout = setTimeout(() => {
      message.warning('推送超时，请重试');
      console.warn('推送命令超时，未收到服务器响应');
      pushTimeout.value = null;
      isPushing.value = false;
    }, 5000); // 5秒超时

    // 存储超时ID，以便在收到响应时清除
    pushTimeout.value = timeout;
  } else {
    message.error('推送命令发送失败，请检查连接');
    console.error('推送命令发送失败');
    isPushing.value = false;
  }
  
  // 2. 打开本地应用
  try {
    // 从props.file.path中提取IP地址和端口号
    const url = new URL(props.file.path);
    const host = url.hostname;
    const port = url.port; // 自动获取端口号
    
    if (props.file.path.startsWith(`http://${host}:${port}/shared/`)) {
      const fileName = decodeURIComponent(props.file.path.replace(`http://${host}:${port}/shared/`, ''))
      // 使用字符串拼接
      const localFilePath = (deviceRef.value.sharedPath || './shared') + '\\' + fileName;
      
      // 定义获取应用类型的函数
      function getAppType(fileType) {
        switch(fileType) {
          case 'video':
            return 'mediaPlayer';
          case 'image':
            return 'imageViewer';
          case 'html':
            return 'browser';
          case 'ppt':
          case 'word':
          case 'excel':
          case 'pdf':
            return 'office';
          default:
            return 'default';
        }
      }
      
      const openCommand = {
        type: 'openWithLocalApp',
        filePath: localFilePath,
        appType: getAppType(props.file.type)
      }
      
      console.log('准备打开本地应用:', openCommand);
      const openSuccess = sendCommand(openCommand);
      if (openSuccess) {
        message.info('打开本地应用命令已发送...');
        console.log('打开本地应用命令发送成功');
      } else {
        message.error('打开本地应用命令发送失败，请检查连接');
        console.error('打开本地应用命令发送失败');
      }
    }
  } catch (error) {
    console.error('构建本地路径失败:', error);
    message.error('无法解析文件路径，请检查文件信息');
  }

  isPushing.value = false;
};

// 处理媒体预览错误
const handleMediaError = (e) => {
  console.error(`[${props.type}] 预览失败:`, e);
  // 可替换为错误占位图
  if (props.type === 'image') {
    e.target.src = 'https://via.placeholder.com/150?text=图片加载失败';
  }
};

// 调试信息
onMounted(() => {
  if (!isFileValid.value) {
    console.warn('无效文件数据:', props.file);
  }
});

// 组件卸载时清除计时器
onUnmounted(() => {
  if (pushTimeout.value) {
    clearTimeout(pushTimeout.value);
    pushTimeout.value = null;
  }
});
</script>

<style scoped>
.file-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border-radius: 16px;
  border: 1px solid #E5E5E7;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
  color: #1D1D1F;
}

.file-name {
  font-weight: bold;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
}

.file-preview {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  min-height: 140px;
  background: #F5F5F7;
  border: 1px dashed #E5E5E7;
  border-radius: 8px;
  transition: background 0.3s;
}

.preview-media {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: 4px;
}

.file-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #007AFF;
  font-size: 20px;
  animation: floatIcon 2s ease-in-out infinite;
}

@keyframes floatIcon {
  0% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
  100% { transform: translateY(0); }
}

.push-button {
  width: 100%;
  background: linear-gradient(to right, #00c6ff, #0072ff);
  border: none;
  color: white;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(0, 234, 255, 0.4);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.push-button:hover {
  transform: scale(1.03);
  box-shadow: 0 0 14px rgba(0, 234, 255, 0.6);
}

.invalid-card {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F5F7;
  border-radius: 12px;
  border: 1px solid rgba(255, 59, 48, 0.3);
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.1);
}

.button-group {
  display: flex;
  gap: 10px;
}
</style>