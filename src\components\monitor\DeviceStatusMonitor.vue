<template>
  <div class="device-status-monitor">
    <!-- 设备列表 -->
    <n-data-table
      :columns="columns"
      :data="devices"
      :pagination="pagination"
      :bordered="false"
      :single-line="false"
      striped
    />
  </div>
</template>

<script setup>
import { ref, h, computed } from 'vue'
import { NTag, NIcon, NButton, NSpace, NProgress } from 'naive-ui'
import {
  HardwareChipOutline,
  PlayOutline,
  PauseOutline,
  StopOutline,
  AlertCircleOutline,
  CheckmarkCircleOutline,
  WifiOutline
} from '@vicons/ionicons5'

const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  }
})

// 分页配置
const pagination = ref({
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
})

// 表格列配置
const columns = [
  {
    title: '设备信息',
    key: 'device',
    width: 200,
    render(row) {
      return h('div', { class: 'device-info-cell' }, [
        h(NIcon, {
          size: 20,
          color: row.connected ? '#34C759' : '#FF3B30',
          style: { marginRight: '8px' }
        }, { default: () => h(HardwareChipOutline) }),
        h('div', { class: 'device-details' }, [
          h('div', { class: 'device-name' }, row.name || '未命名设备'),
          h('div', { class: 'device-address' }, `${row.ip}:${row.port}`)
        ])
      ])
    }
  },
  {
    title: '连接状态',
    key: 'connected',
    width: 120,
    render(row) {
      return h(NTag, {
        type: row.connected ? 'success' : 'error',
        size: 'small'
      }, {
        icon: () => h(NIcon, {}, { 
          default: () => h(row.connected ? CheckmarkCircleOutline : AlertCircleOutline) 
        }),
        default: () => row.connected ? '在线' : '离线'
      })
    }
  },
  {
    title: '播放状态',
    key: 'status',
    width: 120,
    render(row) {
      const statusMap = {
        playing: { type: 'success', icon: PlayOutline, text: '播放中' },
        paused: { type: 'warning', icon: PauseOutline, text: '已暂停' },
        stopped: { type: 'default', icon: StopOutline, text: '已停止' },
        idle: { type: 'info', icon: StopOutline, text: '空闲' }
      }
      const status = statusMap[row.status] || statusMap.idle
      
      return h(NTag, {
        type: status.type,
        size: 'small'
      }, {
        icon: () => h(NIcon, {}, { default: () => h(status.icon) }),
        default: () => status.text
      })
    }
  },
  {
    title: '当前内容',
    key: 'currentMedia',
    width: 200,
    render(row) {
      if (!row.currentMedia) {
        return h('span', { style: { color: '#999' } }, '无')
      }
      return h('div', { class: 'media-info' }, [
        h('div', { class: 'media-name' }, row.currentMedia.name || '未知'),
        h('div', { class: 'media-type' }, row.currentMedia.type || '未知类型')
      ])
    }
  },
  {
    title: '音量',
    key: 'volume',
    width: 100,
    render(row) {
      const volume = row.volume || 0
      return h('div', { class: 'volume-cell' }, [
        h(NProgress, {
          type: 'line',
          percentage: volume,
          height: 8,
          color: volume > 80 ? '#FF3B30' : volume > 50 ? '#FF9500' : '#34C759',
          railColor: '#f0f0f0'
        }),
        h('span', { 
          class: 'volume-text',
          style: { marginTop: '4px', fontSize: '12px', color: '#666' }
        }, `${volume}%`)
      ])
    }
  },
  {
    title: '网络质量',
    key: 'networkQuality',
    width: 120,
    render(row) {
      const quality = row.networkQuality || 'unknown'
      const qualityMap = {
        excellent: { type: 'success', text: '优秀', color: '#34C759' },
        good: { type: 'info', text: '良好', color: '#007AFF' },
        poor: { type: 'warning', text: '较差', color: '#FF9500' },
        bad: { type: 'error', text: '很差', color: '#FF3B30' },
        unknown: { type: 'default', text: '未知', color: '#999' }
      }
      const q = qualityMap[quality]
      
      return h(NTag, {
        type: q.type,
        size: 'small'
      }, {
        icon: () => h(NIcon, { color: q.color }, { default: () => h(WifiOutline) }),
        default: () => q.text
      })
    }
  },
  {
    title: '最后活动',
    key: 'lastActivity',
    width: 150,
    render(row) {
      if (!row.lastActivity) {
        return h('span', { style: { color: '#999' } }, '无记录')
      }
      
      const time = new Date(row.lastActivity)
      const now = new Date()
      const diff = now - time
      
      let timeText = ''
      if (diff < 60000) {
        timeText = '刚刚'
      } else if (diff < 3600000) {
        timeText = `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        timeText = `${Math.floor(diff / 3600000)}小时前`
      } else {
        timeText = time.toLocaleDateString()
      }
      
      return h('div', { class: 'activity-cell' }, [
        h('div', { class: 'activity-time' }, timeText),
        h('div', { 
          class: 'activity-action',
          style: { fontSize: '12px', color: '#666' }
        }, row.lastAction || '未知操作')
      ])
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h(NSpace, { size: 4 }, {
        default: () => [
          h(NButton, {
            size: 'tiny',
            type: 'primary',
            disabled: !row.connected,
            onClick: () => handleDeviceAction(row, 'refresh')
          }, { default: () => '刷新' }),
          h(NButton, {
            size: 'tiny',
            disabled: !row.connected,
            onClick: () => handleDeviceAction(row, 'restart')
          }, { default: () => '重启' }),
          h(NButton, {
            size: 'tiny',
            type: 'error',
            onClick: () => handleDeviceAction(row, 'remove')
          }, { default: () => '移除' })
        ]
      })
    }
  }
]

// 方法
const handleDeviceAction = (device, action) => {
  console.log(`对设备 ${device.name || device.ip} 执行 ${action} 操作`)
  // 这里可以发送相应的命令到设备
}
</script>
