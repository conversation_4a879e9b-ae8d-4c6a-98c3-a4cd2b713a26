import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/pages/Dashboard.vue'),
    meta: { title: '主页' }
  },
  {
    path: '/devices',
    name: 'DevicesSingle',
    component: () => import('@/pages/DevicesNew.vue'),
    meta: { title: '设备管理 - 单选模式' }
  },
  {
    path: '/devices-multi',
    name: 'DevicesMulti',
    component: () => import('@/pages/DevicesMultiNew.vue'),
    meta: { title: '设备管理 - 多选模式' }
  },
  {
    path: '/contents',
    name: 'Contents',
    component: () => import('@/pages/ContentsNew.vue'),
    meta: { title: '内容管理' }
  },
  {
    path: '/host-view',
    name: 'HostView',
    component: () => import('@/pages/HostView.vue'),
    meta: { title: '主机视角控制' }
  },
  {
    path: '/dashboard-fetch',
    name: 'DashboardFetch',
    component: () => import('@/pages/DashboardFetch.vue'),
    meta: { title: '看板管理' }
  },
  {
    path: '/upload',
    name: 'Upload',
    component: () => import('@/pages/Upload.vue'),
    meta: { title: '文件上传' }
  },
  {
    path: '/monitor',
    name: 'Monitor',
    component: () => import('@/pages/MonitorNew.vue'),
    meta: { title: '系统监控' }
  },
  {
    path: '/control',
    name: 'Control',
    component: () => import('@/pages/ControlNew.vue'),
    meta: { title: '播放控制' }
  }
];

export default createRouter({
  history: createWebHistory(),
  routes
});