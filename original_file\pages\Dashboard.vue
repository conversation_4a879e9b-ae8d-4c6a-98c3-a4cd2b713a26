<template>
  <div class="dashboard">
    <!-- 主标题区域 -->
    <div class="hero-section">
      <h1 class="hero-title">控制中心</h1>
      <p class="hero-subtitle">统一管理您的设备和内容</p>
    </div>

    <!-- 功能导航 -->
    <div class="features-section">
      <div class="features-grid">
        <div class="feature-card" @click="$router.push('/devices')">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">设备管理</h3>
          <p class="feature-description">连接和管理您的设备</p>
        </div>

        <div class="feature-card" @click="$router.push('/contents')">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">内容管理</h3>
          <p class="feature-description">管理和分发内容文件</p>
        </div>

        <div class="feature-card" @click="$router.push('/upload')">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">文件上传</h3>
          <p class="feature-description">上传新的内容文件</p>
        </div>

        <div class="feature-card" @click="$router.push('/control')">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M8,5.14V19.14L19,12.14L8,5.14Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">播放控制</h3>
          <p class="feature-description">控制设备播放状态</p>
        </div>

        <div class="feature-card" @click="$router.push('/monitor')">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M22,21H2V3H4V19H6V10H10V19H12V6H16V19H18V14H22V21Z" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="feature-title">系统监控</h3>
          <p class="feature-description">监控系统运行状态</p>
        </div>
      </div>
    </div>

    <!-- 状态概览 -->
    <div class="status-section">
      <div class="status-grid">
        <div class="status-item">
          <div class="status-number">{{ totalDevices }}</div>
          <div class="status-label">设备总数</div>
        </div>
        <div class="status-item">
          <div class="status-number">{{ onlineDevices }}</div>
          <div class="status-label">在线设备</div>
        </div>
        <div class="status-item">
          <div class="status-number">{{ totalFiles }}</div>
          <div class="status-label">内容文件</div>
        </div>
        <div class="status-item">
          <div class="status-number">{{ playingDevices }}</div>
          <div class="status-label">正在播放</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useDeviceStore } from '@/store/device'

const deviceStore = useDeviceStore()

// 计算属性
const totalDevices = computed(() => deviceStore.devices.length)
const onlineDevices = computed(() => deviceStore.devices.filter(d => d.connected).length)
const totalFiles = computed(() => 12) // 模拟数据
const playingDevices = computed(() => deviceStore.devices.filter(d => d.connected && d.status === 'playing').length)
</script>
