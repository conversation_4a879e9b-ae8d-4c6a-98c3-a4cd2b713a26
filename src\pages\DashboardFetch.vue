<template>
  <div class="dashboard-fetch-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">看板管理</h1>
        <p class="page-description">获取网站HTML看板内容并推送到指定设备</p>
      </div>
      <div class="header-actions">
        <button class="action-btn" @click="refreshDashboards">刷新</button>
        <button class="action-btn action-btn-primary" @click="showAddDashboardModal = true">
          添加看板
        </button>
      </div>
    </div>

    <!-- 网站登录配置 -->
    <div class="login-section">
      <n-card class="login-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" style="margin-right: 8px">
              <LockClosedOutline />
            </n-icon>
            网站登录配置
            <div class="header-actions" style="margin-left: auto;">
              <n-button size="small" @click="showAddConfigModal = true">
                添加配置
              </n-button>
            </div>
          </div>
        </template>

        <!-- 配置选择器 -->
        <div v-if="loginConfigs.length > 0" class="config-selector">
          <n-form-item label="选择配置">
            <n-select
              v-model:value="currentConfigIndex"
              :options="configOptions"
              placeholder="选择一个网站配置"
            />
          </n-form-item>
        </div>

        <!-- 当前配置显示 -->
        <div v-if="loginConfigs.length > 0" class="current-config">
          <n-form :model="currentLoginConfig" label-placement="left" label-width="100px">
            <n-form-item label="配置名称">
              <n-input
                v-model:value="currentLoginConfig.name"
                placeholder="配置名称"
                @blur="saveLoginConfigs"
              />
            </n-form-item>
            <n-form-item label="网站地址">
              <n-input
                v-model:value="currentLoginConfig.baseUrl"
                placeholder="https://example.com"
                @blur="validateUrl"
              />
            </n-form-item>
            <n-form-item label="用户名">
              <n-input
                v-model:value="currentLoginConfig.username"
                placeholder="请输入用户名"
                @blur="saveLoginConfigs"
              />
            </n-form-item>
            <n-form-item label="密码">
              <n-input
                v-model:value="currentLoginConfig.password"
                type="password"
                placeholder="请输入密码"
                show-password-on="click"
                @blur="saveLoginConfigs"
              />
            </n-form-item>
            <n-form-item label="登录页面">
              <n-input
                v-model:value="currentLoginConfig.loginPath"
                placeholder="/login"
                @blur="saveLoginConfigs"
              />
            </n-form-item>
          </n-form>

          <div class="login-actions">
            <n-space>
              <n-button
                type="primary"
                @click="testLogin"
                :loading="loginTesting"
              >
                测试登录
              </n-button>
              <n-button @click="saveLoginConfigs">保存配置</n-button>
              <n-button
                type="error"
                @click="deleteCurrentConfig"
                :disabled="loginConfigs.length <= 1"
              >
                删除配置
              </n-button>
            </n-space>
          </div>

          <div v-if="loginStatus" class="login-status">
            <n-alert
              :type="loginStatus.type"
              :title="loginStatus.title"
              :show-icon="true"
            >
              {{ loginStatus.message }}
            </n-alert>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-config">
          <n-empty description="暂无登录配置">
            <template #extra>
              <n-button type="primary" @click="showAddConfigModal = true">
                添加第一个配置
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>
    </div>

    <!-- 看板列表 -->
    <div class="dashboards-section">
      <n-card class="dashboards-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" style="margin-right: 8px">
              <GridOutline />
            </n-icon>
            看板列表
          </div>
        </template>

        <div v-if="dashboards.length > 0" class="dashboards-grid">
          <div
            v-for="dashboard in dashboards"
            :key="dashboard.id"
            class="dashboard-card"
            :class="{ 'dashboard-selected': selectedDashboards.includes(dashboard.id) }"
          >
            <!-- 选择框 -->
            <div class="dashboard-checkbox">
              <input 
                type="checkbox" 
                :checked="selectedDashboards.includes(dashboard.id)"
                @change="toggleDashboardSelection(dashboard.id)"
                class="checkbox-input"
              />
            </div>

            <!-- 看板预览 -->
            <div class="dashboard-preview">
              <div v-if="dashboard.thumbnail" class="dashboard-thumbnail">
                <img :src="dashboard.thumbnail" :alt="dashboard.name" />
              </div>
              <div v-else class="dashboard-placeholder">
                <n-icon size="48" color="#86868b">
                  <GridOutline />
                </n-icon>
              </div>
            </div>

            <!-- 看板信息 -->
            <div class="dashboard-info">
              <h3 class="dashboard-name">{{ dashboard.name }}</h3>
              <p class="dashboard-url">{{ dashboard.url }}</p>
              <div class="dashboard-meta">
                <span class="meta-item">
                  <n-icon size="14"><TimeOutline /></n-icon>
                  {{ formatTime(dashboard.lastFetched) }}
                </span>
                <span class="meta-item" :class="`status-${dashboard.status}`">
                  <n-icon size="14"><CheckmarkCircleOutline v-if="dashboard.status === 'success'" /><CloseCircleOutline v-else /></n-icon>
                  {{ dashboard.status === 'success' ? '正常' : '异常' }}
                </span>
              </div>
            </div>

            <!-- 看板操作 -->
            <div class="dashboard-actions">
              <button class="dashboard-btn dashboard-btn-primary" @click="fetchDashboard(dashboard)">
                获取内容
              </button>
              <button class="dashboard-btn dashboard-btn-success" @click="previewDashboard(dashboard)">
                预览
              </button>
              <button class="dashboard-btn dashboard-btn-info" @click="pushDashboard(dashboard)">
                推送
              </button>
              <button class="dashboard-btn dashboard-btn-danger" @click="deleteDashboard(dashboard.id)">
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">
            <n-icon size="64" color="#86868b">
              <GridOutline />
            </n-icon>
          </div>
          <h3 class="empty-title">暂无看板</h3>
          <p class="empty-description">点击"添加看板"开始配置您的第一个看板</p>
          <button class="action-btn action-btn-primary" @click="showAddDashboardModal = true">
            添加看板
          </button>
        </div>
      </n-card>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedDashboards.length > 0" class="batch-actions">
      <div class="batch-content">
        <div class="batch-info">
          <span class="batch-count">已选择 {{ selectedDashboards.length }} 个看板</span>
        </div>
        <div class="batch-buttons">
          <button class="action-btn" @click="batchFetch">批量获取</button>
          <button class="action-btn" @click="batchPush">批量推送</button>
          <button class="action-btn action-btn-danger" @click="batchDelete">批量删除</button>
          <button class="action-btn" @click="clearSelection">清除选择</button>
        </div>
      </div>
    </div>

    <!-- 设备推送区域 -->
    <div v-if="showPushSection" class="push-section">
      <n-card class="push-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" style="margin-right: 8px">
              <SendOutline />
            </n-icon>
            推送到设备
          </div>
        </template>

        <div v-if="connectedDevices.length > 0">
          <!-- 设备列表 -->
          <div class="device-list">
            <div
              v-for="device in connectedDevices"
              :key="`${device.ip}:${device.port}`"
              class="device-item"
              :class="{ 'device-selected': selectedDevices.includes(getDeviceKey(device)) }"
              @click="toggleDeviceSelection(device)"
            >
              <div class="device-info">
                <n-icon :color="device.connected ? '#00E6B8' : '#FF6B6B'">
                  <HardwareChipOutline />
                </n-icon>
                <span>{{ device.name || `${device.ip}:${device.port}` }}</span>
              </div>
              <input 
                type="checkbox" 
                :checked="selectedDevices.includes(getDeviceKey(device))"
                @click.stop
                @change="updateDeviceSelection(device, $event.target.checked)"
              />
            </div>
          </div>

          <!-- 推送操作 -->
          <div class="push-actions">
            <n-space>
              <n-button type="primary" @click="handlePushToDevices" :disabled="selectedDevices.length === 0">
                推送到选中设备 ({{ selectedDevices.length }})
              </n-button>
              <n-button @click="closePushSection">取消</n-button>
            </n-space>
          </div>
        </div>

        <div v-else class="no-devices">
          <n-empty description="没有连接的设备">
            <template #extra>
              <n-button @click="$router.push('/devices')">
                前往设备管理
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>
    </div>

    <!-- 添加看板模态框 -->
    <n-modal v-model:show="showAddDashboardModal" preset="dialog" title="添加看板">
      <template #header>
        <div>添加看板</div>
      </template>
      <div class="add-dashboard-form">
        <n-form :model="newDashboard" label-placement="left" label-width="80px">
          <n-form-item label="看板名称">
            <n-input v-model:value="newDashboard.name" placeholder="请输入看板名称" />
          </n-form-item>
          <n-form-item label="看板URL">
            <n-input v-model:value="newDashboard.url" placeholder="相对于网站根目录的路径，如 /dashboard/1" />
          </n-form-item>
          <n-form-item label="描述">
            <n-input
              v-model:value="newDashboard.description"
              type="textarea"
              placeholder="看板描述（可选）"
              :rows="3"
            />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showAddDashboardModal = false">取消</n-button>
          <n-button type="primary" @click="addDashboard">添加</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 添加登录配置模态框 -->
    <n-modal v-model:show="showAddConfigModal" preset="dialog" title="添加登录配置">
      <template #header>
        <div>添加登录配置</div>
      </template>
      <div class="add-config-form">
        <n-form :model="newLoginConfig" label-placement="left" label-width="80px">
          <n-form-item label="配置名称">
            <n-input v-model:value="newLoginConfig.name" placeholder="如：SCADA系统" />
          </n-form-item>
          <n-form-item label="网站地址">
            <n-input v-model:value="newLoginConfig.baseUrl" placeholder="https://example.com" />
          </n-form-item>
          <n-form-item label="用户名">
            <n-input v-model:value="newLoginConfig.username" placeholder="请输入用户名" />
          </n-form-item>
          <n-form-item label="密码">
            <n-input
              v-model:value="newLoginConfig.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
            />
          </n-form-item>
          <n-form-item label="登录页面">
            <n-input v-model:value="newLoginConfig.loginPath" placeholder="/login" />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showAddConfigModal = false">取消</n-button>
          <n-button type="primary" @click="addLoginConfig">添加</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { useDeviceStore } from '@/store/device'
import {
  LockClosedOutline,
  GridOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  SendOutline,
  HardwareChipOutline
} from '@vicons/ionicons5'

const message = useMessage()
const deviceStore = useDeviceStore()

// 响应式数据
const loginConfigs = ref([])
const currentConfigIndex = ref(0)
const showAddConfigModal = ref(false)
const loginTesting = ref(false)
const loginStatus = ref(null)

const newLoginConfig = ref({
  name: '',
  baseUrl: '',
  username: '',
  password: '',
  loginPath: '/login'
})

// 计算当前选中的登录配置
const currentLoginConfig = computed(() => {
  return loginConfigs.value[currentConfigIndex.value] || {
    name: '',
    baseUrl: '',
    username: '',
    password: '',
    loginPath: '/login'
  }
})
const dashboards = ref([])
const selectedDashboards = ref([])
const selectedDevices = ref([])
const showPushSection = ref(false)
const showAddDashboardModal = ref(false)
const currentPushDashboard = ref(null)

const newDashboard = ref({
  name: '',
  url: '',
  description: ''
})

// 最大看板数量
const MAX_DASHBOARDS = 50

// 计算属性
const connectedDevices = computed(() =>
  deviceStore.devices.filter(device => device.connected)
)

const configOptions = computed(() =>
  loginConfigs.value.map((config, index) => ({
    label: config.name || `配置 ${index + 1}`,
    value: index
  }))
)

// 工具方法
const getDeviceKey = (device) => `${device.ip}:${device.port}`

const formatTime = (timestamp) => {
  if (!timestamp) return '从未获取'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 方法
const validateUrl = () => {
  const url = currentLoginConfig.value.baseUrl
  if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
    currentLoginConfig.value.baseUrl = 'https://' + url
    saveLoginConfigs()
  }
}

const testLogin = async () => {
  if (!currentLoginConfig.value.baseUrl || !currentLoginConfig.value.username || !currentLoginConfig.value.password) {
    message.error('请填写完整的登录信息')
    return
  }

  loginTesting.value = true
  loginStatus.value = null

  try {
    // 模拟登录测试
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 这里应该实现真实的登录逻辑
    // 例如发送登录请求到目标网站

    loginStatus.value = {
      type: 'success',
      title: '登录成功',
      message: '网站登录配置有效，可以正常获取看板内容'
    }

    message.success('登录测试成功')
  } catch (error) {
    loginStatus.value = {
      type: 'error',
      title: '登录失败',
      message: error.message || '无法连接到目标网站或登录信息错误'
    }

    message.error('登录测试失败')
  } finally {
    loginTesting.value = false
  }
}

const saveLoginConfigs = () => {
  try {
    localStorage.setItem('dashboardLoginConfigs', JSON.stringify(loginConfigs.value))
    message.success('登录配置已保存')
  } catch (error) {
    message.error('保存配置失败')
  }
}

const addLoginConfig = () => {
  if (!newLoginConfig.value.name || !newLoginConfig.value.baseUrl) {
    message.error('请填写配置名称和网站地址')
    return
  }

  loginConfigs.value.push({ ...newLoginConfig.value })
  saveLoginConfigs()

  // 重置表单
  newLoginConfig.value = {
    name: '',
    baseUrl: '',
    username: '',
    password: '',
    loginPath: '/login'
  }

  showAddConfigModal.value = false
  message.success('配置已添加')
}

const deleteCurrentConfig = () => {
  if (loginConfigs.value.length <= 1) {
    message.error('至少需要保留一个配置')
    return
  }

  loginConfigs.value.splice(currentConfigIndex.value, 1)

  // 调整当前选中的索引
  if (currentConfigIndex.value >= loginConfigs.value.length) {
    currentConfigIndex.value = loginConfigs.value.length - 1
  }

  saveLoginConfigs()
  message.success('配置已删除')
}

const refreshDashboards = () => {
  loadDashboards()
  message.info('看板列表已刷新')
}

const toggleDashboardSelection = (id) => {
  const index = selectedDashboards.value.indexOf(id)
  if (index > -1) {
    selectedDashboards.value.splice(index, 1)
  } else {
    selectedDashboards.value.push(id)
  }
}

const clearSelection = () => {
  selectedDashboards.value = []
}

const fetchDashboard = async (dashboard) => {
  if (!currentLoginConfig.value.baseUrl) {
    message.error('请先配置网站登录信息')
    return
  }

  if (connectedDevices.value.length === 0) {
    message.error('没有连接的设备')
    return
  }

  try {
    message.info(`正在获取看板 ${dashboard.name} 的内容...`)

    // 选择第一个连接的设备发送获取命令
    const device = connectedDevices.value[0]

    const fetchCommand = {
      type: 'fetchDashboard',
      loginConfig: currentLoginConfig.value,
      dashboard: dashboard
    }

    if (device.ws && device.ws.readyState === WebSocket.OPEN) {
      device.ws.send(JSON.stringify(fetchCommand))
      console.log('已发送看板获取命令:', fetchCommand)

      // 监听获取结果
      const handleFetchResponse = (event) => {
        try {
          const response = JSON.parse(event.data)

          if (response.type === 'fetchDashboardResponse') {
            if (response.success) {
              console.log(`看板 ${dashboard.name} 获取成功`)

              // 更新看板状态
              const dashboardIndex = dashboards.value.findIndex(d => d.id === dashboard.id)
              if (dashboardIndex > -1) {
                dashboards.value[dashboardIndex].lastFetched = Date.now()
                dashboards.value[dashboardIndex].status = 'success'
                dashboards.value[dashboardIndex].fileName = response.fileName
                saveDashboards()
              }

              message.success(`看板 ${dashboard.name} 内容获取成功`)
            } else {
              console.error('获取看板失败:', response.message)

              // 更新错误状态
              const dashboardIndex = dashboards.value.findIndex(d => d.id === dashboard.id)
              if (dashboardIndex > -1) {
                dashboards.value[dashboardIndex].status = 'error'
                saveDashboards()
              }

              message.error(`获取看板失败: ${response.message}`)
            }
            device.ws.removeEventListener('message', handleFetchResponse)
          } else if (response.type === 'error') {
            console.error('获取看板错误:', response.message)
            message.error(`获取看板失败: ${response.message}`)
            device.ws.removeEventListener('message', handleFetchResponse)
          }
        } catch (error) {
          console.error('解析获取响应失败:', error)
        }
      }

      device.ws.addEventListener('message', handleFetchResponse)

      // 设置超时清理
      setTimeout(() => {
        device.ws.removeEventListener('message', handleFetchResponse)
      }, 30000) // 30秒超时

    } else {
      message.error('设备连接已断开')
    }

  } catch (error) {
    message.error(`获取看板内容失败: ${error.message}`)

    // 更新错误状态
    const dashboardIndex = dashboards.value.findIndex(d => d.id === dashboard.id)
    if (dashboardIndex > -1) {
      dashboards.value[dashboardIndex].status = 'error'
      saveDashboards()
    }
  }
}

const previewDashboard = (dashboard) => {
  if (!currentLoginConfig.value.baseUrl) {
    message.error('请先配置网站登录信息')
    return
  }

  const fullUrl = currentLoginConfig.value.baseUrl + dashboard.url
  window.open(fullUrl, '_blank')
}

const pushDashboard = (dashboard) => {
  if (connectedDevices.value.length === 0) {
    message.error('没有连接的设备')
    return
  }

  // 显示设备选择界面
  showPushSection.value = true
  // 保存当前要推送的看板
  currentPushDashboard.value = dashboard
}

const deleteDashboard = (id) => {
  const index = dashboards.value.findIndex(d => d.id === id)
  if (index > -1) {
    const dashboard = dashboards.value[index]
    if (confirm(`确定要删除看板 "${dashboard.name}" 吗？`)) {
      dashboards.value.splice(index, 1)
      saveDashboards()
      message.success('看板已删除')

      // 清除选择
      const selectedIndex = selectedDashboards.value.indexOf(id)
      if (selectedIndex > -1) {
        selectedDashboards.value.splice(selectedIndex, 1)
      }
    }
  }
}

const batchFetch = async () => {
  if (selectedDashboards.value.length === 0) {
    message.error('请先选择要获取的看板')
    return
  }

  message.info(`开始批量获取 ${selectedDashboards.value.length} 个看板...`)

  for (const dashboardId of selectedDashboards.value) {
    const dashboard = dashboards.value.find(d => d.id === dashboardId)
    if (dashboard) {
      await fetchDashboard(dashboard)
    }
  }

  message.success('批量获取完成')
  clearSelection()
}

const batchPush = () => {
  if (selectedDashboards.value.length === 0) {
    message.error('请先选择要推送的看板')
    return
  }

  if (connectedDevices.value.length === 0) {
    message.error('没有连接的设备')
    return
  }

  showPushSection.value = true
}

const batchDelete = () => {
  if (selectedDashboards.value.length === 0) {
    message.error('请先选择要删除的看板')
    return
  }

  if (confirm(`确定要删除选中的 ${selectedDashboards.value.length} 个看板吗？`)) {
    selectedDashboards.value.forEach(id => {
      const index = dashboards.value.findIndex(d => d.id === id)
      if (index > -1) {
        dashboards.value.splice(index, 1)
      }
    })

    saveDashboards()
    message.success(`已删除 ${selectedDashboards.value.length} 个看板`)
    clearSelection()
  }
}

const toggleDeviceSelection = (device) => {
  const deviceKey = getDeviceKey(device)
  const index = selectedDevices.value.indexOf(deviceKey)
  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(deviceKey)
  }
}

const updateDeviceSelection = (device, checked) => {
  const deviceKey = getDeviceKey(device)
  if (checked && !selectedDevices.value.includes(deviceKey)) {
    selectedDevices.value.push(deviceKey)
  } else if (!checked) {
    const index = selectedDevices.value.indexOf(deviceKey)
    if (index > -1) {
      selectedDevices.value.splice(index, 1)
    }
  }
}

const handlePushToDevices = async () => {
  if (selectedDevices.value.length === 0) {
    message.error('请先选择要推送的设备')
    return
  }

  try {
    let dashboardsToPush = []

    if (currentPushDashboard.value) {
      // 推送单个看板
      dashboardsToPush = [currentPushDashboard.value]
    } else if (selectedDashboards.value.length > 0) {
      // 批量推送
      dashboardsToPush = dashboards.value.filter(d => selectedDashboards.value.includes(d.id))
    }

    if (dashboardsToPush.length === 0) {
      message.error('没有要推送的看板')
      return
    }

    message.info(`开始推送 ${dashboardsToPush.length} 个看板到 ${selectedDevices.value.length} 个设备...`)

    let successCount = 0
    let errorCount = 0

    // 遍历选中的设备
    for (const deviceKey of selectedDevices.value) {
      const device = connectedDevices.value.find(d => getDeviceKey(d) === deviceKey)
      if (!device || !device.connected) {
        console.warn(`设备 ${deviceKey} 未连接，跳过`)
        errorCount++
        continue
      }

      // 遍历要推送的看板
      for (const dashboard of dashboardsToPush) {
        try {
          await pushDashboardToDevice(dashboard, device)
          successCount++
        } catch (error) {
          console.error(`推送看板 ${dashboard.name} 到设备 ${deviceKey} 失败:`, error)
          errorCount++
        }
      }
    }

    if (successCount > 0) {
      message.success(`推送完成！成功: ${successCount}, 失败: ${errorCount}`)
    } else {
      message.error(`推送失败！所有看板推送都失败了`)
    }

    // 清理状态
    closePushSection()
    if (selectedDashboards.value.length > 0) {
      clearSelection()
    }

  } catch (error) {
    console.error('推送看板失败:', error)
    message.error(`推送看板失败: ${error.message}`)
  }
}

// 推送单个看板到设备
const pushDashboardToDevice = async (dashboard, device) => {
  return new Promise((resolve, reject) => {
    try {
      // 检查看板是否已获取内容
      if (!dashboard.fileName) {
        reject(new Error('看板内容尚未获取，请先获取看板内容'))
        return
      }

      // 发送看板推送命令
      const pushCommand = {
        type: 'pushDashboard',
        dashboard: dashboard,
        fileName: dashboard.fileName
      }

      if (device.ws && device.ws.readyState === WebSocket.OPEN) {
        device.ws.send(JSON.stringify(pushCommand))
        console.log('已发送看板推送命令:', pushCommand)

        // 监听推送结果
        const handlePushResponse = (event) => {
          try {
            const response = JSON.parse(event.data)

            if (response.type === 'pushDashboardResponse') {
              if (response.success) {
                console.log(`看板 ${dashboard.name} 已推送到设备 ${device.ip}`)
                resolve()
              } else {
                reject(new Error(response.message || '推送失败'))
              }
              device.ws.removeEventListener('message', handlePushResponse)
            } else if (response.type === 'error') {
              reject(new Error(response.message || '推送错误'))
              device.ws.removeEventListener('message', handlePushResponse)
            }
          } catch (error) {
            console.error('解析推送响应失败:', error)
          }
        }

        device.ws.addEventListener('message', handlePushResponse)

        // 设置超时清理
        setTimeout(() => {
          device.ws.removeEventListener('message', handlePushResponse)
          reject(new Error('推送超时'))
        }, 10000)

      } else {
        reject(new Error('设备连接已断开'))
      }
    } catch (error) {
      reject(error)
    }
  })
}

const closePushSection = () => {
  showPushSection.value = false
  selectedDevices.value = []
  currentPushDashboard.value = null
}

const addDashboard = () => {
  if (!newDashboard.value.name || !newDashboard.value.url) {
    message.error('请填写看板名称和URL')
    return
  }
  if (dashboards.value.length >= MAX_DASHBOARDS) {
    message.error(`最多只能保存${MAX_DASHBOARDS}个看板`)
    return
  }
  const dashboard = {
    id: Date.now().toString(),
    name: newDashboard.value.name,
    url: newDashboard.value.url,
    description: newDashboard.value.description,
    status: null,
    lastFetched: null,
    thumbnail: null
  }
  dashboards.value.push(dashboard)
  saveDashboards()
  message.success(`看板 "${dashboard.name}" 已添加`)
  // 重置表单
  newDashboard.value = {
    name: '',
    url: '',
    description: ''
  }
  showAddDashboardModal.value = false
}

// 生命周期
onMounted(() => {
  // 初始化数据
  loadLoginConfigs()
  loadDashboards()
})

// 加载登录配置
const loadLoginConfigs = () => {
  const saved = localStorage.getItem('dashboardLoginConfigs')
  if (saved) {
    try {
      loginConfigs.value = JSON.parse(saved)
      if (loginConfigs.value.length === 0) {
        addDefaultConfig()
      }
    } catch (e) {
      console.error('加载登录配置失败:', e)
      addDefaultConfig()
    }
  } else {
    addDefaultConfig()
  }
}

// 添加默认配置
const addDefaultConfig = () => {
  loginConfigs.value = []
  currentConfigIndex.value = 0
}

// 加载看板列表
const loadDashboards = () => {
  const saved = localStorage.getItem('dashboardList')
  if (saved) {
    try {
      const arr = JSON.parse(saved)
      dashboards.value = Array.isArray(arr) ? arr.slice(0, MAX_DASHBOARDS) : []
    } catch (e) {
      console.error('加载看板列表失败:', e)
      dashboards.value = []
    }
  }
}

// 保存看板列表
const saveDashboards = () => {
  // 限制最多保存MAX_DASHBOARDS个
  dashboards.value = dashboards.value.slice(0, MAX_DASHBOARDS)
  localStorage.setItem('dashboardList', JSON.stringify(dashboards.value))
}


</script>

<style scoped>
/* 看板管理页面样式 */
.dashboard-fetch-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f7;
}

.action-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn-primary:hover {
  background: #0056cc;
}

.action-btn-danger {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

.action-btn-danger:hover {
  background: #d70015;
}

/* 登录配置区域 */
.login-section {
  margin-bottom: 24px;
}

.login-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1d1d1f;
}

.login-form {
  padding: 16px 0;
}

.login-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e5e7;
}

.login-status {
  margin-top: 16px;
}

/* 看板列表区域 */
.dashboards-section {
  margin-bottom: 24px;
}

.dashboards-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.dashboard-card {
  position: relative;
  border: 1px solid #e5e5e7;
  border-radius: 12px;
  padding: 16px;
  background: #f9f9f9;
  transition: all 0.2s ease;
  cursor: pointer;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-selected {
  border-color: #007aff;
  background: #f0f8ff;
}

.dashboard-checkbox {
  position: absolute;
  top: 12px;
  right: 12px;
}

.checkbox-input {
  width: 16px;
  height: 16px;
}

.dashboard-preview {
  text-align: center;
  margin-bottom: 16px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-thumbnail img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  object-fit: cover;
}

.dashboard-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-info {
  text-align: center;
  margin-bottom: 16px;
}

.dashboard-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dashboard-url {
  font-size: 14px;
  color: #86868b;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dashboard-meta {
  display: flex;
  justify-content: center;
  gap: 12px;
  font-size: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #86868b;
}

.status-success {
  color: #34c759;
}

.status-error {
  color: #ff3b30;
}

.dashboard-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dashboard-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dashboard-btn-primary {
  background: #007aff;
  color: white;
}

.dashboard-btn-primary:hover {
  background: #0056cc;
}

.dashboard-btn-success {
  background: #34c759;
  color: white;
}

.dashboard-btn-success:hover {
  background: #28a745;
}

.dashboard-btn-info {
  background: #5ac8fa;
  color: white;
}

.dashboard-btn-info:hover {
  background: #007aff;
}

.dashboard-btn-danger {
  background: #ff3b30;
  color: white;
}

.dashboard-btn-danger:hover {
  background: #d70015;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #86868b;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  margin: 0 0 24px 0;
}

/* 批量操作 */
.batch-actions {
  background: #f0f8ff;
  border: 1px solid #007aff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-size: 14px;
  font-weight: 500;
  color: #007aff;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

/* 设备推送区域 */
.push-section {
  margin-bottom: 24px;
}

.push-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-item:hover {
  background: #f5f5f7;
}

.device-selected {
  border-color: #007aff;
  background: #f0f8ff;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.push-actions {
  padding-top: 16px;
  border-top: 1px solid #e5e5e7;
}

.no-devices {
  text-align: center;
  padding: 40px;
}

/* 添加看板表单 */
.add-dashboard-form {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-fetch-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .dashboards-grid {
    grid-template-columns: 1fr;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
