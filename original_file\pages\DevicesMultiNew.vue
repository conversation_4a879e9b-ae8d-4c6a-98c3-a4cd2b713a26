<template>
  <div class="devices-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">设备管理</h1>
        <p class="page-description">选择多个设备进行批量操作</p>
      </div>
      <div class="header-actions">
        <div class="mode-buttons">
          <button class="mode-btn" @click="$router.push('/devices')">单选模式</button>
          <button class="mode-btn mode-btn-active">多选模式</button>
        </div>
        <div class="action-buttons">
          <button class="action-btn" @click="showConnectionTester = true">连接测试</button>
          <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedDevices.length > 0" class="batch-actions">
      <div class="batch-content">
        <div class="batch-info">
          <span class="batch-count">已选择 {{ selectedDevices.length }} 个设备</span>
        </div>
        <div class="batch-buttons">
          <button class="action-btn" @click="batchConnect">批量连接</button>
          <button class="action-btn" @click="batchDisconnect">批量断开</button>
          <button class="action-btn action-btn-primary" @click="batchGoToContents">批量管理内容</button>
          <button class="action-btn" @click="clearSelection">清除选择</button>
        </div>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="devices-section">
      <div class="devices-grid">
        <div
          v-for="(device, index) in devices"
          :key="`${device.ip}:${device.port}`"
          class="device-card"
          :class="{ 
            'device-connected': device.connected,
            'device-connecting': device.connecting,
            'device-selected': selectedDevices.includes(index)
          }"
        >
          <!-- 选择框 -->
          <div class="device-checkbox">
            <input 
              type="checkbox" 
              :checked="selectedDevices.includes(index)"
              @change="toggleDeviceSelection(index)"
              class="checkbox-input"
            />
          </div>

          <!-- 设备状态指示器 -->
          <div class="device-status-indicator">
            <div 
              class="status-dot" 
              :class="{
                'status-connected': device.connected,
                'status-connecting': device.connecting,
                'status-disconnected': !device.connected && !device.connecting
              }"
            ></div>
          </div>

          <!-- 设备信息 -->
          <div class="device-info">
            <div class="device-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" :fill="device.connected ? '#000' : '#86868b'"/>
              </svg>
            </div>
            
            <h3 class="device-name">{{ device.name || `设备 ${index + 1}` }}</h3>
            <p class="device-address">{{ device.ip }}:{{ device.port }}</p>
            
            <div class="device-status">
              <span 
                class="status-badge"
                :class="{
                  'status-badge-connected': device.connected,
                  'status-badge-connecting': device.connecting,
                  'status-badge-disconnected': !device.connected && !device.connecting
                }"
              >
                {{ getStatusText(device) }}
              </span>
            </div>
          </div>

          <!-- 设备操作 -->
          <div class="device-actions">
            <button
              v-if="!device.connected && !device.connecting"
              class="connect-btn"
              @click="connectDevice(index)"
            >
              连接设备
            </button>
            
            <button
              v-else-if="device.connecting"
              class="connect-btn connect-btn-loading"
              disabled
            >
              连接中...
            </button>
            
            <div v-else class="connected-actions">
              <button
                class="action-btn action-btn-primary"
                @click="goToContents(index)"
              >
                内容管理
              </button>
              <button
                class="action-btn"
                @click="disconnectDevice(index)"
              >
                断开连接
              </button>
            </div>
          </div>

          <!-- 设备菜单 -->
          <div class="device-menu">
            <button class="menu-btn" @click="handleDeviceMenu(index)">⋯</button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="devices.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="#86868b"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无设备</h3>
        <p class="empty-description">请先添加设备以开始使用</p>
        <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <div v-if="showAddDialog" class="modal-overlay" @click="showAddDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">添加设备</h2>
          <button class="modal-close" @click="showAddDialog = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">设备名称</label>
            <input v-model="newDevice.name" class="form-input" placeholder="请输入设备名称" />
          </div>
          <div class="form-group">
            <label class="form-label">IP地址</label>
            <input v-model="newDevice.ip" class="form-input" placeholder="请输入IP地址" />
          </div>
          <div class="form-group">
            <label class="form-label">端口</label>
            <input v-model="newDevice.port" type="number" class="form-input" placeholder="请输入端口号" />
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showAddDialog = false">取消</button>
          <button class="action-btn action-btn-primary" @click="handleAddDevice">添加</button>
        </div>
      </div>
    </div>

    <!-- 连接测试器 -->
    <ConnectionTester v-model="showConnectionTester" />

    <!-- 批量连接成功对话框 -->
    <div v-if="showSuccessModal" class="modal-overlay" @click="showSuccessModal = false">
      <div class="modal-content success-modal" @click.stop>
        <div class="modal-header">
          <div class="success-header">
            <div class="success-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#34c759" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <h2 class="modal-title">批量连接成功</h2>
          </div>
          <button class="modal-close" @click="showSuccessModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="success-content">
            <p class="success-message">已成功连接 <strong>{{ connectedDeviceCount }}</strong> 个设备！</p>
            <p class="success-question">是否立即进入内容管理页面获取多个设备的内容？</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showSuccessModal = false">稍后再说</button>
          <button class="action-btn action-btn-primary" @click="goToContentsFromModal">立即进入</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDeviceStore } from '@/store/device'
import ConnectionTester from '@/components/ConnectionTester.vue'

const router = useRouter()
const deviceStore = useDeviceStore()
const { devices, addDevice, connectDevice: storeConnectDevice, disconnectDevice: storeDisconnectDevice } = deviceStore

// 响应式数据
const showAddDialog = ref(false)
const showConnectionTester = ref(false)
const showSuccessModal = ref(false)
const selectedDevices = ref([])
const connectedDeviceCount = ref(0)
const newDevice = ref({
  name: '',
  ip: '',
  port: 9000
})

// 方法
const getStatusText = (device) => {
  if (device.connecting) return '连接中'
  if (device.connected) return '已连接'
  return '未连接'
}

const toggleDeviceSelection = (index) => {
  const selectedIndex = selectedDevices.value.indexOf(index)
  if (selectedIndex > -1) {
    selectedDevices.value.splice(selectedIndex, 1)
  } else {
    selectedDevices.value.push(index)
  }
}

const clearSelection = () => {
  selectedDevices.value = []
}

const connectDevice = async (index) => {
  const device = devices[index]
  device.connecting = true
  
  try {
    const wsConnection = storeConnectDevice(index)
    if (wsConnection) {
      wsConnection.connect()
    }
  } catch (error) {
    device.connecting = false
    console.error('连接失败:', error)
  }
}

const disconnectDevice = (index) => {
  try {
    storeDisconnectDevice(index)
  } catch (error) {
    console.error('断开失败:', error)
  }
}

const batchConnect = () => {
  let connectedCount = 0
  const totalSelected = selectedDevices.value.length

  selectedDevices.value.forEach(index => {
    if (!devices[index].connected && !devices[index].connecting) {
      connectDevice(index)

      // 监听连接成功
      const checkConnection = setInterval(() => {
        if (devices[index].connected) {
          clearInterval(checkConnection)
          connectedCount++

          // 如果所有选中的设备都连接完成，显示成功模态框
          if (connectedCount === totalSelected) {
            showBatchConnectionSuccessModal(connectedCount)
          }
        }
      }, 500)
    }
  })
}

const batchDisconnect = () => {
  selectedDevices.value.forEach(index => {
    if (devices[index].connected) {
      disconnectDevice(index)
    }
  })
}

const batchGoToContents = () => {
  router.push('/contents')
}

const goToContents = () => {
  router.push('/contents')
}

const showBatchConnectionSuccessModal = (connectedCount) => {
  showSuccessModal.value = true
  connectedDeviceCount.value = connectedCount
}

const goToContentsFromModal = () => {
  showSuccessModal.value = false
  router.push('/contents')
}

const handleAddDevice = () => {
  if (!newDevice.value.name || !newDevice.value.ip || !newDevice.value.port) {
    alert('请填写完整的设备信息')
    return
  }
  
  // 检查设备是否已存在
  const exists = devices.some(d => d.ip === newDevice.value.ip && d.port === newDevice.value.port)
  if (exists) {
    alert('该设备已存在')
    return
  }
  
  addDevice({
    ...newDevice.value,
    connected: false,
    connecting: false,
    connectionState: 'disconnected'
  })
  
  showAddDialog.value = false
  
  // 重置表单
  newDevice.value = {
    name: '',
    ip: '',
    port: 9000
  }
}

const handleDeviceMenu = (index) => {
  // 设备菜单功能
  console.log('设备菜单:', index)
}
</script>
