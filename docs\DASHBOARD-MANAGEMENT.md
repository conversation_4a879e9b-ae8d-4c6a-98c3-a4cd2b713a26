# 看板管理功能使用说明

## 功能概述

看板管理功能允许您从需要登录的网站获取HTML看板内容，并推送到指定的设备进行全屏显示。这个功能特别适用于SCADA系统、监控面板、数据分析看板等需要实时显示的Web应用。

## 主要特性

1. **网站登录配置** - 支持基本认证的网站登录
2. **看板内容获取** - 自动获取网站HTML内容并保存
3. **设备推送** - 将看板内容推送到连接的设备
4. **全屏显示** - 自动全屏显示看板内容
5. **批量操作** - 支持批量获取和推送多个看板

## 使用步骤

### 1. 配置网站登录信息

在"网站登录配置"区域填写以下信息：

- **网站地址**: 目标网站的基础URL（如：`http://your-scada-server.com`）
- **用户名**: 登录用户名
- **密码**: 登录密码
- **登录页面**: 登录页面路径（通常为 `/login`）

配置完成后，点击"测试登录"验证配置是否正确。

### 2. 添加看板

点击"添加看板"按钮，填写看板信息：

- **看板名称**: 便于识别的看板名称
- **看板URL**: 相对于网站根目录的路径（如：`/WebDesigner/Designer/RunTime.html?projectId=19&userName=80415865&token=BFJJdhqjyfF`）
- **描述**: 看板的详细描述（可选）

### 3. 获取看板内容

1. 确保至少有一个设备已连接
2. 在看板列表中找到要获取的看板
3. 点击"获取内容"按钮
4. 系统会自动：
   - 使用配置的登录信息访问网站
   - 获取指定看板页面的HTML内容
   - 处理相对路径，确保资源能正确加载
   - 保存为HTML文件到服务器共享目录

### 4. 推送看板到设备

获取内容成功后，可以推送看板到设备：

1. 点击看板的"推送"按钮
2. 在弹出的设备选择界面中选择目标设备
3. 点击"推送到选中设备"
4. 系统会自动：
   - 在目标设备上打开播放器
   - 加载看板HTML内容
   - 进入全屏模式显示

### 5. 批量操作

支持以下批量操作：

- **批量获取**: 选择多个看板，一次性获取所有内容
- **批量推送**: 选择多个看板，推送到指定设备
- **批量删除**: 删除选中的看板配置

## 技术实现

### 前端功能

- Vue 3 + Naive UI 组件库
- WebSocket 与设备通信
- 本地存储看板配置
- 响应式设计，支持移动端

### 服务端功能

- Node.js + WebSocket 服务
- HTTP客户端获取网站内容
- HTML内容处理和优化
- 文件管理和共享

### 看板显示

- iframe 全屏显示
- 自动处理相对路径
- 支持交互式内容
- 加载状态指示

## 注意事项

1. **网站兼容性**: 目标网站需要支持iframe嵌入，某些网站可能有X-Frame-Options限制
2. **认证方式**: 当前支持基本认证，复杂的认证方式可能需要额外配置
3. **资源加载**: 系统会自动处理相对路径，但某些动态加载的资源可能需要特殊处理
4. **设备连接**: 确保目标设备已连接并且WebSocket通信正常
5. **网络访问**: 服务器需要能够访问目标网站

## 故障排除

### 获取内容失败

1. 检查网站地址是否正确
2. 验证登录信息是否有效
3. 确认网络连接正常
4. 查看服务端日志获取详细错误信息

### 推送失败

1. 确认设备连接状态
2. 检查WebSocket连接是否正常
3. 验证看板内容是否已成功获取
4. 查看设备端播放器是否正常工作

### 显示异常

1. 检查目标网站是否支持iframe嵌入
2. 验证资源路径是否正确
3. 确认设备浏览器兼容性
4. 检查网络防火墙设置

## 扩展功能

未来可以考虑添加以下功能：

1. **更多认证方式**: OAuth、SAML等
2. **定时刷新**: 自动定时获取最新内容
3. **缓存机制**: 提高加载速度
4. **截图预览**: 生成看板缩略图
5. **权限管理**: 用户权限和访问控制

## 示例配置

### SCADA系统看板

```
网站地址: http://scada.company.com
用户名: operator
密码: ********
看板URL: /WebDesigner/Designer/RunTime.html?projectId=19&userName=operator&token=xxxxx
```

### 监控系统看板

```
网站地址: https://monitor.company.com
用户名: admin
密码: ********
看板URL: /dashboard/system-overview
```

通过以上配置和操作，您可以轻松地将各种Web看板内容推送到大屏设备进行展示。
