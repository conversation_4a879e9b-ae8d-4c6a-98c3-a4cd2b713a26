// @/composables/useWebSocket.js
import { ref, watch, onUnmounted, getCurrentInstance } from 'vue'

export function useWebSocket(selectedDevice, onMessage, deviceStore = null, deviceIndex = -1) {
  const ws = ref(null)
  const isConnected = ref(false)
  const connectionState = ref('disconnected') // 新增详细状态：disconnected/connecting/connected/error
  const lastError = ref(null)
  const reconnectTimer = ref(null)
  const reconnectCount = ref(0)
  const heartbeatTimer = ref(null) // 心跳定时器
  const healthCheckTimer = ref(null) // 健康检查定时器
  const messageQueue = ref([]) // 消息队列
  const pendingMessages = ref(new Map()) // 待确认消息
  const connectionQuality = ref('unknown') // 连接质量：excellent/good/poor/bad

  // 配置参数 - 针对生产环境16台主机优化
  const config = {
    baseReconnectInterval: 2000,     // 基础重连间隔增加到2秒
    maxReconnectInterval: 30000,     // 最大重连间隔增加到30秒
    maxReconnectAttempts: 10,        // 最大重连次数增加到10次
    heartbeatInterval: 15000,        // 心跳间隔缩短到15秒，提高检测频率
    connectionTimeout: 8000,         // 连接超时时间8秒
    messageTimeout: 5000,            // 消息响应超时5秒
    healthCheckInterval: 60000,      // 健康检查间隔1分钟
  }

  let isManualDisconnect = false
  let lastHeartbeatTime = 0
  let heartbeatResponseTime = 0
  let missedHeartbeats = 0

  // 清除重连定时器
  const clearReconnectTimer = () => {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }

  // 新增：清除心跳定时器
  const clearHeartbeatTimer = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value)
      heartbeatTimer.value = null
    }
  }

  // 增强心跳功能
  const sendHeartbeat = () => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      try {
        lastHeartbeatTime = Date.now()
        const heartbeatId = `heartbeat_${lastHeartbeatTime}`

        // 发送带ID的心跳消息
        ws.value.send(JSON.stringify({
          type: 'heartbeat',
          id: heartbeatId,
          timestamp: lastHeartbeatTime
        }))

        // 记录待确认的心跳
        pendingMessages.value.set(heartbeatId, {
          type: 'heartbeat',
          timestamp: lastHeartbeatTime,
          timeout: setTimeout(() => {
            missedHeartbeats++
            pendingMessages.value.delete(heartbeatId)
            console.warn(`⚠️ 心跳超时 (ID: ${heartbeatId})，已错过 ${missedHeartbeats} 次心跳`)

            // 连续错过3次心跳认为连接有问题
            if (missedHeartbeats >= 3) {
              console.error('❌ 连续错过3次心跳，触发重连')
              handleConnectionError(new Error('心跳超时'))
            }
          }, config.messageTimeout)
        })

        console.log(`💓 发送心跳消息 (ID: ${heartbeatId})`)
      } catch (err) {
        console.error('❌ 发送心跳失败:', err)
        missedHeartbeats++
        handleConnectionError(err)
      }
    } else {
      console.warn('⚠️ WebSocket 未连接，跳过心跳')
      missedHeartbeats++
    }
  }

  // 新增：评估连接质量
  const evaluateConnectionQuality = () => {
    const now = Date.now()
    const responseTime = heartbeatResponseTime

    if (missedHeartbeats === 0 && responseTime < 100) {
      connectionQuality.value = 'excellent'
    } else if (missedHeartbeats <= 1 && responseTime < 500) {
      connectionQuality.value = 'good'
    } else if (missedHeartbeats <= 2 && responseTime < 1000) {
      connectionQuality.value = 'poor'
    } else {
      connectionQuality.value = 'bad'
    }

    console.log(`📊 连接质量评估: ${connectionQuality.value} (响应时间: ${responseTime}ms, 错过心跳: ${missedHeartbeats})`)
  }

  // 新增：健康检查
  const performHealthCheck = () => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ 健康检查：WebSocket未连接')
      return
    }

    evaluateConnectionQuality()

    // 清理过期的待确认消息
    const now = Date.now()
    for (const [id, message] of pendingMessages.value.entries()) {
      if (now - message.timestamp > config.messageTimeout * 2) {
        clearTimeout(message.timeout)
        pendingMessages.value.delete(id)
        console.warn(`🧹 清理过期消息: ${id}`)
      }
    }

    // 如果连接质量差，主动重连
    if (connectionQuality.value === 'bad' && !isManualDisconnect) {
      console.warn('⚠️ 连接质量差，主动重连')
      handleConnectionError(new Error('连接质量差'))
    }
  }

  // 新增：处理连接错误
  const handleConnectionError = (err) => {
    lastError.value = err
    console.error('❌ WebSocket 连接错误:', err)

    // 如果连接已关闭，尝试重连
    if (!isConnected.value && !isManualDisconnect && reconnectCount.value < config.maxReconnectAttempts) {
      const reconnectDelay = Math.min(
        config.baseReconnectInterval * Math.pow(2, reconnectCount.value - 1),
        config.maxReconnectInterval
      )
      console.log(`🔄 计划第 ${reconnectCount.value + 1} 次重连（延迟 ${reconnectDelay}ms）...`)
      reconnectTimer.value = setTimeout(() => {
        connect()
      }, reconnectDelay)
    } else if (reconnectCount.value >= config.maxReconnectAttempts) {
      console.error(`❌ 已达到最大重连次数 (${config.maxReconnectAttempts})，停止重连`)
      connectionState.value = 'failed'
    }
  }

  // 建立连接
  function connect() {
    // 校验设备信息
    if (!selectedDevice.value) {
      lastError.value = new Error('设备信息为空')
      connectionState.value = 'error'
      console.error('❌ 设备信息为空')
      return
    }

    const { ip, port } = selectedDevice.value
    if (!ip || !port) {
      lastError.value = new Error('设备IP或端口缺失')
      connectionState.value = 'error'
      console.error('❌ 设备IP或端口缺失:', { ip, port })
      return
    }

    // 如果已经在连接中，避免重复连接
    if (connectionState.value === 'connecting') {
      console.log('⏳ 连接正在进行中，跳过重复连接')
      return
    }

    // 关闭现有连接
    if (ws.value && ws.value.readyState !== WebSocket.CLOSED) {
      console.log('🔄 关闭现有连接')
      ws.value.close(3001, '重新连接')
    }

    connectionState.value = 'connecting'
    const currentAttempt = reconnectCount.value + 1

    try {
      const wsUrl = `ws://${ip}:${port}`
      console.log(`🔗 创建WebSocket连接到: ${wsUrl} (尝试 ${currentAttempt}/${config.maxReconnectAttempts})`)

      ws.value = new WebSocket(wsUrl)

      // 设置连接超时
      const connectTimeout = setTimeout(() => {
        if (ws.value && ws.value.readyState === WebSocket.CONNECTING) {
          console.error('⏰ 连接超时')
          ws.value.close()
          handleConnectionError(new Error('连接超时'))
        }
      }, 10000) // 10秒超时

      ws.value.onopen = () => {
        clearTimeout(connectTimeout)
        isConnected.value = true
        connectionState.value = 'connected'
        lastError.value = null
        reconnectCount.value = 0
        clearReconnectTimer()
        console.log(`✅ WebSocket 已成功连接到 ${wsUrl}`)

        // 重置连接状态
        missedHeartbeats = 0
        heartbeatResponseTime = 0
        connectionQuality.value = 'excellent'

        // 启动心跳
        clearHeartbeatTimer()
        heartbeatTimer.value = setInterval(sendHeartbeat, config.heartbeatInterval)

        // 启动健康检查
        if (healthCheckTimer.value) {
          clearInterval(healthCheckTimer.value)
        }
        healthCheckTimer.value = setInterval(performHealthCheck, config.healthCheckInterval)

        // 发送初始心跳
        sendHeartbeat()

        // 更新设备连接状态
        if (deviceStore && deviceStore.setDeviceConnected && deviceIndex >= 0) {
          deviceStore.setDeviceConnected(deviceIndex, true, ws.value, 'connected')
        }
      }

      ws.value.onclose = (event) => {
        clearTimeout(connectTimeout)
        isConnected.value = false
        clearHeartbeatTimer()

        // 根据关闭代码判断关闭原因
        const closeReasons = {
          1000: '正常关闭',
          1001: '端点离开',
          1002: '协议错误',
          1003: '不支持的数据',
          1006: '异常关闭',
          1011: '服务器错误',
          3001: '重新连接'
        }

        const reason = closeReasons[event.code] || '未知原因'
        console.log(`🔌 WebSocket 关闭 (代码: ${event.code}, 原因: ${reason})`)

        // 根据关闭代码决定是否重连
        const shouldReconnect = !isManualDisconnect &&
                               reconnectCount.value < config.maxReconnectAttempts &&
                               event.code !== 1000 && // 正常关闭不重连
                               event.code !== 1001    // 端点离开不重连

        if (shouldReconnect) {
          connectionState.value = 'reconnecting'
          const reconnectDelay = Math.min(
            config.baseReconnectInterval * Math.pow(2, reconnectCount.value),
            config.maxReconnectInterval
          )
          console.log(`🔄 计划第 ${reconnectCount.value + 1} 次重连（延迟 ${reconnectDelay}ms）...`)
          reconnectTimer.value = setTimeout(() => {
            reconnectCount.value++
            connect()
          }, reconnectDelay)
        } else {
          connectionState.value = 'disconnected'
          if (isManualDisconnect) {
            console.log('👋 手动断开连接')
            isManualDisconnect = false
          } else if (reconnectCount.value >= config.maxReconnectAttempts) {
            console.error('❌ 已达到最大重连次数，停止重连')
            connectionState.value = 'failed'
          }
        }

        // 更新设备连接状态
        if (deviceStore && deviceStore.setDeviceConnected && deviceIndex >= 0) {
          deviceStore.setDeviceConnected(deviceIndex, false, null, connectionState.value)
        }
      }

      ws.value.onerror = (err) => {
        clearTimeout(connectTimeout)
        isConnected.value = false
        connectionState.value = 'error'
        console.error('❌ WebSocket 错误事件:', err)
        handleConnectionError(err)
      }

      ws.value.onmessage = (event) => {
        console.log(`收到WebSocket消息: ${event.data}`)

        // 处理心跳响应
        try {
          const data = JSON.parse(event.data)
          if (data.type === 'heartbeat' && data.id) {
            const heartbeatMessage = pendingMessages.value.get(data.id)
            if (heartbeatMessage) {
              // 计算响应时间
              heartbeatResponseTime = Date.now() - heartbeatMessage.timestamp

              // 清除超时定时器
              clearTimeout(heartbeatMessage.timeout)
              pendingMessages.value.delete(data.id)

              // 重置错过心跳计数
              missedHeartbeats = 0

              console.log(`💓 收到心跳响应 (ID: ${data.id}, 响应时间: ${heartbeatResponseTime}ms)`)

              // 更新连接质量
              evaluateConnectionQuality()
            }
          }
        } catch (e) {
          // 不是JSON格式的消息，继续正常处理
        }

        if (typeof onMessage === 'function') {
          onMessage(event) // 确保消息回调执行
        }
      }

    } catch (err) {
      isConnected.value = false
      connectionState.value = 'error'
      handleConnectionError(err)
    }
  }

  // 发送命令（优化：检查实际连接状态而非仅依赖标志位）
  function sendCommand(command) {
    console.log('📤 尝试发送命令:', command)

    // 检查WebSocket实际状态
    const isWsOpen = ws.value && ws.value.readyState === WebSocket.OPEN

    if (isWsOpen) {
      try {
        const message = typeof command === 'string' ? command : JSON.stringify(command)
        ws.value.send(message)
        console.log('✅ 命令发送成功:', message)

        // 添加命令类型日志，方便调试
        if (typeof command === 'object' && command.type) {
          console.log(`📋 发送的命令类型: ${command.type}`)
        }

        return true
      } catch (err) {
        lastError.value = new Error('发送消息失败: ' + err.message)
        console.error('❌ 发送命令错误:', err)
        return false
      }
    } else {
      const wsState = ws.value ? ws.value.readyState : 'null'
      const stateNames = {
        0: 'CONNECTING',
        1: 'OPEN',
        2: 'CLOSING',
        3: 'CLOSED',
        'null': 'NULL'
      }
      const err = new Error(`WebSocket未连接（状态: ${connectionState.value}, WS状态: ${stateNames[wsState]}），无法发送指令`)
      lastError.value = err
      console.warn('⚠️', err.message)
      return false
    }
  }

  // 断开连接
  function disconnect() {
    isManualDisconnect = true
    clearReconnectTimer()
    clearHeartbeatTimer()

    // 清除健康检查定时器
    if (healthCheckTimer.value) {
      clearInterval(healthCheckTimer.value)
      healthCheckTimer.value = null
    }

    // 清除所有待确认消息
    for (const [id, message] of pendingMessages.value.entries()) {
      clearTimeout(message.timeout)
    }
    pendingMessages.value.clear()

    // 重置状态
    missedHeartbeats = 0
    heartbeatResponseTime = 0
    connectionQuality.value = 'unknown'

    if (ws.value) {
      ws.value.close(1000, '手动断开')
    }
    ws.value = null
    isConnected.value = false
    connectionState.value = 'disconnected'
  }

  // 监听设备变化
  const stopWatch = watch(
    () => selectedDevice.value,
    (newDevice) => {
      if (newDevice) {
        isManualDisconnect = false
        connect()
      } else {
        disconnect()
      }
    },
    { immediate: true }
  )

  // 组件卸载时清理（只在组件内部使用时注册）
  const instance = getCurrentInstance()
  if (instance) {
    onUnmounted(() => {
      stopWatch()
      disconnect()
    })
  }

  // 手动清理方法（用于非组件环境）
  const cleanup = () => {
    stopWatch()
    disconnect()
  }

  return {
    ws,
    isConnected,
    connectionState, // 暴露详细连接状态
    connectionQuality, // 暴露连接质量
    lastError,
    reconnectCount,
    messageQueue, // 暴露消息队列
    pendingMessages, // 暴露待确认消息
    connect,
    sendCommand,
    disconnect,
    clearReconnectTimer,
    cleanup // 暴露手动清理方法
  }
}