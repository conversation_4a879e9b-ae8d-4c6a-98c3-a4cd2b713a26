<template>
  <div class="device-tab">
    <div class="status-bar">
      状态：
      <n-tag :type="connectionState === 'connected' ? 'success' : 'error'" size="small">
        {{ connectionState === 'connected' ? '已连接' :
           connectionState === 'connecting' ? '连接中...' :
           connectionState === 'error' ? '连接错误' : '未连接' }}
      </n-tag>
      <n-space style="margin-left: 16px">
        <n-button size="small" @click="handleCancelSelect">取消选中</n-button>
        <n-button size="small" @click="testWebSocket">测试连接</n-button>
        <n-button size="small" @click="checkSharedFolder">检查共享文件夹</n-button>
      </n-space>
      <span class="error-msg" v-if="lastError">{{ lastError.message }}</span>
    </div>

    <!-- 调试用：显示原始数据 -->
    <pre v-if="debugMode" class="debug-log">
      连接状态: {{ connectionState }}
      文件列表: {{ JSON.stringify(deviceFiles, null, 2) }}
    </pre>

    <!-- 添加错误边界 -->
    <div v-if="!hasError">
      <n-tabs v-model:value="activeTab" class="file-tabs" type="line">
        <n-tab-pane name="video" tab="视频">
          <FileList :files="stableDeviceFiles.video" :device="device" type="video" />
        </n-tab-pane>
        <n-tab-pane name="image" tab="图片">
          <FileList :files="stableDeviceFiles.image" :device="device" type="image" />
        </n-tab-pane>
        <n-tab-pane name="html" tab="HTML">
          <FileList :files="stableDeviceFiles.html" :device="device" type="html" />
        </n-tab-pane>
        <n-tab-pane name="ppt" tab="PPT">
          <FileList :files="stableDeviceFiles.ppt" :device="device" type="ppt" />
        </n-tab-pane>
        <n-tab-pane name="text" tab="文本">
          <FileList :files="stableDeviceFiles.text" :device="device" type="text" />
        </n-tab-pane>
        <n-tab-pane name="word" tab="Word">
          <FileList :files="stableDeviceFiles.word" :device="device" type="word" />
        </n-tab-pane>
        <n-tab-pane name="pdf" tab="PDF">
          <FileList :files="stableDeviceFiles.pdf" :device="device" type="pdf" />
        </n-tab-pane>
        <n-tab-pane name="excel" tab="Excel">
          <FileList :files="stableDeviceFiles.excel" :device="device" type="excel" />
        </n-tab-pane>
      </n-tabs>
    </div>
    <div v-else class="error-boundary">
      <n-alert
        title="组件渲染错误"
        type="error"
        show-icon
      >
        {{ errorMessage }}
      </n-alert>
      <n-button size="small" type="primary" @click="retryRender" style="margin-top: 16px">重试</n-button>
    </div>
  </div>
</template>

<script setup>
import { inject, watch, ref, computed, reactive, onUnmounted, onErrorCaptured } from 'vue'
import { useMessage } from 'naive-ui'
import FileList from './FileList.vue'
import { useDeviceStore } from '@/store/device'
import { useWebSocket } from '@/composables/useWebSocket'

const message = useMessage()

// 定义props
const props = defineProps({ 
  device: {
    type: Object,
    required: true,
    default: () => ({ ip: '', port: '', name: '未知设备' })
  }
})

// 调试模式
const debugMode = ref(false)

// 响应式文件列表
const deviceFiles = reactive({
  video: [],
  image: [],
  html: [],
  ppt: [],
  text: [],
  word: [],
  pdf: [],
  excel: []
})

// 稳定的文件列表
const stableDeviceFiles = computed(() => ({
  video: deviceFiles.video || [],
  image: deviceFiles.image || [],
  html: deviceFiles.html || [],
  ppt: deviceFiles.ppt || [],
  text: deviceFiles.text || [],
  word: deviceFiles.word || [],
  pdf: deviceFiles.pdf || [],
  excel: deviceFiles.excel || []
}))

// 当前激活的标签页
const activeTab = ref('video')

// 组件错误状态
const hasError = ref(false)
const errorMessage = ref('')

// 组件卸载标记
const isUnmounted = ref(false)

// 监控deviceFiles变化
watch(
  () => deviceFiles,
  (newFiles) => {
    console.log('deviceFiles状态更新:', newFiles)
    console.log('视频文件数量:', newFiles.video.length)
    console.log('图片文件数量:', newFiles.image.length)
    console.log('HTML文件数量:', newFiles.html.length)
  },
  { deep: true, immediate: true }
)

// 设备相关逻辑
const deviceStore = useDeviceStore()
const { toggleSelectDevice, devices } = deviceStore
const deviceIndex = computed(() =>
  devices.findIndex(d => d.ip === props.device.ip && d.port === props.device.port)
)

// WebSocket相关
const deviceRef = ref(props.device)
const { sendCommand, isConnected, connectionState, lastError, connect } = useWebSocket(
  deviceRef,
  (event) => {
    // 组件已卸载，不处理消息
    if (isUnmounted.value) return
    
    console.log('✅ WebSocket消息到达DeviceTab:', event.data)
    try {
      const msg = JSON.parse(event.data)
      console.log('解析后的消息结构:', msg)

      if (msg.type === 'fileList') {
        console.log('📂 收到文件列表，原始数据:', msg.files)

        // 验证文件列表结构
        const videoFiles = Array.isArray(msg.files.video) ? msg.files.video : []
        const imageFiles = Array.isArray(msg.files.image) ? msg.files.image : []
        const htmlFiles = Array.isArray(msg.files.html) ? msg.files.html : []
        const pptFiles = Array.isArray(msg.files.ppt) ? msg.files.ppt : []
        const textFiles = Array.isArray(msg.files.text) ? msg.files.text : []
        const wordFiles = Array.isArray(msg.files.word) ? msg.files.word : []
        const pdfFiles = Array.isArray(msg.files.pdf) ? msg.files.pdf : []
        const excelFiles = Array.isArray(msg.files.excel) ? msg.files.excel : []

        console.log('🔍 验证后文件数量:', {
          video: videoFiles.length,
          image: imageFiles.length,
          html: htmlFiles.length,
          ppt: pptFiles.length,
          text: textFiles.length,
          word: wordFiles.length
        })

        // 直接修改reactive对象属性
        deviceFiles.video = videoFiles
        deviceFiles.image = imageFiles
        deviceFiles.html = htmlFiles
        deviceFiles.ppt = pptFiles
        deviceFiles.text = textFiles
        deviceFiles.word = wordFiles
        deviceFiles.pdf = pdfFiles
        deviceFiles.excel = excelFiles

        console.log('✏️ 赋值后deviceFiles立即检查:', {
          video: deviceFiles.video.length,
          image: deviceFiles.image.length,
          html: deviceFiles.html.length,
          ppt: deviceFiles.ppt.length,
          text: deviceFiles.text.length,
          word: deviceFiles.word.length
        })
      } else if (msg.type === 'pushContentResponse') {
        // 处理推送内容的响应
        console.log('📤 收到推送内容响应:', msg);
        
        // 清除超时计时器
        if (pushTimeout.value) {
          clearTimeout(pushTimeout.value);
          pushTimeout.value = null;
        }
        
        if (msg.success) {
          console.log('✅ 内容推送成功');
          // 这里可以添加成功提示，如 ElMessage.success('内容推送成功')
        } else {
          console.error('❌ 内容推送失败:', msg.error);
          // 这里可以添加失败提示，如 ElMessage.error('内容推送失败: ' + msg.error)
        }
      }
    } catch (e) {
      console.error('❌ 消息解析/处理失败:', e)
      lastError.value = e
      errorMessage.value = `消息处理错误: ${e.message}`
      hasError.value = true
    }
  }
)

// 初始化连接
connect()

// 连接成功后自动请求文件列表（优化重试机制）
watch(isConnected, (connected) => {
  if (connected) {
    console.log('🔗 连接成功，尝试发送listFiles命令')
    sendListFilesCommand()
  }
})

// 发送文件列表命令（带重试和状态检查）
const sendListFilesCommand = (retry = 3) => {
  // 组件已卸载，不发送命令
  if (isUnmounted.value) return
  
  // 优先检查更精确的connectionState
  if (connectionState.value !== 'connected') {
    console.log(`连接状态异常（${connectionState.value}），取消发送命令`)
    return
  }
  
  if (retry <= 0) {
    console.error('发送listFiles命令失败，已达最大重试次数')
    errorMessage.value = '获取文件列表失败，请检查连接'
    hasError.value = true
    return
  }
  
  const success = sendCommand({ type: 'listFiles' })
  if (!success) {
    console.log(`发送失败，剩余重试次数: ${retry - 1}`)
    setTimeout(() => sendListFilesCommand(retry - 1), 1000)
  }
}

// 按钮事件处理
const handleCancelSelect = () => {
  if (deviceIndex.value !== -1) toggleSelectDevice(deviceIndex.value)
}

const testWebSocket = () => {
  console.log('📤 手动触发listFiles命令')
  if (connectionState.value === 'connected') {
    sendListFilesCommand()
  } else {
    console.log('🔄 未连接，尝试重新连接')
    connect()
  }
}

const checkSharedFolder = () => {
  console.log('检查共享文件夹路径是否正确')
  if (connectionState.value === 'connected') {
    sendCommand({ type: 'checkFolder' })
  } else {
    console.log('未连接，无法检查共享文件夹')
    lastError.value = new Error('未连接到设备，无法执行检查')
  }
}

// 重试渲染（用于错误恢复）
const retryRender = () => {
  hasError.value = false
  errorMessage.value = ''
  if (connectionState.value === 'connected') {
    sendListFilesCommand()
  } else {
    connect()
  }
}

// 捕获组件内部错误
onErrorCaptured((err) => {
  console.error('DeviceTab组件内部错误:', err)
  errorMessage.value = `组件渲染错误: ${err.message}`
  hasError.value = true
  return false // 继续向上传播错误
})

// 组件卸载时清理
onUnmounted(() => {
  isUnmounted.value = true
  console.log('DeviceTab组件已卸载')
})

// 事件总线监听
const eventBus = inject('eventBus', { on: () => {} })
eventBus.on('fetchFiles', () => {
  console.log('📢 收到fetchFiles事件，触发文件列表请求')
  testWebSocket()
})
</script>

<style scoped>
.status-bar {
  margin: 10px 0;
  padding: 12px 16px;
  background: #F5F5F7;
  border-radius: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  color: #1D1D1F;
  font-family: 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  border: 1px solid #E5E5E7;
}

/* 状态高亮 */
.online {
  color: #34C759;
  font-weight: bold;
}

.offline {
  color: #888ea1;
  font-weight: bold;
}

/* 错误信息展示 */
.error-msg {
  color: #FF3B30;
  font-size: 13px;
  margin-left: auto;
  font-style: italic;
  background: rgba(255, 0, 0, 0.08);
  padding: 4px 8px;
  border-radius: 6px;
}

/* tabs 上边距 */
.file-tabs {
  margin-top: 14px;
}

/* 调试区域 */
.debug-log {
  background: #F5F5F7;
  color: #1D1D1F;
  padding: 14px;
  border-radius: 12px;
  font-size: 13px;
  line-height: 1.6;
  overflow-x: auto;
  white-space: pre-wrap;
  border: 1px solid #E5E5E7;
  margin: 14px 0;
}

/* 错误提示容器 */
.error-boundary {
  margin: 24px;
  text-align: center;
}

/* el-alert 自定义颜色 */
:deep(.el-alert__title) {
  color: #FF3B30 !important;
  font-weight: bold;
}
</style>
