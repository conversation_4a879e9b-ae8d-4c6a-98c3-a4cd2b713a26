
# 音量控制脚本
param([int]$volume = 50)

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class VolumeControl {
    [DllImport("winmm.dll")]
    public static extern int waveOutSetVolume(IntPtr hwo, uint dwVolume);
    
    [DllImport("winmm.dll")]
    public static extern int waveOutGetVolume(IntPtr hwo, out uint dwVolume);
    
    public static void SetVolume(int volume) {
        if (volume < 0) volume = 0;
        if (volume > 100) volume = 100;
        
        uint vol = (uint)((volume * 0xFFFF) / 100);
        uint stereoVol = (vol << 16) | vol;
        waveOutSetVolume(IntPtr.Zero, stereoVol);
    }
    
    public static int GetVolume() {
        uint volume;
        waveOutGetVolume(IntPtr.Zero, out volume);
        return (int)((volume & 0xFFFF) * 100 / 0xFFFF);
    }
}
"@

if ($volume -eq -1) {
    $currentVolume = [VolumeControl]::GetVolume()
    Write-Output "Current volume: $currentVolume"
} else {
    [VolumeControl]::SetVolume($volume)
    Write-Output "Volume set to: $volume"
}
