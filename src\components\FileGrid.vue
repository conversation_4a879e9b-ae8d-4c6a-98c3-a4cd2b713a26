<template>
  <div class="oppo-file-grid">
    <!-- 文件网格头部 -->
    <div class="oppo-grid-header" v-if="files.length > 0">
      <!-- 分类标签 -->
      <div class="oppo-category-tabs">
        <n-tabs v-model:value="activeCategory" type="line" size="large" class="oppo-tabs">
          <n-tab-pane
            v-for="category in categories"
            :key="category.key"
            :name="category.key"
            :tab="getCategoryTabLabel(category)"
          />
        </n-tabs>
      </div>

      <!-- 工具栏 -->
      <div class="oppo-grid-toolbar">
        <div class="oppo-view-controls">
          <n-button-group>
            <n-button
              :type="viewMode === 'grid' ? 'primary' : 'default'"
              size="small"
              @click="viewMode = 'grid'"
            >
              <template #icon>
                <n-icon>
                  <GridOutline />
                </n-icon>
              </template>
              网格
            </n-button>
            <n-button
              :type="viewMode === 'list' ? 'primary' : 'default'"
              size="small"
              @click="viewMode = 'list'"
            >
              <template #icon>
                <n-icon>
                  <ListOutline />
                </n-icon>
              </template>
              列表
            </n-button>
          </n-button-group>
        </div>

        <div class="oppo-sort-controls">
          <n-select
            v-model:value="sortBy"
            :options="sortOptions"
            size="small"
            style="width: 120px"
            placeholder="排序方式"
          />
        </div>

        <div class="oppo-search-control">
          <n-input
            v-model:value="searchKeyword"
            size="small"
            placeholder="搜索文件..."
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
          </n-input>
        </div>
      </div>
    </div>

    <!-- 文件网格内容 -->
    <div class="oppo-grid-content" v-if="files.length > 0">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="oppo-files-grid">
        <div
          v-for="file in filteredFiles"
          :key="file.path"
          class="oppo-file-item"
          :class="getFileItemClass(file)"
          @click="handleFileClick(file)"
        >
          <!-- 文件预览 -->
          <div class="oppo-file-preview">
            <!-- 图片预览 -->
            <div v-if="isImageFile(file)" class="oppo-image-preview">
              <img
                :src="getFilePreviewUrl(file)"
                :alt="file.name"
                class="oppo-preview-image"
                @load="handleImageLoad"
                @error="handleImageError"
              />
              <div class="oppo-image-overlay">
                <n-icon size="24" color="white">
                  <EyeOutline />
                </n-icon>
              </div>
            </div>

            <!-- 视频预览 -->
            <div v-else-if="isVideoFile(file)" class="video-preview">
              <video
                :src="getFilePreviewUrl(file)"
                :poster="getVideoThumbnail(file)"
                preload="metadata"
                muted
                class="preview-video"
                @loadedmetadata="handleVideoLoad"
                @error="handleVideoError"
              >
                您的浏览器不支持视频预览
              </video>
              <div class="video-overlay">
                <n-icon size="24" color="white">
                  <PlayOutline />
                </n-icon>
              </div>
            </div>

            <!-- HTML预览 -->
            <div v-else-if="isHtmlFile(file)" class="html-preview">
              <iframe
                :src="getFilePreviewUrl(file)"
                frameborder="0"
                class="preview-iframe"
                @load="handleIframeLoad"
                @error="handleIframeError"
              ></iframe>
            </div>

            <!-- 文档预览 -->
            <div v-else-if="isDocumentFile(file)" class="document-preview">
              <div class="document-thumbnail">
                <n-icon size="48" :color="getFileIconColor(file.type)">
                  <component :is="getFileIcon(file.type)" />
                </n-icon>
                <div class="document-info">
                  <span class="doc-type">{{ getFileTypeLabel(file.type) }}</span>
                  <span class="doc-pages" v-if="file.pages">{{ file.pages }} 页</span>
                </div>
              </div>
            </div>

            <!-- 默认文件图标 -->
            <!-- 文档预览 -->
            <div v-else class="oppo-document-preview">
              <div class="oppo-document-icon" :class="getFileTypeClass(file)">
                <n-icon size="32" :color="getFileTypeColor(file)">
                  <component :is="getFileTypeIcon(file)" />
                </n-icon>
              </div>
              <div class="oppo-file-type-label">{{ getFileTypeLabel(file) }}</div>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="oppo-file-info">
            <h4 class="oppo-file-name" :title="file.name">{{ file.name }}</h4>
            <div class="oppo-file-meta">
              <span class="oppo-file-size">{{ formatFileSize(file.size) }}</span>
              <span class="oppo-file-date">{{ formatFileDate(file.modified) }}</span>
            </div>
          </div>

          <!-- 文件操作 -->
          <div class="oppo-file-actions">
            <n-button
              size="small"
              type="primary"
              @click.stop="handlePushFile(file)"
              :disabled="!hasConnectedDevices"
            >
              <template #icon>
                <n-icon>
                  <SendOutline />
                </n-icon>
              </template>
              推送
            </n-button>

            <n-button
              size="small"
              @click.stop="handlePreviewFile(file)"
            >
              <template #icon>
                <n-icon>
                  <EyeOutline />
                </n-icon>
              </template>
              预览
            </n-button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="oppo-files-list">
        <div class="oppo-list-header">
          <div class="oppo-list-col oppo-col-name">文件名</div>
          <div class="oppo-list-col oppo-col-size">大小</div>
          <div class="oppo-list-col oppo-col-date">修改时间</div>
          <div class="oppo-list-col oppo-col-actions">操作</div>
        </div>

        <div
          v-for="file in filteredFiles"
          :key="file.path"
          class="oppo-list-item"
          :class="getFileItemClass(file)"
          @click="handleFileClick(file)"
        >
          <div class="oppo-list-col oppo-col-name">
            <div class="oppo-list-file-info">
              <div class="oppo-list-file-icon">
                <n-icon size="20" :color="getFileTypeColor(file)">
                  <component :is="getFileTypeIcon(file)" />
                </n-icon>
              </div>
              <span class="oppo-list-file-name" :title="file.name">{{ file.name }}</span>
            </div>
          </div>
          <div class="oppo-list-col oppo-col-size">{{ formatFileSize(file.size) }}</div>
          <div class="oppo-list-col oppo-col-date">{{ formatFileDate(file.modified) }}</div>
          <div class="oppo-list-col oppo-col-actions">
            <n-button
              size="tiny"
              type="primary"
              @click.stop="handlePushFile(file)"
              :disabled="!hasConnectedDevices"
            >
              推送
            </n-button>
            <n-button
              size="tiny"
              @click.stop="handlePreviewFile(file)"
            >
              预览
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="oppo-empty-state">
      <n-icon size="64" color="var(--oppo-neutral-400)">
        <FolderOutline />
      </n-icon>
      <h3 class="oppo-empty-title">暂无文件</h3>
      <p class="oppo-empty-description">此设备当前没有可用的文件</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import {
  VideocamOutline,
  ImageOutline,
  DocumentOutline,
  FolderOutline,
  ArchiveOutline,
  CodeOutline,
  PlayOutline,
  EyeOutline,
  SendOutline,
  MusicalNotesOutline,
  DocumentTextOutline,
  GridOutline,
  ListOutline,
  SearchOutline,
  PlayCircleOutline
} from '@vicons/ionicons5'

const message = useMessage()

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  device: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['openLocal', 'pushToPlayer', 'play', 'preview'])

// 响应式数据
const activeCategory = ref('all')
const viewMode = ref('grid')
const sortBy = ref('name')
const searchKeyword = ref('')

// 排序选项
const sortOptions = [
  { label: '按名称', value: 'name' },
  { label: '按大小', value: 'size' },
  { label: '按时间', value: 'date' },
  { label: '按类型', value: 'type' }
]

// 计算属性
const hasConnectedDevices = computed(() => true) // 模拟有连接的设备

// 文件分类定义
const categories = computed(() => {
  const filesByType = props.files.reduce((acc, file) => {
    const type = file.type || 'other'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {})

  return [
    {
      key: 'all',
      label: '全部',
      icon: GridOutline,
      color: '#007AFF',
      tagType: 'info',
      count: props.files.length
    },
    {
      key: 'video',
      label: '视频',
      icon: VideocamOutline,
      color: '#FF3B30',
      tagType: 'error',
      count: filesByType.video || 0
    },
    {
      key: 'image',
      label: '图片',
      icon: ImageOutline,
      color: '#34C759',
      tagType: 'success',
      count: filesByType.image || 0
    },
    {
      key: 'audio',
      label: '音频',
      icon: MusicalNotesOutline,
      color: '#FF9500',
      tagType: 'warning',
      count: filesByType.audio || 0
    },
    {
      key: 'document',
      label: '文档',
      icon: DocumentTextOutline,
      color: '#9C88FF',
      tagType: 'info',
      count: (filesByType.document || 0) + (filesByType.pdf || 0) + (filesByType.text || 0)
    },
    {
      key: 'other',
      label: '其他',
      icon: FolderOutline,
      color: '#909090',
      tagType: 'default',
      count: filesByType.other || 0
    }
  ].filter(category => category.key === 'all' || category.count > 0)
})

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (activeCategory.value === 'all') {
    return props.files
  }

  return props.files.filter(file => {
    const type = file.type || 'other'
    if (activeCategory.value === 'document') {
      return ['document', 'pdf', 'text'].includes(type)
    }
    return type === activeCategory.value
  })
})

// 获取文件图标
const getFileIcon = (type) => {
  const iconMap = {
    video: VideocamOutline,
    image: ImageOutline,
    document: DocumentOutline,
    archive: ArchiveOutline,
    code: CodeOutline
  }
  return iconMap[type] || DocumentOutline
}

// 获取文件图标颜色
const getFileIconColor = (type) => {
  const colorMap = {
    video: '#FF3B30',
    image: '#34C759',
    document: '#007AFF',
    archive: '#FF9500',
    code: '#9C88FF'
  }
  return colorMap[type] || '#86868B'
}

// 获取文件类型标签
const getFileTypeLabel = (type) => {
  const labelMap = {
    video: '视频',
    image: '图片',
    document: '文档',
    archive: '压缩包',
    code: '代码'
  }
  return labelMap[type] || '文件'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 文件类型判断方法
const isImageFile = (file) => {
  const imageTypes = ['image', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
  return imageTypes.includes(file.type) || imageTypes.includes(getFileExtension(file.name))
}

const isVideoFile = (file) => {
  const videoTypes = ['video', 'mp4', 'webm', 'ogg', 'avi', 'mov']
  return videoTypes.includes(file.type) || videoTypes.includes(getFileExtension(file.name))
}

const isHtmlFile = (file) => {
  const htmlTypes = ['html', 'htm']
  return htmlTypes.includes(file.type) || htmlTypes.includes(getFileExtension(file.name))
}

const isDocumentFile = (file) => {
  const docTypes = ['document', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']
  return docTypes.includes(file.type) || docTypes.includes(getFileExtension(file.name))
}

const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase()
}

// 获取文件预览URL
const getFilePreviewUrl = (file) => {
  if (!props.device) return ''

  // 构建预览URL - 直接使用shared路径
  const baseUrl = `http://${props.device.ip}:9004`
  // 从完整路径中提取文件名，避免双重编码
  let fileName = file.name || file.path.split('/').pop() || file.path.split('\\').pop()

  // 如果文件名已经是编码的，先解码再重新编码
  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，说明不是编码的文件名，直接使用
  }

  return `${baseUrl}/shared/${encodeURIComponent(fileName)}`
}

// 获取视频缩略图
const getVideoThumbnail = (file) => {
  if (!props.device) return ''

  const baseUrl = `http://${props.device.ip}:9004`
  // 从完整路径中提取文件名，避免双重编码
  let fileName = file.name || file.path.split('/').pop() || file.path.split('\\').pop()

  // 如果文件名已经是编码的，先解码再重新编码
  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，说明不是编码的文件名，直接使用
  }

  return `${baseUrl}/thumbnail/${encodeURIComponent(fileName)}`
}

// 事件处理方法
const handleFileClick = (file) => {
  emit('play', file)
}

const handlePreview = (file) => {
  emit('preview', file)
}

const handleImageLoad = () => {
  console.log('图片加载成功')
}

const handleImageError = () => {
  console.error('图片加载失败')
}

const handleVideoLoad = () => {
  console.log('视频加载成功')
}

const handleVideoError = () => {
  console.error('视频加载失败')
}

const handleIframeLoad = () => {
  console.log('HTML加载成功')
}

const handleIframeError = () => {
  console.error('HTML加载失败')
}

// 新增的缺失方法
const getCategoryTabLabel = (category) => {
  return `${category.label} (${category.count})`
}

const getFileItemClass = (file) => {
  return {
    'oppo-file-selected': false, // 可以根据需要添加选中状态
    [`oppo-file-${file.type}`]: true
  }
}

const getFileTypeClass = (file) => {
  return `oppo-file-type-${file.type || 'unknown'}`
}

const getFileTypeIcon = (file) => {
  return getFileIcon(file.type)
}

const getFileTypeColor = (file) => {
  return getFileIconColor(file.type)
}

const getVideoDuration = (file) => {
  // 模拟视频时长
  return '00:30'
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatFileDate = (date) => {
  if (!date) return '未知时间'
  return new Date(date).toLocaleDateString('zh-CN')
}

const handlePushFile = (file) => {
  emit('pushToPlayer', file)
  message.success(`正在推送文件: ${file.name}`)
}

const handlePreviewFile = (file) => {
  emit('preview', file)
}
</script>

<style scoped>
/* OPPO 文件网格样式 */
.oppo-file-grid {
  background: white;
  border-radius: var(--oppo-radius-xl);
  border: 1px solid var(--oppo-neutral-200);
  overflow: hidden;
}

/* 网格头部 */
.oppo-grid-header {
  padding: var(--oppo-spacing-lg);
  background: var(--oppo-neutral-50);
  border-bottom: 1px solid var(--oppo-neutral-200);
}

.oppo-category-tabs {
  margin-bottom: var(--oppo-spacing-lg);
}

.oppo-grid-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--oppo-spacing-md);
  flex-wrap: wrap;
}

.oppo-view-controls,
.oppo-sort-controls,
.oppo-search-control {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
}

/* 网格内容 */
.oppo-grid-content {
  padding: var(--oppo-spacing-lg);
}

/* 网格视图 */
.oppo-files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--oppo-spacing-lg);
}

.oppo-file-item {
  background: white;
  border-radius: var(--oppo-radius-lg);
  border: 1px solid var(--oppo-neutral-200);
  overflow: hidden;
  transition: all var(--oppo-duration-normal) var(--oppo-easing);
  cursor: pointer;
}

.oppo-file-item:hover {
  border-color: var(--oppo-primary-300);
  box-shadow: var(--oppo-shadow-md);
  transform: translateY(-2px);
}

/* 文件预览 */
.oppo-file-preview {
  position: relative;
  width: 100%;
  height: 120px;
  background: var(--oppo-neutral-100);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.oppo-image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.oppo-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.oppo-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--oppo-duration-normal);
}

.oppo-image-preview:hover .oppo-image-overlay {
  opacity: 1;
}

.oppo-video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.oppo-preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.oppo-video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oppo-video-duration {
  position: absolute;
  bottom: var(--oppo-spacing-xs);
  right: var(--oppo-spacing-xs);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--oppo-spacing-xs) var(--oppo-spacing-sm);
  border-radius: var(--oppo-radius-sm);
  font-size: var(--oppo-text-xs);
  font-family: var(--oppo-font-mono);
}

.oppo-document-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--oppo-spacing-sm);
}

.oppo-document-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--oppo-radius-lg);
  background: var(--oppo-neutral-200);
  display: flex;
  align-items: center;
  justify-content: center;
}

.oppo-file-type-label {
  font-size: var(--oppo-text-xs);
  color: var(--oppo-neutral-600);
  font-weight: 500;
}

/* 文件信息 */
.oppo-file-info {
  padding: var(--oppo-spacing-md);
}

.oppo-file-name {
  font-size: var(--oppo-text-sm);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.oppo-file-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--oppo-text-xs);
  color: var(--oppo-neutral-600);
  margin-bottom: var(--oppo-spacing-sm);
}

.oppo-file-size {
  font-family: var(--oppo-font-mono);
  font-weight: 500;
}

.oppo-file-date {
  font-weight: 500;
}

/* 文件操作 */
.oppo-file-actions {
  padding: 0 var(--oppo-spacing-md) var(--oppo-spacing-md);
  display: flex;
  gap: var(--oppo-spacing-xs);
}

/* 列表视图 */
.oppo-files-list {
  background: white;
  border-radius: var(--oppo-radius-lg);
  border: 1px solid var(--oppo-neutral-200);
  overflow: hidden;
}

.oppo-list-header {
  display: grid;
  grid-template-columns: 1fr 100px 120px 120px;
  gap: var(--oppo-spacing-md);
  padding: var(--oppo-spacing-md) var(--oppo-spacing-lg);
  background: var(--oppo-neutral-50);
  border-bottom: 1px solid var(--oppo-neutral-200);
  font-size: var(--oppo-text-sm);
  font-weight: 600;
  color: var(--oppo-neutral-700);
}

.oppo-list-item {
  display: grid;
  grid-template-columns: 1fr 100px 120px 120px;
  gap: var(--oppo-spacing-md);
  padding: var(--oppo-spacing-md) var(--oppo-spacing-lg);
  border-bottom: 1px solid var(--oppo-neutral-100);
  transition: background-color var(--oppo-duration-normal);
  cursor: pointer;
}

.oppo-list-item:hover {
  background: var(--oppo-neutral-50);
}

.oppo-list-item:last-child {
  border-bottom: none;
}

.oppo-list-col {
  display: flex;
  align-items: center;
  font-size: var(--oppo-text-sm);
  color: var(--oppo-neutral-700);
}

.oppo-col-actions {
  justify-content: flex-end;
  gap: var(--oppo-spacing-xs);
}

.oppo-list-file-info {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
}

.oppo-list-file-icon {
  width: 24px;
  height: 24px;
  border-radius: var(--oppo-radius-sm);
  background: var(--oppo-neutral-100);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.oppo-list-file-name {
  font-weight: 500;
  color: var(--oppo-neutral-900);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空状态 */
.oppo-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--oppo-spacing-4xl);
  text-align: center;
}

.oppo-empty-title {
  font-size: var(--oppo-text-xl);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin: var(--oppo-spacing-lg) 0 var(--oppo-spacing-sm);
}

.oppo-empty-description {
  font-size: var(--oppo-text-base);
  color: var(--oppo-neutral-600);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .oppo-files-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .oppo-grid-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--oppo-spacing-sm);
  }
}

@media (max-width: 768px) {
  .oppo-files-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--oppo-spacing-md);
  }

  .oppo-grid-header {
    padding: var(--oppo-spacing-md);
  }

  .oppo-grid-content {
    padding: var(--oppo-spacing-md);
  }

  .oppo-list-header,
  .oppo-list-item {
    grid-template-columns: 1fr 80px 100px;
    padding: var(--oppo-spacing-sm) var(--oppo-spacing-md);
  }

  .oppo-col-date {
    display: none;
  }
}

@media (max-width: 480px) {
  .oppo-files-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .oppo-file-preview {
    height: 100px;
  }

  .oppo-list-header,
  .oppo-list-item {
    grid-template-columns: 1fr 60px;
  }

  .oppo-col-size {
    display: none;
  }

  .oppo-col-actions {
    flex-direction: column;
    gap: var(--oppo-spacing-xs);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .oppo-file-item:hover {
    transform: none;
    border-color: var(--oppo-neutral-200);
    box-shadow: none;
  }

  .oppo-image-preview:hover .oppo-image-overlay {
    opacity: 0;
  }

  .oppo-list-item:hover {
    background: transparent;
  }
}

/* 减少动画 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
  .oppo-file-item {
    transition: none;
  }

  .oppo-image-overlay {
    transition: none;
  }

  .oppo-list-item {
    transition: none;
  }
}
</style>

