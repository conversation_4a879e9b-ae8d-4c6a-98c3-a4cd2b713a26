<template>
  <div v-if="showModal" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <div class="modal-title">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" fill="currentColor"/>
          </svg>
          连接诊断工具
        </div>
        <button class="modal-close" @click="closeModal">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <div class="connection-tester">
          <div class="test-form">
            <div class="form-group">
              <label class="form-label">服务器IP</label>
              <input
                v-model="testConfig.ip"
                class="form-input"
                placeholder="***************"
                type="text"
              />
            </div>
            <div class="form-group">
              <label class="form-label">端口</label>
              <input
                v-model.number="testConfig.port"
                class="form-input"
                placeholder="9000"
                type="number"
                min="1"
                max="65535"
              />
            </div>
          </div>

          <div class="test-results">
            <div class="results-card">
              <h3 class="results-title">测试结果</h3>
              <div v-if="testResults.length > 0">
                <div class="test-item" v-for="test in testResults" :key="test.name">
                  <div class="test-name">{{ test.name }}</div>
                  <div class="test-status" :class="test.status">
                    {{ test.message }}
                  </div>
                </div>
              </div>

              <div v-else class="no-tests">
                点击"开始测试"进行连接诊断
              </div>
            </div>
          </div>

          <div class="console-output" v-if="consoleOutput.length > 0">
            <div class="results-card">
              <h3 class="results-title">详细日志</h3>
              <div class="console-content">
                <div v-for="(log, index) in consoleOutput" :key="index" class="log-line">
                  <span class="log-time">{{ log.time }}</span>
                  <span :class="['log-level', log.level]">{{ log.level.toUpperCase() }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button class="action-btn" @click="closeModal">关闭</button>
            <button class="action-btn" @click="clearResults">清除结果</button>
            <button class="action-btn action-btn-primary" @click="startTest" :disabled="testing">
              {{ testing ? '测试中...' : '开始测试' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'

// Props & Emits
const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const showModal = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const closeModal = () => {
  emit('update:modelValue', false)
}

const testing = ref(false)
const testConfig = ref({
  ip: '***************',
  port: 9000
})

const testResults = ref([])
const consoleOutput = ref([])

// 添加日志
const addLog = (level, message) => {
  consoleOutput.value.push({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
}

// 添加测试结果
const addTestResult = (name, status, message) => {
  testResults.value.push({ name, status, message })
}

// 清除结果
const clearResults = () => {
  testResults.value = []
  consoleOutput.value = []
}

// 开始测试
const startTest = async () => {
  testing.value = true
  clearResults()
  
  addLog('info', '开始连接测试...')
  
  // 测试1: 基本连接性
  await testBasicConnection()
  
  // 测试2: WebSocket 连接
  await testWebSocketConnection()
  
  // 测试3: 心跳测试
  await testHeartbeat()
  
  testing.value = false
  addLog('info', '测试完成')
}

// 测试基本连接性
const testBasicConnection = async () => {
  addLog('info', `测试基本连接性: ${testConfig.value.ip}:${testConfig.value.port}`)
  
  try {
    // 模拟 ping 测试（实际应该使用 fetch 或其他方法）
    const response = await fetch(`http://${testConfig.value.ip}:9004`, {
      method: 'HEAD',
      mode: 'no-cors',
      timeout: 5000
    })
    
    addTestResult('网络连通性', 'success', '网络可达')
    addLog('success', '网络连通性测试通过')
  } catch (error) {
    addTestResult('网络连通性', 'warning', '无法确认网络状态')
    addLog('warning', `网络测试: ${error.message}`)
  }
}

// 测试 WebSocket 连接
const testWebSocketConnection = () => {
  return new Promise((resolve) => {
    addLog('info', '测试 WebSocket 连接...')
    
    const wsUrl = `ws://${testConfig.value.ip}:${testConfig.value.port}`
    const ws = new WebSocket(wsUrl)
    
    const timeout = setTimeout(() => {
      ws.close()
      addTestResult('WebSocket连接', 'error', '连接超时')
      addLog('error', 'WebSocket 连接超时')
      resolve()
    }, 10000)
    
    ws.onopen = () => {
      clearTimeout(timeout)
      addTestResult('WebSocket连接', 'success', '连接成功')
      addLog('success', 'WebSocket 连接成功')
      ws.close()
      resolve()
    }
    
    ws.onerror = (error) => {
      clearTimeout(timeout)
      addTestResult('WebSocket连接', 'error', '连接失败')
      addLog('error', `WebSocket 连接失败: ${error}`)
      resolve()
    }
    
    ws.onclose = (event) => {
      addLog('info', `WebSocket 关闭: 代码=${event.code}, 原因=${event.reason}`)
    }
  })
}

// 测试心跳
const testHeartbeat = () => {
  return new Promise((resolve) => {
    addLog('info', '测试心跳响应...')
    
    const wsUrl = `ws://${testConfig.value.ip}:${testConfig.value.port}`
    const ws = new WebSocket(wsUrl)
    
    const timeout = setTimeout(() => {
      ws.close()
      addTestResult('心跳测试', 'error', '心跳超时')
      addLog('error', '心跳测试超时')
      resolve()
    }, 15000)
    
    ws.onopen = () => {
      addLog('info', '发送心跳消息...')
      ws.send(JSON.stringify({ type: 'heartbeat' }))
    }
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'heartbeat') {
          clearTimeout(timeout)
          addTestResult('心跳测试', 'success', '心跳响应正常')
          addLog('success', '收到心跳响应')
          ws.close()
          resolve()
        }
      } catch (error) {
        addLog('warning', `解析心跳响应失败: ${error.message}`)
      }
    }
    
    ws.onerror = () => {
      clearTimeout(timeout)
      addTestResult('心跳测试', 'error', '心跳测试失败')
      resolve()
    }
  })
}
</script>
