<template>
  <div>
    <!-- 仅在文件列表有效时渲染 -->
    <n-grid :cols="3" :x-gap="16" :y-gap="16" class="file-list" v-if="isFilesValid" responsive="screen">
      <n-grid-item
        v-for="(file, index) in validFiles"
        :key="file.path || `file-${index}`"
        :span="3" :sm="3" :md="1"
      >
        <FileItem
          :file="file"
          :device="device"
          :type="type"
        />
      </n-grid-item>
    </n-grid>

    <!-- 空状态和错误状态 -->
    <n-empty
      v-if="!isFilesValid || (isFilesValid && validFiles.length === 0) && !hasError"
      :description="emptyDescription"
      class="empty-state"
    />

    <n-alert
      v-if="hasError"
      title="文件加载失败"
      type="error"
      show-icon
      class="error-state"
    >
      {{ errorMessage }}
    </n-alert>
  </div>
</template>

<script setup>
// 修复：添加缺失的computed和watch导入
import { ref, computed, watch, onErrorCaptured } from 'vue' 
import FileItem from './FileItem.vue'

const props = defineProps({
  files: {
    type: Array,
    required: true,
    default: () => []
  },
  device: {
    type: Object,
    required: true,
    default: () => ({})  // 避免undefined
  },
  type: {
    type: String,
    required: true,
    validator: (v) => ['video', 'image', 'html', 'ppt', 'text', 'word', 'pdf', 'excel'].includes(v)
  }
})

// 验证文件列表是否有效（避免非数组导致的循环错误）
const isFilesValid = computed(() => {
  const isValid = Array.isArray(props.files)
  if (!isValid) {
    console.error(`FileList(${props.type})收到的files不是数组:`, props.files)
  }
  return isValid
})

// 过滤有效文件（排除空对象或缺失关键属性的文件）
const validFiles = computed(() => {
  if (!isFilesValid.value) return []
  return props.files.filter(file => {
    const isValid = file && typeof file === 'object' && file.path && file.name
    if (!isValid) {
      console.warn(`FileList(${props.type})过滤无效文件:`, file)
    }
    return isValid
  })
})

// 空状态描述（修复：使用computed）
const emptyDescription = computed(() => {
  const map = {
    video: '无视频文件',
    image: '无图片文件',
    html: '无HTML文件',
    ppt: '无PPT文件',
    text: '无文本文件',
    word: '无Word文件',
    pdf: '无PDF文件',
    excel: '无Excel文件'
  }
  return map[props.type] || '无文件'
})

// 错误状态
const hasError = ref(false)
const errorMessage = ref('')

// 监听文件变化（添加详细日志）
watch(
  () => validFiles.value,  // 监听过滤后的有效文件
  (newFiles) => {
    console.log(`FileList(${props.type})有效文件数量:`, newFiles.length)
    console.log(`FileList(${props.type})有效文件内容:`, newFiles)
  },
  { deep: true, immediate: true }
)

// 捕获组件内部错误
onErrorCaptured((err) => {
  console.error(`FileList(${props.type})组件内部错误:`, err)
  errorMessage.value = `渲染${props.type === 'video' ? '视频' : props.type === 'image' ? '图片' : 'HTML'}列表失败: ${err.message}`
  hasError.value = true
  return false  // 继续向上传播错误
})
</script>

<style scoped>
.file-list {
  margin-top: 10px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #E5E5E7;
  transition: all 0.3s ease;
}

.empty-state {
  margin: 24px 0;
  text-align: center;
  color: #86868B;
  background: #F5F5F7;
  border-radius: 12px;
  padding: 30px 20px;
  border: 1px dashed #E5E5E7;
  animation: fadeIn 0.6s ease;
}

.error-state {
  margin: 24px 0;
  text-align: center;
  background: rgba(255, 0, 0, 0.08);
  color: #FF3B30;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 0, 0, 0.3);
  box-shadow: 0 0 12px rgba(255, 0, 0, 0.2);
  animation: shake 0.5s ease;
}

@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  50% { transform: translateX(2px); }
  75% { transform: translateX(-1px); }
  100% { transform: translateX(0); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

</style>