<template>
  <div class="oppo-error-boundary">
    <div v-if="hasError" class="oppo-error-page">
      <!-- 错误图标和动画 -->
      <div class="oppo-error-visual">
        <div class="oppo-error-icon">
          <n-icon size="80" color="var(--oppo-error)">
            <AlertCircleOutline />
          </n-icon>
        </div>
        <div class="oppo-error-waves">
          <div class="oppo-wave oppo-wave-1"></div>
          <div class="oppo-wave oppo-wave-2"></div>
          <div class="oppo-wave oppo-wave-3"></div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="oppo-error-content">
        <h2 class="oppo-error-title">页面加载出错</h2>
        <p class="oppo-error-message">{{ errorMessage || '抱歉，页面遇到了一些问题' }}</p>

        <!-- 错误代码 -->
        <div class="oppo-error-code">
          <n-tag type="error" size="small" round>
            <template #icon>
              <n-icon>
                <BugOutline />
              </n-icon>
            </template>
            错误代码: {{ errorCode }}
          </n-tag>
        </div>

        <!-- 建议操作 -->
        <div class="oppo-error-suggestions">
          <h4 class="oppo-suggestions-title">您可以尝试：</h4>
          <ul class="oppo-suggestions-list">
            <li>刷新页面重新加载</li>
            <li>检查网络连接状态</li>
            <li>返回首页重新开始</li>
            <li>联系技术支持获取帮助</li>
          </ul>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="oppo-error-actions">
        <n-button
          type="primary"
          size="large"
          @click="retry"
          :loading="isRetrying"
        >
          <template #icon>
            <n-icon>
              <RefreshOutline />
            </n-icon>
          </template>
          {{ isRetrying ? '重新加载中...' : '刷新页面' }}
        </n-button>

        <n-button
          size="large"
          @click="goHome"
        >
          <template #icon>
            <n-icon>
              <HomeOutline />
            </n-icon>
          </template>
          返回首页
        </n-button>

        <n-button
          size="large"
          @click="reportError"
        >
          <template #icon>
            <n-icon>
              <MailOutline />
            </n-icon>
          </template>
          报告问题
        </n-button>
      </div>

      <!-- 错误详情（可展开） -->
      <div class="oppo-error-details">
        <n-collapse>
          <n-collapse-item title="查看错误详情" name="details">
            <div class="oppo-error-info">
              <div class="oppo-info-item">
                <span class="oppo-info-label">错误时间:</span>
                <span class="oppo-info-value">{{ errorTime }}</span>
              </div>
              <div class="oppo-info-item">
                <span class="oppo-info-label">页面路径:</span>
                <span class="oppo-info-value">{{ currentRoute }}</span>
              </div>
              <div class="oppo-info-item">
                <span class="oppo-info-label">用户代理:</span>
                <span class="oppo-info-value">{{ userAgent }}</span>
              </div>
            </div>

            <div v-if="errorDetails" class="oppo-error-stack">
              <h5 class="oppo-stack-title">错误堆栈:</h5>
              <pre class="oppo-stack-content">{{ errorDetails }}</pre>
            </div>
          </n-collapse-item>
        </n-collapse>
      </div>
    </div>

    <slot v-else />
  </div>
</template>

<script setup>
import { ref, computed, onErrorCaptured } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  AlertCircleOutline,
  BugOutline,
  RefreshOutline,
  HomeOutline,
  MailOutline
} from '@vicons/ionicons5'

const router = useRouter()
const route = useRoute()
const message = useMessage()

// 响应式数据
const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')
const errorTime = ref('')
const isRetrying = ref(false)

// 计算属性
const errorCode = computed(() => {
  return `ERR_${Date.now().toString().slice(-6)}`
})

const currentRoute = computed(() => {
  return route.fullPath
})

const userAgent = computed(() => {
  return navigator.userAgent
})

// 捕获错误
onErrorCaptured((error, _instance, info) => {
  console.error('ErrorBoundary 捕获到错误:', error)

  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorDetails.value = `${error.stack}\n\n组件信息: ${info}`
  errorTime.value = new Date().toLocaleString('zh-CN')

  // 发送错误报告到监控系统
  reportErrorToMonitoring(error, info)

  // 返回 false 阻止错误继续传播
  return false
})

// 错误监控上报
const reportErrorToMonitoring = (error, info) => {
  try {
    // 这里可以集成错误监控服务，如 Sentry
    console.log('错误上报:', {
      message: error.message,
      stack: error.stack,
      info,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    })
  } catch (e) {
    console.error('错误上报失败:', e)
  }
}

// 重试
const retry = async () => {
  isRetrying.value = true

  try {
    // 清除错误状态
    hasError.value = false
    errorMessage.value = ''
    errorDetails.value = ''

    // 延迟一下再重新加载，给用户更好的体验
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 重新加载当前路由
    router.go(0)
  } catch (error) {
    message.error('重新加载失败')
    hasError.value = true
  } finally {
    isRetrying.value = false
  }
}

// 返回首页
const goHome = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''

  router.push('/dashboard')
  message.success('已返回首页')
}

// 报告问题
const reportError = () => {
  const errorReport = {
    message: errorMessage.value,
    details: errorDetails.value,
    time: errorTime.value,
    route: currentRoute.value,
    userAgent: userAgent.value,
    code: errorCode.value
  }

  // 这里可以打开邮件客户端或者显示反馈表单
  const subject = encodeURIComponent(`错误报告 - ${errorCode.value}`)
  const body = encodeURIComponent(`
错误信息: ${errorReport.message}
错误时间: ${errorReport.time}
页面路径: ${errorReport.route}
错误代码: ${errorReport.code}

详细信息:
${errorReport.details}
  `)

  window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
  message.success('已打开邮件客户端，请发送错误报告')
}
</script>

<style scoped>
/* OPPO 错误边界样式 */
.oppo-error-boundary {
  min-height: 100vh;
  width: 100%;
}

.oppo-error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--oppo-spacing-2xl);
  background: linear-gradient(135deg, var(--oppo-neutral-50) 0%, var(--oppo-neutral-100) 100%);
  text-align: center;
}

/* 错误视觉效果 */
.oppo-error-visual {
  position: relative;
  margin-bottom: var(--oppo-spacing-2xl);
}

.oppo-error-icon {
  position: relative;
  z-index: 2;
  animation: bounce 2s infinite;
}

.oppo-error-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.oppo-wave {
  position: absolute;
  border: 2px solid var(--oppo-error);
  border-radius: 50%;
  opacity: 0;
  animation: wave 2s infinite;
}

.oppo-wave-1 {
  width: 100px;
  height: 100px;
  margin: -50px 0 0 -50px;
  animation-delay: 0s;
}

.oppo-wave-2 {
  width: 140px;
  height: 140px;
  margin: -70px 0 0 -70px;
  animation-delay: 0.5s;
}

.oppo-wave-3 {
  width: 180px;
  height: 180px;
  margin: -90px 0 0 -90px;
  animation-delay: 1s;
}

/* 错误内容 */
.oppo-error-content {
  max-width: 600px;
  margin-bottom: var(--oppo-spacing-2xl);
}

.oppo-error-title {
  font-size: var(--oppo-text-3xl);
  font-weight: 700;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-lg);
  line-height: 1.2;
}

.oppo-error-message {
  font-size: var(--oppo-text-lg);
  color: var(--oppo-neutral-600);
  line-height: 1.6;
  margin-bottom: var(--oppo-spacing-lg);
}

.oppo-error-code {
  margin-bottom: var(--oppo-spacing-xl);
}

/* 建议操作 */
.oppo-error-suggestions {
  background: white;
  border-radius: var(--oppo-radius-xl);
  padding: var(--oppo-spacing-xl);
  border: 1px solid var(--oppo-neutral-200);
  margin-bottom: var(--oppo-spacing-xl);
  text-align: left;
}

.oppo-suggestions-title {
  font-size: var(--oppo-text-lg);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-md);
}

.oppo-suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.oppo-suggestions-list li {
  position: relative;
  padding-left: var(--oppo-spacing-lg);
  margin-bottom: var(--oppo-spacing-sm);
  color: var(--oppo-neutral-700);
  line-height: 1.5;
}

.oppo-suggestions-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--oppo-primary-500);
  font-weight: bold;
}

/* 操作按钮 */
.oppo-error-actions {
  display: flex;
  gap: var(--oppo-spacing-md);
  margin-bottom: var(--oppo-spacing-2xl);
  flex-wrap: wrap;
  justify-content: center;
}

/* 错误详情 */
.oppo-error-details {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: var(--oppo-radius-xl);
  border: 1px solid var(--oppo-neutral-200);
  overflow: hidden;
}

.oppo-error-info {
  display: flex;
  flex-direction: column;
  gap: var(--oppo-spacing-sm);
  margin-bottom: var(--oppo-spacing-lg);
}

.oppo-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--oppo-spacing-sm);
  background: var(--oppo-neutral-50);
  border-radius: var(--oppo-radius-md);
}

.oppo-info-label {
  font-size: var(--oppo-text-sm);
  font-weight: 500;
  color: var(--oppo-neutral-600);
}

.oppo-info-value {
  font-size: var(--oppo-text-sm);
  color: var(--oppo-neutral-900);
  font-family: var(--oppo-font-mono);
  word-break: break-all;
}

.oppo-error-stack {
  margin-top: var(--oppo-spacing-lg);
}

.oppo-stack-title {
  font-size: var(--oppo-text-base);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-sm);
}

.oppo-stack-content {
  background: var(--oppo-neutral-900);
  color: var(--oppo-neutral-100);
  padding: var(--oppo-spacing-lg);
  border-radius: var(--oppo-radius-md);
  font-size: var(--oppo-text-sm);
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes wave {
  0% {
    opacity: 1;
    transform: scale(0);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .oppo-error-page {
    padding: var(--oppo-spacing-xl);
  }

  .oppo-error-title {
    font-size: var(--oppo-text-2xl);
  }

  .oppo-error-message {
    font-size: var(--oppo-text-base);
  }

  .oppo-error-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .oppo-info-item {
    flex-direction: column;
    gap: var(--oppo-spacing-xs);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .oppo-error-page {
    padding: var(--oppo-spacing-lg);
  }

  .oppo-error-title {
    font-size: var(--oppo-text-xl);
  }

  .oppo-error-suggestions {
    padding: var(--oppo-spacing-lg);
  }

  .oppo-wave-1 {
    width: 80px;
    height: 80px;
    margin: -40px 0 0 -40px;
  }

  .oppo-wave-2 {
    width: 120px;
    height: 120px;
    margin: -60px 0 0 -60px;
  }

  .oppo-wave-3 {
    width: 160px;
    height: 160px;
    margin: -80px 0 0 -80px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .oppo-error-actions button {
    min-height: 48px;
  }
}

/* 减少动画 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
  .oppo-error-icon {
    animation: none;
  }

  .oppo-wave {
    animation: none;
    opacity: 0.3;
  }
}
</style>
