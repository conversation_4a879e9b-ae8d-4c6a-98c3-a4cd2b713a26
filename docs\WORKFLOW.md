# Pad 控制端工作流程说明

## 🎯 业务场景

这是一个 **Pad 控制端应用**，用于远程控制服务端设备播放多媒体内容。

## 📋 完整工作流程

### 1. 设备连接阶段
```
Pad 控制端 → WebSocket 连接 → 服务端设备
```

**操作步骤：**
- 在"设备管理"页面添加服务端设备信息（IP + 端口）
- 点击"连接"建立 WebSocket 通信
- 确认连接状态为"已连接"

### 2. 获取文件列表阶段
```
Pad 控制端 → 发送 listFiles 命令 → 服务端
服务端 → 返回共享文件夹内容 → Pad 控制端
```

**操作步骤：**
- 进入"内容管理"页面
- 选择已连接的设备
- 点击"获取文件列表"按钮
- 系统发送 `{ type: 'listFiles' }` 命令到服务端
- 服务端返回共享文件夹中的文件信息

### 3. 文件展示阶段
```
Pad 控制端接收文件列表 → 分类展示 → 用户浏览选择
```

**展示特性：**
- 按文件类型分类（视频、图片、文档）
- 网格布局展示文件缩略图
- 显示文件名、大小、修改时间
- 支持搜索和筛选

### 4. 文件播放阶段
```
用户选择文件 → Pad 发送播放命令 → 服务端接收 → 服务端本地播放
```

**操作步骤：**
- 用户在 Pad 上点击文件的"推送播放"按钮
- 系统发送播放命令：
  ```json
  {
    "type": "playFile",
    "file": {
      "path": "/shared/videos/demo.mp4",
      "name": "演示视频.mp4",
      "type": "video"
    }
  }
  ```
- 服务端接收命令后用默认应用打开文件
- 服务端返回播放状态确认

## 🔧 技术实现

### WebSocket 通信协议

#### 1. 获取文件列表
```javascript
// 发送
{
  "type": "listFiles",
  "timestamp": 1642234567890
}

// 接收
{
  "type": "fileList",
  "files": [
    {
      "name": "演示视频.mp4",
      "path": "/shared/videos/demo.mp4",
      "size": "125.6 MB",
      "type": "video",
      "lastModified": "2024-01-15 14:30:00"
    }
  ]
}
```

#### 2. 播放文件
```javascript
// 发送
{
  "type": "playFile",
  "file": {
    "path": "/shared/videos/demo.mp4",
    "name": "演示视频.mp4",
    "type": "video"
  }
}

// 接收
{
  "type": "playResult",
  "success": true,
  "message": "文件播放成功"
}
```

#### 3. 心跳保持
```javascript
// 发送（每30秒）
{
  "type": "heartbeat"
}

// 接收
{
  "type": "heartbeat",
  "timestamp": 1642234567890
}
```

### 文件类型识别

```javascript
const fileTypes = {
  video: /\.(mp4|avi|mkv|mov|wmv|flv|webm)$/i,
  image: /\.(jpg|jpeg|png|gif|bmp|svg|webp)$/i,
  document: /\.(pdf|doc|docx|ppt|pptx|xls|xlsx|txt|html|htm)$/i
}
```

## 🎨 用户界面

### Dashboard（仪表盘）
- 设备状态概览
- 快速连接操作
- 工作流程测试

### 设备管理
- 添加/删除设备
- 连接状态管理
- 批量操作

### 内容管理
- 选择目标设备
- 获取服务端文件列表
- 分类浏览文件
- 推送播放操作

### 播放控制
- 选择控制设备
- 播放状态监控
- 音量和模式控制

## 🚀 使用示例

### 典型使用场景

1. **会议室演示**
   - Pad 连接会议室电脑
   - 获取演示文件列表
   - 选择 PPT 推送播放

2. **数字标牌控制**
   - Pad 连接显示设备
   - 获取广告素材列表
   - 选择视频内容播放

3. **教学场景**
   - Pad 连接教学电脑
   - 获取课件资源
   - 选择教学视频播放

## 🔍 调试和测试

### 开发环境测试
1. 启动开发服务器：`npm run dev`
2. 在 Dashboard 点击"测试工作流程"
3. 查看控制台 WebSocket 通信日志
4. 在"内容管理"页面测试文件操作

### 生产环境部署
1. 确保服务端 WebSocket 服务正常运行
2. 配置正确的服务端 IP 和端口
3. 测试网络连通性
4. 验证文件共享权限

## 📞 故障排除

### 常见问题

1. **连接失败**
   - 检查服务端 IP 和端口
   - 确认网络连通性
   - 查看防火墙设置

2. **文件列表为空**
   - 确认服务端共享文件夹配置
   - 检查文件权限
   - 查看服务端日志

3. **播放失败**
   - 确认文件路径正确
   - 检查服务端默认应用配置
   - 验证文件格式支持

---

**Pad Controller** - 让远程控制变得简单高效！
