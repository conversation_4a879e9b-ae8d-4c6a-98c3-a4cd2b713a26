<template>
  <!-- OPPO 风格的连接测试模态框 -->
  <n-modal
    v-model:show="showModal"
    preset="card"
    style="width: 90%; max-width: 600px;"
    :mask-closable="false"
    :closable="false"
  >
    <template #header>
      <div class="oppo-modal-header">
        <n-icon size="24" color="var(--oppo-primary-500)">
          <WifiOutline />
        </n-icon>
        <span class="oppo-modal-title">连接诊断工具</span>
      </div>
    </template>

    <div class="oppo-connection-tester">
      <!-- 测试配置 -->
      <div class="oppo-test-config">
        <h3 class="oppo-section-title">连接配置</h3>

        <n-form :model="testConfig" label-placement="left" label-width="80px">
          <n-form-item label="服务器IP">
            <n-input
              v-model:value="testConfig.ip"
              placeholder="请输入服务器IP地址"
              :disabled="isTesting"
            >
              <template #prefix>
                <n-icon>
                  <ServerOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item label="端口">
            <n-input-number
              v-model:value="testConfig.port"
              placeholder="端口号"
              :min="1"
              :max="65535"
              :disabled="isTesting"
              style="width: 100%"
            />
          </n-form-item>

          <n-form-item label="超时时间">
            <n-input-number
              v-model:value="testConfig.timeout"
              placeholder="超时时间(秒)"
              :min="1"
              :max="60"
              :disabled="isTesting"
              style="width: 100%"
            />
          </n-form-item>
        </n-form>

        <div class="oppo-test-actions">
          <n-button
            type="primary"
            size="large"
            @click="startTest"
            :loading="isTesting"
            :disabled="!isConfigValid"
            block
          >
            <template #icon>
              <n-icon>
                <PlayOutline />
              </n-icon>
            </template>
            {{ isTesting ? '测试中...' : '开始测试' }}
          </n-button>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="oppo-test-results">
        <div class="oppo-results-header">
          <h3 class="oppo-section-title">测试结果</h3>
          <div v-if="testResults.length > 0" class="oppo-results-summary">
            <n-tag
              :type="overallStatus === 'success' ? 'success' : overallStatus === 'error' ? 'error' : 'warning'"
              size="small"
              round
            >
              {{ getOverallStatusText() }}
            </n-tag>
          </div>
        </div>

        <div v-if="testResults.length > 0" class="oppo-results-list">
          <div
            v-for="test in testResults"
            :key="test.name"
            class="oppo-test-item"
            :class="getTestItemClass(test.status)"
          >
            <div class="oppo-test-icon">
              <n-icon size="20" :color="getTestStatusColor(test.status)">
                <component :is="getTestStatusIcon(test.status)" />
              </n-icon>
            </div>

            <div class="oppo-test-content">
              <div class="oppo-test-name">{{ test.name }}</div>
              <div class="oppo-test-message">{{ test.message }}</div>
              <div v-if="test.details" class="oppo-test-details">
                {{ test.details }}
              </div>
            </div>

            <div class="oppo-test-status">
              <n-tag :type="getTestTagType(test.status)" size="small" round>
                {{ getTestStatusText(test.status) }}
              </n-tag>
            </div>
          </div>
        </div>

        <div v-else class="oppo-no-results">
          <n-icon size="48" color="var(--oppo-neutral-400)">
            <InformationCircleOutline />
          </n-icon>
          <p class="oppo-no-results-text">点击"开始测试"进行连接诊断</p>
        </div>
      </div>
    </div>

    <template #action>
      <div class="oppo-modal-actions">
        <n-button @click="closeModal" :disabled="isTesting">
          取消
        </n-button>

        <n-button
          v-if="testResults.length > 0"
          @click="clearResults"
          :disabled="isTesting"
        >
          清除结果
        </n-button>

        <n-button
          type="primary"
          @click="retryTest"
          :loading="isTesting"
          :disabled="!isConfigValid"
        >
          <template #icon>
            <n-icon>
              <RefreshOutline />
            </n-icon>
          </template>
          重新测试
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { useMessage } from 'naive-ui'
import {
  WifiOutline,
  ServerOutline,
  PlayOutline,
  RefreshOutline,
  InformationCircleOutline,
  CheckmarkCircleOutline,
  AlertCircleOutline,
  WarningOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'

const message = useMessage()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

// 响应式数据
const showModal = computed({
  get: () => props.show,
  set: (value) => {
    if (!value) emit('close')
  }
})

const testConfig = ref({
  ip: '***************',
  port: 9000,
  timeout: 10
})

const isTesting = ref(false)
const testResults = ref([])
const consoleOutput = ref([])

// 计算属性
const isConfigValid = computed(() => {
  return testConfig.value.ip &&
         testConfig.value.port > 0 &&
         testConfig.value.port <= 65535 &&
         testConfig.value.timeout > 0
})

const overallStatus = computed(() => {
  if (testResults.value.length === 0) return 'pending'

  const hasError = testResults.value.some(test => test.status === 'error')
  const hasWarning = testResults.value.some(test => test.status === 'warning')

  if (hasError) return 'error'
  if (hasWarning) return 'warning'
  return 'success'
})

// 工具方法
const getOverallStatusText = () => {
  const statusMap = {
    success: '全部通过',
    warning: '部分警告',
    error: '存在错误',
    pending: '等待测试'
  }
  return statusMap[overallStatus.value] || '未知'
}

const getTestItemClass = (status) => {
  return `oppo-test-${status}`
}

const getTestStatusColor = (status) => {
  const colorMap = {
    success: 'var(--oppo-success)',
    warning: 'var(--oppo-warning)',
    error: 'var(--oppo-error)',
    pending: 'var(--oppo-neutral-400)'
  }
  return colorMap[status] || 'var(--oppo-neutral-400)'
}

const getTestStatusIcon = (status) => {
  const iconMap = {
    success: CheckmarkCircleOutline,
    warning: WarningOutline,
    error: CloseCircleOutline,
    pending: InformationCircleOutline
  }
  return iconMap[status] || InformationCircleOutline
}

const getTestTagType = (status) => {
  const typeMap = {
    success: 'success',
    warning: 'warning',
    error: 'error',
    pending: 'default'
  }
  return typeMap[status] || 'default'
}

const getTestStatusText = (status) => {
  const textMap = {
    success: '通过',
    warning: '警告',
    error: '失败',
    pending: '等待'
  }
  return textMap[status] || '未知'
}

const addLog = (level, message) => {
  consoleOutput.value.push({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
}

// 测试方法
const startTest = async () => {
  if (!isConfigValid.value || isTesting.value) return

  isTesting.value = true
  testResults.value = []
  consoleOutput.value = []

  addLog('info', '开始连接测试...')

  try {
    // 测试1: 基本连接测试
    await testBasicConnection()

    // 测试2: WebSocket连接测试
    await testWebSocketConnection()

    // 测试3: HTTP服务测试
    await testHttpService()

    addLog('success', '所有测试完成')
    message.success('连接测试完成')
  } catch (error) {
    addLog('error', `测试失败: ${error.message}`)
    message.error('连接测试失败')
  } finally {
    isTesting.value = false
  }
}

const testBasicConnection = async () => {
  addLog('info', '测试基本网络连接...')

  try {
    // 模拟网络连接测试
    await new Promise(resolve => setTimeout(resolve, 1000))

    testResults.value.push({
      name: '网络连接',
      status: 'success',
      message: '网络连接正常',
      details: `目标: ${testConfig.value.ip}:${testConfig.value.port}`
    })

    addLog('success', '网络连接测试通过')
  } catch (error) {
    testResults.value.push({
      name: '网络连接',
      status: 'error',
      message: '网络连接失败',
      details: error.message
    })

    addLog('error', '网络连接测试失败')
    throw error
  }
}

const testWebSocketConnection = async () => {
  addLog('info', '测试WebSocket连接...')

  try {
    // 模拟WebSocket连接测试
    await new Promise(resolve => setTimeout(resolve, 1500))

    testResults.value.push({
      name: 'WebSocket连接',
      status: 'success',
      message: 'WebSocket连接正常',
      details: `ws://${testConfig.value.ip}:${testConfig.value.port}`
    })

    addLog('success', 'WebSocket连接测试通过')
  } catch (error) {
    testResults.value.push({
      name: 'WebSocket连接',
      status: 'warning',
      message: 'WebSocket连接异常',
      details: error.message
    })

    addLog('warning', 'WebSocket连接测试警告')
  }
}

const testHttpService = async () => {
  addLog('info', '测试HTTP服务...')

  try {
    // 模拟HTTP服务测试
    await new Promise(resolve => setTimeout(resolve, 800))

    testResults.value.push({
      name: 'HTTP服务',
      status: 'success',
      message: 'HTTP服务正常',
      details: `http://${testConfig.value.ip}:9004`
    })

    addLog('success', 'HTTP服务测试通过')
  } catch (error) {
    testResults.value.push({
      name: 'HTTP服务',
      status: 'error',
      message: 'HTTP服务异常',
      details: error.message
    })

    addLog('error', 'HTTP服务测试失败')
  }
}

// 事件处理方法
const closeModal = () => {
  if (!isTesting.value) {
    emit('close')
  }
}

const clearResults = () => {
  testResults.value = []
  consoleOutput.value = []
  message.success('测试结果已清除')
}

const retryTest = () => {
  startTest()
}
</script>

<style scoped>
/* OPPO 连接测试器样式 */
.oppo-modal-header {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-md);
}

.oppo-modal-title {
  font-size: var(--oppo-text-lg);
  font-weight: 600;
  color: var(--oppo-neutral-900);
}

.oppo-connection-tester {
  display: flex;
  flex-direction: column;
  gap: var(--oppo-spacing-xl);
}

/* 测试配置 */
.oppo-test-config {
  background: var(--oppo-neutral-50);
  border-radius: var(--oppo-radius-lg);
  padding: var(--oppo-spacing-lg);
}

.oppo-section-title {
  font-size: var(--oppo-text-lg);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
}

.oppo-test-actions {
  margin-top: var(--oppo-spacing-lg);
}

/* 测试结果 */
.oppo-test-results {
  background: white;
  border-radius: var(--oppo-radius-lg);
  border: 1px solid var(--oppo-neutral-200);
  overflow: hidden;
}

.oppo-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--oppo-spacing-lg);
  background: var(--oppo-neutral-50);
  border-bottom: 1px solid var(--oppo-neutral-200);
}

.oppo-results-summary {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
}

.oppo-results-list {
  padding: var(--oppo-spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--oppo-spacing-md);
}

.oppo-test-item {
  display: flex;
  align-items: flex-start;
  gap: var(--oppo-spacing-md);
  padding: var(--oppo-spacing-md);
  border-radius: var(--oppo-radius-lg);
  border: 1px solid var(--oppo-neutral-200);
  transition: all var(--oppo-duration-normal) var(--oppo-easing);
}

.oppo-test-item:hover {
  border-color: var(--oppo-primary-300);
  box-shadow: var(--oppo-shadow-sm);
}

.oppo-test-success {
  border-left: 4px solid var(--oppo-success);
}

.oppo-test-warning {
  border-left: 4px solid var(--oppo-warning);
}

.oppo-test-error {
  border-left: 4px solid var(--oppo-error);
}

.oppo-test-pending {
  border-left: 4px solid var(--oppo-neutral-300);
}

.oppo-test-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--oppo-radius-lg);
  background: var(--oppo-neutral-100);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.oppo-test-content {
  flex: 1;
}

.oppo-test-name {
  font-size: var(--oppo-text-base);
  font-weight: 600;
  color: var(--oppo-neutral-900);
  margin-bottom: var(--oppo-spacing-xs);
}

.oppo-test-message {
  font-size: var(--oppo-text-sm);
  color: var(--oppo-neutral-700);
  margin-bottom: var(--oppo-spacing-xs);
}

.oppo-test-details {
  font-size: var(--oppo-text-xs);
  color: var(--oppo-neutral-600);
  font-family: var(--oppo-font-mono);
}

.oppo-test-status {
  flex-shrink: 0;
}

/* 空状态 */
.oppo-no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--oppo-spacing-4xl);
  text-align: center;
}

.oppo-no-results-text {
  font-size: var(--oppo-text-base);
  color: var(--oppo-neutral-600);
  margin-top: var(--oppo-spacing-lg);
}

/* 模态框操作 */
.oppo-modal-actions {
  display: flex;
  gap: var(--oppo-spacing-md);
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .oppo-connection-tester {
    gap: var(--oppo-spacing-lg);
  }

  .oppo-test-config,
  .oppo-results-list {
    padding: var(--oppo-spacing-md);
  }

  .oppo-test-item {
    flex-direction: column;
    text-align: center;
  }

  .oppo-modal-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .oppo-results-header {
    flex-direction: column;
    gap: var(--oppo-spacing-sm);
    text-align: center;
  }

  .oppo-test-item {
    padding: var(--oppo-spacing-sm);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .oppo-test-item:hover {
    border-color: var(--oppo-neutral-200);
    box-shadow: none;
  }
}

/* 减少动画 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
  .oppo-test-item {
    transition: none;
  }
}
</style>
