<template>
  <n-config-provider :theme="lightTheme" :theme-overrides="themeOverrides">
    <n-message-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <div class="oppo-app">
            <!-- 顶部导航栏 - OPPO风格设计 -->
            <header class="oppo-header">
              <div class="oppo-header-container">
                <!-- Logo区域 -->
                <div class="oppo-logo-section">
                  <div class="oppo-logo-redesign">
                    <img src="@/assets/OPPOlogo.jpg" alt="OPPO Logo" />
                  </div>
                </div>

                <!-- 主导航菜单 -->
                <nav class="oppo-nav-section">
                  <div class="oppo-nav-menu">
                    <div
                      v-for="item in menuOptions"
                      :key="item.key"
                      class="oppo-nav-item"
                      :class="{ 'oppo-nav-item-active': activeMenu === item.key }"
                      @click="handleMenuSelect(item.key)"
                    >
                      <n-icon size="18" class="oppo-nav-icon">
                        <component :is="item.icon" />
                      </n-icon>
                      <span class="oppo-nav-label">{{ item.label }}</span>
                      <div v-if="activeMenu === item.key" class="oppo-nav-indicator"></div>
                    </div>
                  </div>
                </nav>

                <!-- 全屏按钮 -->
                <div class="oppo-fullscreen-btn">
                  <n-tooltip trigger="hover" placement="bottom">
                    <template #trigger>
                      <div class="oppo-tool-item" @click="toggleFullscreen">
                        <n-icon size="18" :color="isFullscreen ? 'var(--oppo-primary-500)' : 'var(--oppo-neutral-600)'">
                          <ExpandOutline v-if="!isFullscreen" />
                          <ContractOutline v-else />
                        </n-icon>
                      </div>
                    </template>
                    {{ isFullscreen ? '退出全屏' : '进入全屏' }}
                  </n-tooltip>
                </div>
              </div> <!-- 修复：添加header-container的闭合标签 -->
            </header> <!-- 修复：添加header的闭合标签 -->

            <!-- 主内容区域 -->
            <main class="oppo-main">
              <div class="oppo-content-wrapper">
                <ErrorBoundary>
                  <router-view v-slot="{ Component }">
                    <transition name="oppo-page-transition" mode="out-in">
                      <component :is="Component" />
                    </transition>
                  </router-view>
                </ErrorBoundary>
              </div>
            </main>

            <!-- 底部状态栏 (移动端) -->
            <footer class="oppo-footer oppo-desktop-hidden">
              <div class="oppo-footer-content">
                <div class="oppo-footer-info">
                  <span class="oppo-footer-text">{{ currentTime }}</span>
                  <span class="oppo-footer-separator">•</span>
                  <span class="oppo-footer-text">{{ onlineDeviceCount }} 设备在线</span>
                </div>
              </div>
            </footer>
          </div>
        </n-notification-provider> 
      </n-dialog-provider> 
    </n-message-provider> 
  </n-config-provider> 
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { lightTheme } from 'naive-ui'
import { themeOverrides } from './theme'
import {
  HomeOutline,
  PhonePortraitOutline,
  FolderOutline,
  CloudUploadOutline,
  PlayOutline,
  BarChartOutline,
  TvOutline,
  ExpandOutline,
  ContractOutline,
  EyeOutline
} from '@vicons/ionicons5'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import { useDeviceStore } from '@/store/device'

const route = useRoute()
const router = useRouter()
const deviceStore = useDeviceStore()

// 响应式状态
const activeMenu = ref(route.path)
const currentTime = ref('')
const isFullscreen = ref(false)

// 监听路由变化
watch(route, (val) => {
  activeMenu.value = val.path
})

// 菜单配置 - 添加图标
const menuOptions = [
  {
    label: '主页',
    key: '/dashboard',
    icon: HomeOutline
  },
  {
    label: '设备',
    key: '/devices',
    icon: PhonePortraitOutline
  },
  {
    label: '内容',
    key: '/contents',
    icon: FolderOutline
  },
  {
    label: '视角',
    key: '/host-view',
    icon: EyeOutline
  },
  {
    label: '控制',
    key: '/control',
    icon: PlayOutline
  },
  {
    label: '看板',
    key: '/dashboard-fetch',
    icon: BarChartOutline
  },
  {
    label: '上传',
    key: '/upload',
    icon: CloudUploadOutline
  },
  {
    label: '监控',
    key: '/monitor',
    icon: TvOutline
  }
]


// 计算属性
const onlineDeviceCount = computed(() => {
  return deviceStore.devices.filter(d => d.connected).length
})



// 时间更新
let timeInterval = null

function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 事件处理
function handleMenuSelect(key) {
  if (key !== route.path) {
    router.push(key)
  }
}



// 全屏切换
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    // 进入全屏
    document.documentElement.requestFullscreen().then(() => {
      isFullscreen.value = true
    }).catch(err => {
      console.error('进入全屏失败:', err)
    })
  } else {
    // 退出全屏
    document.exitFullscreen().then(() => {
      isFullscreen.value = false
    }).catch(err => {
      console.error('退出全屏失败:', err)
    })
  }
}

// 监听全屏状态变化
function handleFullscreenChange() {
  isFullscreen.value = !!document.fullscreenElement
}

// 添加全屏状态监听
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

// 生命周期
onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 60000) // 每分钟更新一次
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* OPPO App 主框架样式 */
.oppo-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--oppo-neutral-50);
}

/* 顶部导航栏 */
.oppo-header {
  background: white;
  border-bottom: 1px solid var(--oppo-neutral-200);
  box-shadow: var(--oppo-shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.oppo-header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--oppo-spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
}

/* Logo区域 */
.oppo-logo-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  background: none;
  margin-left: 20px; /* 仅logo区域整体右移，状态栏不动 */
}

.oppo-logo-redesign {
  width: auto;
  height: auto;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  box-shadow: none;
}

.oppo-logo-redesign img {
  width: 128px;
  height: 128px;
  object-fit: contain;
  border-radius: 0;
  background: none;
  box-shadow: none;
}

/* 移除原有 logo 相关样式 */
.oppo-logo,
.oppo-logo-image,
.logo-img {
  display: none !important;
}



/* 导航菜单 */
.oppo-nav-section {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 800px;
  margin: 0 var(--oppo-spacing-xl);
}

.oppo-nav-menu {
  display: flex;
  align-items: center;
  gap: 2px;
  background: var(--oppo-neutral-100);
  padding: 2px;
  border-radius: var(--oppo-radius-xl);
}

.oppo-nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-xs);
  padding: var(--oppo-spacing-xs) var(--oppo-spacing-md);
  border-radius: var(--oppo-radius-md);
  cursor: pointer;
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
  font-weight: 500;
  color: var(--oppo-neutral-600);
  user-select: none;
}

.oppo-nav-item:hover {
  background: white;
  color: var(--oppo-primary-500);
  box-shadow: var(--oppo-shadow-sm);
}

.oppo-nav-item-active {
  background: white;
  color: var(--oppo-primary-500);
  box-shadow: var(--oppo-shadow-md);
}

.oppo-nav-icon {
  flex-shrink: 0;
}

.oppo-nav-label {
  font-size: var(--oppo-text-sm);
  white-space: nowrap;
}

.oppo-nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--oppo-gradient-primary);
  border-radius: var(--oppo-radius-full);
}

/* 右侧工具栏 */
.oppo-tools-section {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-lg);
  flex-shrink: 0;
}

.oppo-status-indicators {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-md);
}

.oppo-device-status,
.oppo-system-status {
  display: flex;
  align-items: center;
}

.oppo-status-item {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
  padding: var(--oppo-spacing-sm) var(--oppo-spacing-md);
  border-radius: var(--oppo-radius-lg);
  background: var(--oppo-neutral-100);
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
}

.oppo-status-item:hover {
  background: var(--oppo-neutral-200);
}

.oppo-status-text {
  font-size: var(--oppo-text-sm);
  font-weight: 500;
  color: var(--oppo-neutral-700);
}

.oppo-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--oppo-neutral-400);
  transition: background-color var(--oppo-duration-fast) var(--oppo-easing);
}

.oppo-status-online .oppo-status-dot {
  background: var(--oppo-success);
  box-shadow: 0 0 0 2px rgba(6, 182, 56, 0.2);
}

.oppo-status-warning .oppo-status-dot {
  background: var(--oppo-warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.oppo-status-offline .oppo-status-dot {
  background: var(--oppo-neutral-400);
}

/* 全屏按钮 */
.oppo-fullscreen-btn {
  display: flex;
  align-items: center;
}

.oppo-tool-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--oppo-radius-md);
  cursor: pointer;
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
  background: transparent;
}

.oppo-tool-item:hover {
  background: var(--oppo-neutral-100);
  transform: scale(1.05);
}

.oppo-tool-item:active {
  transform: scale(0.95);
}

.oppo-user-menu {
  cursor: pointer;
  transition: transform var(--oppo-duration-fast) var(--oppo-easing);
}

.oppo-user-menu:hover {
  transform: scale(1.05);
}

/* 主内容区域 */
.oppo-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.oppo-content-wrapper {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--oppo-spacing-xl) var(--oppo-spacing-lg);
  width: 100%;
}

/* 底部状态栏 */
.oppo-footer {
  background: white;
  border-top: 1px solid var(--oppo-neutral-200);
  padding: var(--oppo-spacing-md) var(--oppo-spacing-lg);
}

.oppo-footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.oppo-footer-info {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-sm);
}

.oppo-footer-text {
  font-size: var(--oppo-text-sm);
  color: var(--oppo-neutral-600);
}

.oppo-footer-separator {
  color: var(--oppo-neutral-400);
}

/* 页面过渡动画 */
.oppo-page-transition-enter-active,
.oppo-page-transition-leave-active {
  transition: all var(--oppo-duration-normal) var(--oppo-easing);
}

.oppo-page-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.oppo-page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
/* 平板横屏 (1024px - 1280px) */
@media (min-width: 1024px) and (max-width: 1280px) {
  .oppo-header-container {
    padding: 0 var(--oppo-spacing-md);
  }

  .oppo-nav-section {
    margin: 0 var(--oppo-spacing-lg);
  }

  .oppo-nav-item {
    padding: var(--oppo-spacing-sm) var(--oppo-spacing-md);
  }

  .oppo-content-wrapper {
    padding: var(--oppo-spacing-lg) var(--oppo-spacing-md);
  }
}

/* 平板竖屏 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .oppo-header-container {
    height: 64px;
    padding: 0 var(--oppo-spacing-md);
  }

  .oppo-logo {
    gap: var(--oppo-spacing-sm);
  }

  .oppo-logo-image {
    width: 112px;
    height: 56px;
  }



  .oppo-nav-section {
    margin: 0 var(--oppo-spacing-md);
  }

  .oppo-nav-menu {
    gap: var(--oppo-spacing-xs);
    padding: 2px;
  }

  .oppo-nav-item {
    padding: var(--oppo-spacing-xs) var(--oppo-spacing-sm);
  }

  .oppo-nav-label {
    font-size: var(--oppo-text-xs);
  }

  .oppo-status-indicators {
    gap: var(--oppo-spacing-sm);
  }

  .oppo-content-wrapper {
    padding: var(--oppo-spacing-md);
  }
}

/* 手机横屏 (480px - 768px) */
@media (min-width: 480px) and (max-width: 768px) {
  .oppo-header-container {
    height: 56px;
    padding: 0 var(--oppo-spacing-sm);
  }



  .oppo-nav-section {
    margin: 0 var(--oppo-spacing-sm);
  }

  .oppo-nav-menu {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .oppo-nav-menu::-webkit-scrollbar {
    display: none;
  }

  .oppo-nav-item {
    flex-shrink: 0;
    padding: var(--oppo-spacing-xs) var(--oppo-spacing-sm);
  }

  .oppo-nav-label {
    display: none;
  }

  .oppo-tools-section {
    gap: var(--oppo-spacing-sm);
  }

  .oppo-content-wrapper {
    padding: var(--oppo-spacing-sm);
  }
}

/* 手机竖屏 (< 480px) */
@media (max-width: 480px) {
  .oppo-header-container {
    height: 56px;
    padding: 0 var(--oppo-spacing-sm);
  }

  .oppo-logo {
    gap: var(--oppo-spacing-xs);
  }

  .oppo-logo-image {
    width: 96px;
    height: 48px;
  }



  .oppo-nav-section {
    margin: 0 var(--oppo-spacing-xs);
  }

  .oppo-nav-menu {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    gap: 2px;
  }

  .oppo-nav-menu::-webkit-scrollbar {
    display: none;
  }

  .oppo-nav-item {
    flex-shrink: 0;
    padding: var(--oppo-spacing-xs);
    min-width: 44px;
    justify-content: center;
  }

  .oppo-nav-label {
    display: none;
  }

  .oppo-tools-section {
    gap: var(--oppo-spacing-xs);
  }

  .oppo-status-indicators {
    gap: var(--oppo-spacing-xs);
  }

  .oppo-content-wrapper {
    padding: var(--oppo-spacing-sm);
  }

  .oppo-footer {
    padding: var(--oppo-spacing-sm);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .oppo-nav-item {
    min-height: 44px;
    min-width: 44px;
  }

  .oppo-status-item {
    min-height: 44px;
    padding: var(--oppo-spacing-sm) var(--oppo-spacing-md);
  }

  .oppo-user-menu {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 禁用悬停效果 */
  .oppo-nav-item:hover,
  .oppo-status-item:hover,
  .oppo-user-menu:hover {
    transform: none;
    background: inherit;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .oppo-logo-image {
    box-shadow: var(--oppo-shadow-lg);
  }

  .oppo-nav-item-active {
    box-shadow: var(--oppo-shadow-lg);
  }
}

/* 深色模式支持 (预留) */
@media (prefers-color-scheme: dark) {
  /* 深色模式样式将在后续版本中添加 */
}

/* 减少动画 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
  .oppo-nav-item,
  .oppo-status-item,
  .oppo-user-menu,
  .oppo-page-transition-enter-active,
  .oppo-page-transition-leave-active {
    transition: none;
  }

  .oppo-page-transition-enter-from,
  .oppo-page-transition-leave-to {
    transform: none;
  }
}
</style>