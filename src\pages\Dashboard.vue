<template>
  <div class="oppo-dashboard">
    <!-- 欢迎横幅 -->
    <section class="oppo-hero-section">
      <div class="oppo-hero-content">
        <div class="oppo-hero-text">
          <h1 class="oppo-hero-title">
            <span class="oppo-gradient-text">欢迎使用</span>
            <br>
            OPPO 控制中心
          </h1>
          <p class="oppo-hero-subtitle">
            统一管理您的设备和内容，享受智能化的控制体验
          </p>
          <div class="oppo-hero-stats">
            <div class="oppo-stat-item">
              <span class="oppo-stat-number">{{ onlineDevices }}</span>
              <span class="oppo-stat-label">设备在线</span>
            </div>
            <div class="oppo-stat-divider"></div>
            <div class="oppo-stat-item">
              <span class="oppo-stat-number">{{ totalFiles }}</span>
              <span class="oppo-stat-label">内容文件</span>
            </div>
            <div class="oppo-stat-divider"></div>
            <div class="oppo-stat-item">
              <span class="oppo-stat-number">{{ playingDevices }}</span>
              <span class="oppo-stat-label">正在播放</span>
            </div>
          </div>
        </div>
        <div class="oppo-hero-visual">
          <div class="oppo-hero-image">
            <div class="oppo-floating-card oppo-card-1">
              <n-icon size="24" color="var(--oppo-primary-500)">
                <PhonePortraitOutline />
              </n-icon>
            </div>
            <div class="oppo-floating-card oppo-card-2">
              <n-icon size="24" color="var(--oppo-secondary-500)">
                <PlayOutline />
              </n-icon>
            </div>
            <div class="oppo-floating-card oppo-card-3">
              <n-icon size="24" color="var(--oppo-primary-400)">
                <FolderOutline />
              </n-icon>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useDeviceStore } from '@/store/device'
import {
  PhonePortraitOutline,
  FolderOutline,
  PlayOutline
} from '@vicons/ionicons5'

const deviceStore = useDeviceStore()

// 响应式数据
const fileStats = ref({
  total: 0,
  video: 0,
  image: 0,
  audio: 0,
  document: 0,
  other: 0
})

// 计算属性
const totalDevices = computed(() => deviceStore.devices.length)
const onlineDevices = computed(() => deviceStore.devices.filter(d => d.connected).length)
const totalFiles = computed(() => fileStats.value.total)
const playingDevices = computed(() => deviceStore.devices.filter(d => d.connected && d.status === 'playing').length)
</script>

<style scoped>
/* OPPO Dashboard 样式 */
.oppo-dashboard {
  min-height: 100vh;
  background: var(--oppo-neutral-50);
}

/* 欢迎横幅区域 */
.oppo-hero-section {
  background: linear-gradient(135deg, white 0%, var(--oppo-neutral-50) 100%);
  padding: var(--oppo-spacing-3xl) 0;
  margin-bottom: var(--oppo-spacing-2xl);
  position: relative;
  overflow: hidden;
}

.oppo-hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: var(--oppo-gradient-light);
  opacity: 0.5;
  z-index: 0;
}

.oppo-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--oppo-spacing-3xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.oppo-hero-text {
  padding-left: var(--oppo-spacing-lg);
}

.oppo-hero-title {
  font-size: var(--oppo-text-5xl);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--oppo-spacing-lg);
  color: var(--oppo-neutral-900);
}

.oppo-hero-subtitle {
  font-size: var(--oppo-text-xl);
  color: var(--oppo-neutral-600);
  margin-bottom: var(--oppo-spacing-2xl);
  line-height: 1.6;
}

.oppo-hero-stats {
  display: flex;
  align-items: center;
  gap: var(--oppo-spacing-lg);
}

.oppo-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.oppo-stat-number {
  font-size: var(--oppo-text-3xl);
  font-weight: 700;
  color: var(--oppo-primary-500);
  line-height: 1;
}

.oppo-stat-label {
  font-size: var(--oppo-text-sm);
  color: var(--oppo-neutral-600);
  margin-top: var(--oppo-spacing-xs);
}

.oppo-stat-divider {
  width: 1px;
  height: 40px;
  background: var(--oppo-neutral-300);
}

/* 视觉元素 */
.oppo-hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.oppo-hero-image {
  position: relative;
  width: 300px;
  height: 300px;
}

.oppo-floating-card {
  position: absolute;
  width: 80px;
  height: 80px;
  background: white;
  border-radius: var(--oppo-radius-xl);
  box-shadow: var(--oppo-shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float 6s ease-in-out infinite;
}

.oppo-card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.oppo-card-2 {
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.oppo-card-3 {
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
</style>
