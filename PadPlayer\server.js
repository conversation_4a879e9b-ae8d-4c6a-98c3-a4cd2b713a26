const WebSocket = require('ws')
const fs = require('fs')
const path = require('path')
const http = require('http')
const { exec } = require('child_process')  // 引入child_process模块
const KeySimulator = require('./scripts/keySimulator')  // 引入按键模拟器

// 配置
const WS_PORT = 9000 // 控制端端口
const PLAYER_WS_PORT = 9001 // 播放器端口
const HTTP_PORT = 9004
const SHARED_DIR = path.join(__dirname, 'shared')

// 初始化按键模拟器
const keySimulator = new KeySimulator()

// 创建共享目录（如果不存在）
if (!fs.existsSync(SHARED_DIR)) {
  fs.mkdirSync(SHARED_DIR, { recursive: true })
  console.log(`创建共享目录: ${SHARED_DIR}`)
}

// 控制端WebSocket服务（接收Pad控制命令）
const wss = new WebSocket.Server({ port: WS_PORT }, () => {
  console.log(`控制端WebSocket服务启动成功，端口: ${WS_PORT}`)
})

// 播放器WebSocket服务（转发命令给播放器）
const playerWss = new WebSocket.Server({ port: PLAYER_WS_PORT }, () => {
  console.log(`播放器WebSocket服务启动成功，端口: ${PLAYER_WS_PORT}`)
})

// 连接状态管理
let padSocket = null // 控制端连接
let playerSocket = null // 播放器连接

// 处理控制端连接
wss.on('connection', (ws) => {
  padSocket = ws
  console.log('控制端已连接（Pad）')

  ws.on('message', (msg) => {
    console.log('\n收到控制端消息:', msg.toString())
    try {
      const data = JSON.parse(msg.toString())
      handleJsonMessage(data)
    } catch (e) {
      console.log('解析为JSON失败，按文本命令处理')
      handleTextMessage(msg.toString())
    }
  })

  ws.on('close', () => {
    console.log('控制端已断开连接')
    padSocket = null
  })

  ws.on('error', (err) => {
    console.error('控制端连接错误:', err)
  })
})

// 处理播放器连接
playerWss.on('connection', (ws) => {
  playerSocket = ws
  console.log('播放器已连接')

  ws.on('close', () => {
    console.log('播放器已断开连接')
    playerSocket = null
  })

  ws.on('error', (err) => {
    console.error('播放器连接错误:', err)
  })
})

// 处理JSON格式命令
async function handleJsonMessage(data) {
  const validTypes = ['listFiles', 'pushContent', 'video', 'image', 'html', 'play', 'pause', 'stop', 'volume', 'fullscreen', 'single', 'sequential', 'loop','unmute', 'openWithLocalApp', 'getSharedPath', 'heartbeat', 'keySimulate', 'autoPlay', 'setVolume', 'toggleFullscreen', 'testLoop', 'scheduleShutdown', 'scheduleStartup', 'cancelSchedule', 'mute', 'broadcast', 'fetchDashboard', 'pushDashboard', 'unityViewControl']  // 添加Unity视角控制类型

  // 处理心跳消息
  if (data.type === 'heartbeat') {
    console.log('💓 收到心跳消息:', data.id || 'no-id')
    // 立即回复心跳响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'heartbeat',
        id: data.id,
        timestamp: data.timestamp,
        serverTime: Date.now()
      }))
    }
    return
  }

  if (!validTypes.includes(data.type)) {
    console.log('忽略未知类型命令:', data.type)
    return
  }

  // 处理获取共享路径请求
  if (data.type === 'getSharedPath') {
    console.log('收到获取共享路径请求')
    try {
      // 向控制端返回共享路径
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'sharedPathResponse',
          sharedPath: SHARED_DIR
        }))
        console.log(`共享路径返回成功：${SHARED_DIR}`)
      }
    } catch (err) {
      console.error('返回共享路径失败:', err)
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'error',
          message: '获取共享路径失败',
          details: err.message
        }))
      }
    }
    return
  }
  // 处理文件列表请求
  if (data.type === 'listFiles') {
    console.log('收到文件列表请求，扫描共享目录...')
    try {
      // 读取共享目录文件并获取详细信息
      const fileNames = fs.readdirSync(SHARED_DIR)
      const files = fileNames.map(name => {
        const filePath = path.join(SHARED_DIR, name)
        const stats = fs.statSync(filePath)

        // 获取文件扩展名
        const ext = path.extname(name).toLowerCase()

        // 确定文件类型
        let fileType = 'other'
        if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'].includes(ext)) {
          fileType = 'video'
        } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(ext)) {
          fileType = 'image'
        } else if (['.html', '.htm'].includes(ext)) {
          fileType = 'html'
        } else if (['.mp3', '.wav', '.flac', '.aac', '.ogg'].includes(ext)) {
          fileType = 'audio'
        } else if (['.txt', '.log'].includes(ext)) {
          fileType = 'text'
        } else if (['.pdf'].includes(ext)) {
          fileType = 'pdf'
        } else if (['.doc', '.docx'].includes(ext)) {
          fileType = 'word'
        } else if (['.xls', '.xlsx'].includes(ext)) {
          fileType = 'excel'
        } else if (['.ppt', '.pptx'].includes(ext)) {
          fileType = 'ppt'
        }

        return {
          name,
          path: `http://192.168.132.204:${HTTP_PORT}/shared/${encodeURIComponent(name)}`,
          type: fileType,
          size: stats.size,
          modified: stats.mtime.toISOString(),
          created: stats.birthtime.toISOString(),
          extension: ext.substring(1) // 去掉点号
        }
      })
      
      // 按文件类型和名称排序
      files.sort((a, b) => {
        // 首先按类型排序
        const typeOrder = ['video', 'image', 'audio', 'html', 'pdf', 'word', 'excel', 'ppt', 'text', 'other']
        const typeA = typeOrder.indexOf(a.type)
        const typeB = typeOrder.indexOf(b.type)

        if (typeA !== typeB) {
          return typeA - typeB
        }

        // 同类型按名称排序
        return a.name.localeCompare(b.name)
      })

      // 筛选有效文件类型（保持向后兼容）
      const filteredFiles = {
        video: files.filter(f => f.type === 'video'),
        image: files.filter(f => f.type === 'image'),
        audio: files.filter(f => f.type === 'audio'),
        html: files.filter(f => f.type === 'html'),
        ppt: files.filter(f => f.type === 'ppt'),
        text: files.filter(f => f.type === 'text'),
        word: files.filter(f => f.type === 'word'),
        pdf: files.filter(f => f.type === 'pdf'),
        excel: files.filter(f => f.type === 'excel'),
        other: files.filter(f => f.type === 'other')
      }

      // 计算统计信息
      const totalSize = files.reduce((sum, file) => sum + file.size, 0)
      const typeStats = Object.keys(filteredFiles).map(type => ({
        type,
        count: filteredFiles[type].length,
        size: filteredFiles[type].reduce((sum, file) => sum + file.size, 0)
      })).filter(stat => stat.count > 0)

      // 向控制端返回文件列表
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'fileList',
          files: filteredFiles,
          allFiles: files, // 添加完整文件列表
          totalFiles: files.length,
          totalSize: totalSize,
          statistics: typeStats
        }))

        const statsText = typeStats.map(stat => `${stat.type}${stat.count}个`).join('，')
        console.log(`文件列表返回成功：共${files.length}个文件，总大小${(totalSize / 1024 / 1024).toFixed(2)}MB，${statsText}`)
      }
    } catch (err) {
      console.error('读取共享目录失败:', err)
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'error',
          message: '获取文件列表失败',
          details: err.message
        }))
      }
    }
    return
  }

  // 处理 pushContent 命令
  if (data.type === 'pushContent') {
    console.log('收到推送内容命令:', data.content)

    // 验证内容有效性
    if (!data.content || !data.content.type || !data.content.path) {
      console.error('推送内容无效，缺少必要字段')
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'error',
          message: '推送内容无效，缺少 type 或 path 字段'
        }))
      }
      return
    }

    // 自动打开播放器页面
    try {
      const playerUrl = `http://localhost:${HTTP_PORT}/player.html`
      console.log('自动打开播放器页面:', playerUrl)

      // 使用start命令打开播放器页面
      exec(`start "" "${playerUrl}"`, (error) => {
        if (error) {
          console.error('打开播放器页面失败:', error)
        } else {
          console.log('播放器页面已打开')

          // 播放器打开后，延迟触发全屏
          setTimeout(async () => {
            try {
              console.log('触发浏览器全屏...')
              const fullscreenResult = await keySimulator.triggerBrowserFullscreen()
              console.log('浏览器全屏结果:', fullscreenResult)
            } catch (error) {
              console.error('触发浏览器全屏失败:', error)
            }
          }, 2000) // 等待2秒让浏览器完全加载
        }
      })

      // 等待播放器连接后再发送内容
      setTimeout(() => {
        sendContentToPlayer(data.content)
      }, 3000) // 等待3秒让播放器加载和连接

    } catch (err) {
      console.error('打开播放器失败:', err)
      sendContentToPlayer(data.content) // 如果打开失败，仍然尝试发送内容
    }

    return
  }

  // 发送内容到播放器的函数
  function sendContentToPlayer(content) {
    // 检查播放器连接状态
    if (!playerSocket || playerSocket.readyState !== WebSocket.OPEN) {
      console.warn('播放器未连接，等待连接...')

      // 等待播放器连接，最多等待10秒
      let attempts = 0
      const maxAttempts = 20 // 10秒，每500ms检查一次

      const waitForConnection = setInterval(() => {
        attempts++

        if (playerSocket && playerSocket.readyState === WebSocket.OPEN) {
          clearInterval(waitForConnection)
          console.log('播放器已连接，发送内容')
          sendContentNow(content)
        } else if (attempts >= maxAttempts) {
          clearInterval(waitForConnection)
          console.error('等待播放器连接超时')
          if (padSocket && padSocket.readyState === WebSocket.OPEN) {
            padSocket.send(JSON.stringify({
              type: 'error',
              message: '播放器连接超时，请手动打开播放器页面'
            }))
          }
        }
      }, 500)

      return
    }

    sendContentNow(content)
  }

  // 立即发送内容到播放器
  function sendContentNow(content) {
    try {
      // 重构消息格式，适配播放器期望的格式
      const playerMessage = {
        type: content.type,  // 直接使用内容类型（video/image/html）
        src: content.path,   // 使用 src 字段传递路径
        autoFullscreen: true // 标记需要自动全屏
      }

      playerSocket.send(JSON.stringify(playerMessage))
      console.log('内容已推送到播放器:', playerMessage)

      // 反馈控制端：推送成功
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'pushContentResponse',
          success: true,
          content: content
        }))
      }
    } catch (err) {
      console.error('推送内容失败:', err)
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'error',
          message: '推送内容失败',
          details: err.message
        }))
      }
    }
  }

  // 处理 openWithLocalApp 命令
  if (data.type === 'openWithLocalApp') {
    console.log('收到打开本地应用命令:', data)
    
    // 验证参数
    if (!data.filePath || !data.appType) {
      console.error('打开本地应用参数无效')
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'error',
          message: '打开本地应用参数无效，缺少filePath或appType'
        }))
      }
      return
    }
    
    // 调用本地应用打开文件，并自动执行按键模拟
    openFileWithApp(data.filePath, data.appType, async (err) => {
      if (err) {
        console.error('打开本地应用失败:', err)
        if (padSocket && padSocket.readyState === WebSocket.OPEN) {
          padSocket.send(JSON.stringify({
            type: 'error',
            message: '打开本地应用失败',
            details: err.message
          }))
        }
      } else {
        console.log('成功打开本地应用，开始自动化处理')

        // 直接报告应用打开成功，自动化处理由openLocalApp方法内部完成
        if (padSocket && padSocket.readyState === WebSocket.OPEN) {
          padSocket.send(JSON.stringify({
            type: 'openWithLocalAppResponse',
            success: true,
            message: '成功打开本地应用',
            filePath: data.filePath,
            appType: data.appType
          }))
        }
      }
    })
    return
  }

  // 处理 testLoop 命令
  if (data.type === 'testLoop') {
    console.log('收到循环播放测试命令')

    try {
      const result = await keySimulator.testLoop()

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'testLoopResponse',
          success: result.success,
          message: result.message
        }))
      }
    } catch (error) {
      console.error('循环播放测试失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'testLoopResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 处理Unity视角控制命令
  if (data.type === 'unityViewControl') {
    console.log('收到Unity视角控制命令:', data)

    try {
      const { hostId, viewKey, viewName } = data

      if (!viewKey) {
        throw new Error('缺少视角键参数')
      }

      console.log(`主机${hostId}切换到视角: ${viewName} (${viewKey})`)

      // 调用按键模拟器执行视角切换
      const result = await keySimulator.unityViewControl(viewKey)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'unityViewControlResponse',
          success: result.success,
          message: result.message,
          hostId: hostId,
          viewKey: viewKey,
          viewName: viewName,
          keyAction: result.keyAction
        }))
      }

      console.log(`Unity视角控制完成: ${result.message}`)

    } catch (error) {
      console.error('Unity视角控制失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'unityViewControlResponse',
          success: false,
          message: error.message,
          hostId: data.hostId,
          viewKey: data.viewKey
        }))
      }
    }
    return
  }

  // 处理定时关机命令
  if (data.type === 'scheduleShutdown') {
    console.log('收到定时关机命令:', data.time)

    try {
      // 这里可以使用Windows的shutdown命令
      const shutdownCommand = `shutdown /s /t ${getSecondsUntilTime(data.time)}`
      console.log('执行关机命令:', shutdownCommand)

      // 实际执行关机命令（注释掉以防意外关机）
      // exec(shutdownCommand)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'scheduleShutdownResponse',
          success: true,
          message: `已设置在 ${data.time} 关机`
        }))
      }
    } catch (error) {
      console.error('设置定时关机失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'scheduleShutdownResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 处理定时开机命令
  if (data.type === 'scheduleStartup') {
    console.log('收到定时开机命令:', data.time)

    try {
      // 定时开机通常需要BIOS支持或者使用任务计划程序
      console.log('设置定时开机:', data.time)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'scheduleStartupResponse',
          success: true,
          message: `已设置在 ${data.time} 开机（需要BIOS支持）`
        }))
      }
    } catch (error) {
      console.error('设置定时开机失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'scheduleStartupResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 处理取消定时任务命令
  if (data.type === 'cancelSchedule') {
    console.log('收到取消定时任务命令')

    try {
      // 取消Windows的shutdown命令
      exec('shutdown /a')

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'cancelScheduleResponse',
          success: true,
          message: '已取消定时任务'
        }))
      }
    } catch (error) {
      console.error('取消定时任务失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'cancelScheduleResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 处理静音命令
  if (data.type === 'mute') {
    console.log('收到静音命令')

    try {
      const result = await keySimulator.mediaControl('mute')

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'muteResponse',
          success: true,
          message: '已设置静音'
        }))
      }
    } catch (error) {
      console.error('静音失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'muteResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 处理看板获取命令
  if (data.type === 'fetchDashboard') {
    console.log('收到看板获取命令:', data)
    handleFetchDashboard(data)
    return
  }

  // 处理看板推送命令
  if (data.type === 'pushDashboard') {
    console.log('收到看板推送命令:', data)
    handlePushDashboard(data)
    return
  }

  // 处理广播命令
  if (data.type === 'broadcast') {
    console.log('收到广播命令:', data.message)

    try {
      // 显示系统通知或者弹窗
      console.log(`广播消息 [${data.messageType || 'info'}]: ${data.message}`)
      console.log(`显示时长: ${data.duration}ms`)

      // 检查播放器是否连接
      if (!playerSocket || playerSocket.readyState !== WebSocket.OPEN) {
        console.log('播放器未连接，自动打开播放器页面...')

        // 自动打开播放器页面
        const playerUrl = `http://localhost:${HTTP_PORT}/player.html`
        console.log('自动打开播放器页面:', playerUrl)

        exec(`start "" "${playerUrl}"`, (error) => {
          if (error) {
            console.error('打开播放器页面失败:', error)
          } else {
            console.log('播放器页面已打开')

            // 播放器打开后，延迟触发全屏
            setTimeout(async () => {
              try {
                console.log('触发浏览器全屏...')
                const fullscreenResult = await keySimulator.triggerBrowserFullscreen()
                console.log('浏览器全屏结果:', fullscreenResult)
              } catch (error) {
                console.error('触发浏览器全屏失败:', error)
              }
            }, 2000) // 等待2秒让浏览器完全加载
          }
        })

        // 等待播放器连接后再发送广播消息
        setTimeout(() => {
          sendBroadcastToPlayer(data)
        }, 3000) // 等待3秒让播放器加载和连接
      } else {
        // 播放器已连接，直接发送广播消息
        sendBroadcastToPlayer(data)
      }

      // 立即返回成功响应给控制端
      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'broadcastResponse',
          success: true,
          message: '广播消息已发送'
        }))
      }
    } catch (error) {
      console.error('广播失败:', error)

      if (padSocket && padSocket.readyState === WebSocket.OPEN) {
        padSocket.send(JSON.stringify({
          type: 'broadcastResponse',
          success: false,
          message: error.message
        }))
      }
    }
    return
  }

  // 发送广播消息到播放器的函数
  function sendBroadcastToPlayer(broadcastData) {
    // 检查播放器连接状态
    if (!playerSocket || playerSocket.readyState !== WebSocket.OPEN) {
      console.warn('播放器未连接，等待连接...')

      // 等待播放器连接，最多等待10秒
      let attempts = 0
      const maxAttempts = 20 // 10秒，每500ms检查一次

      const waitForConnection = setInterval(() => {
        attempts++

        if (playerSocket && playerSocket.readyState === WebSocket.OPEN) {
          clearInterval(waitForConnection)
          console.log('播放器已连接，发送广播消息')
          sendBroadcastNow(broadcastData)
        } else if (attempts >= maxAttempts) {
          clearInterval(waitForConnection)
          console.error('等待播放器连接超时')
        }
      }, 500)

      return
    }

    sendBroadcastNow(broadcastData)
  }

  // 立即发送广播消息到播放器
  function sendBroadcastNow(broadcastData) {
    try {
      playerSocket.send(JSON.stringify({
        type: 'showNotification',
        message: broadcastData.message,
        messageType: broadcastData.messageType || 'warning',
        duration: broadcastData.duration || 0 // 0表示永久显示
      }))
      console.log('广播消息已转发给播放器')
    } catch (err) {
      console.error('发送广播消息失败:', err)
    }
  }

  // 处理按键模拟命令
  if (data.type === 'keySimulate') {
    console.log('收到按键模拟命令:', data)
    handleKeySimulateCommand(data)
    return
  }

  // 处理自动播放命令
  if (data.type === 'autoPlay') {
    console.log('收到自动播放命令:', data)
    handleAutoPlayCommand(data)
    return
  }

  // 处理音量设置命令
  if (data.type === 'setVolume') {
    console.log('收到音量设置命令:', data)
    handleVolumeCommand(data)
    return
  }

  // 处理全屏切换命令
  if (data.type === 'toggleFullscreen') {
    console.log('收到全屏切换命令:', data)
    handleFullscreenCommand(data)
    return
  }

  // 检查播放器连接状态
  if (!playerSocket || playerSocket.readyState !== WebSocket.OPEN) {
    console.warn('播放器未连接，无法转发命令')
    padSocket?.send(JSON.stringify({
      type: 'error',
      message: '播放器未连接'
    }))
    return
  }

  // 转发其他命令给播放器
  try {
    playerSocket.send(JSON.stringify(data))
    console.log('命令已转发给播放器:', data)
    padSocket?.send(JSON.stringify({
      type: 'commandForwarded',
      success: true,
      command: data
    }))
  } catch (err) {
    console.error('转发命令失败:', err)
  }
}

// 处理文本格式命令（如"play"）
function handleTextMessage(text) {
  const commandMap = {
    'play': { type: 'play' },
    'pause': { type: 'pause' },
    'stop': { type: 'stop' },
    'fullscreen': { type: 'fullscreen' },
    'single': { type: 'single' },
    'sequential': { type: 'sequential' },
    'loop': { type: 'loop' },
    'unmute': { type: 'unmute' }
  }

  const command = commandMap[text.trim().toLowerCase()]
  if (command) {
    handleJsonMessage(command) // 复用JSON命令处理逻辑
  } else {
    console.log('未知文本命令:', text)
    padSocket?.send(JSON.stringify({
      type: 'error',
      message: '未知命令: ' + text
    }))
  }
}

// HTTP服务（提供播放器页面和共享文件）
const httpServer = http.createServer((req, res) => {
  // 允许跨域（避免文件访问时的跨域问题）
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  // 处理OPTIONS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    return res.end()
  }

  // 处理文件上传
  if (req.url === '/upload' && req.method === 'POST') {
    handleFileUpload(req, res)
    return
  }

  if (req.url === '/' || req.url === '/player.html') {
    const playerPath = path.join(__dirname, 'player.html')
    fs.readFile(playerPath, (err, data) => {
      if (err) {
        res.writeHead(404)
        return res.end('播放器页面未找到')
      }
      res.writeHead(200, { 'Content-Type': 'text/html' })
      res.end(data)
    })
  } else if (req.url.startsWith('/shared/')) {
    // 处理共享文件请求
    let fileName = req.url.replace('/shared/', '')

    // 处理URL编码，避免双重编码问题
    try {
      fileName = decodeURIComponent(fileName)
    } catch (e) {
      console.log('文件名解码失败:', fileName)
    }

    const filePath = path.join(SHARED_DIR, fileName)
    console.log('请求文件:', fileName, '完整路径:', filePath)

    // 安全校验：防止访问共享目录外的文件
    if (!filePath.startsWith(SHARED_DIR)) {
      res.writeHead(403)
      return res.end('禁止访问')
    }
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404)
        return res.end('文件未找到')
      }
      // 设置正确的MIME类型
      const ext = path.extname(filePath).toLowerCase()
      const mimeTypes = {
        '.mp4': 'video/mp4',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.html': 'text/html',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.txt': 'text/plain',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.pdf': 'application/pdf',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
      const mimeType = mimeTypes[ext] || 'application/octet-stream'
      res.writeHead(200, { 'Content-Type': mimeType })
      res.end(data)
    })
  } else if (req.url.startsWith('/preview/')) {
    // 处理文件预览请求
    let fileName = req.url.replace('/preview/', '')

    // 处理URL编码，避免双重编码问题
    try {
      fileName = decodeURIComponent(fileName)
    } catch (e) {
      console.log('预览文件名解码失败:', fileName)
    }

    const fullPath = path.join(SHARED_DIR, fileName)
    console.log('请求预览:', fileName, '完整路径:', fullPath)

    // 安全校验
    if (!fullPath.startsWith(SHARED_DIR)) {
      res.writeHead(403)
      return res.end('禁止访问')
    }

    fs.readFile(fullPath, (err, data) => {
      if (err) {
        res.writeHead(404)
        return res.end('文件未找到')
      }

      // 设置MIME类型
      const ext = path.extname(fullPath).toLowerCase()
      const mimeTypes = {
        '.mp4': 'video/mp4',
        '.webm': 'video/webm',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.html': 'text/html',
        '.htm': 'text/html',
        '.pdf': 'application/pdf'
      }
      const mimeType = mimeTypes[ext] || 'application/octet-stream'

      res.writeHead(200, {
        'Content-Type': mimeType,
        'Cache-Control': 'public, max-age=3600'
      })
      res.end(data)
    })
  } else if (req.url.startsWith('/thumbnail/')) {
    // 处理视频缩略图请求
    let fileName = req.url.replace('/thumbnail/', '')

    // 处理URL编码，避免双重编码问题
    try {
      fileName = decodeURIComponent(fileName)
    } catch (e) {
      console.log('缩略图文件名解码失败:', fileName)
    }

    const fullPath = path.join(SHARED_DIR, fileName)
    console.log('请求缩略图:', fileName, '完整路径:', fullPath)

    // 安全校验
    if (!fullPath.startsWith(SHARED_DIR)) {
      res.writeHead(403)
      return res.end('禁止访问')
    }

    // 简单实现：对于视频文件，返回一个默认的缩略图
    // 在实际项目中，可以使用ffmpeg生成真实的视频缩略图
    const ext = path.extname(fullPath).toLowerCase()
    if (['.mp4', '.webm', '.avi', '.mov', '.mkv', '.flv'].includes(ext)) {
      // 检查原视频文件是否存在
      fs.access(fullPath, fs.constants.F_OK, (err) => {
        if (err) {
          res.writeHead(404)
          return res.end('视频文件未找到')
        }

        // 返回一个简单的SVG作为视频缩略图
        const svgThumbnail = `
          <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="150" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <circle cx="100" cy="75" r="25" fill="#007AFF"/>
            <polygon points="90,60 90,90 115,75" fill="white"/>
            <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
              视频文件
            </text>
          </svg>
        `
        res.writeHead(200, { 'Content-Type': 'image/svg+xml' })
        res.end(svgThumbnail)
      })
    } else {
      // 非视频文件，返回原文件
      fs.readFile(fullPath, (err, data) => {
        if (err) {
          res.writeHead(404)
          return res.end('文件未找到')
        }

        const mimeTypes = {
          '.jpg': 'image/jpeg',
          '.jpeg': 'image/jpeg',
          '.png': 'image/png',
          '.gif': 'image/gif',
          '.webp': 'image/webp'
        }
        const mimeType = mimeTypes[ext] || 'application/octet-stream'

        res.writeHead(200, { 'Content-Type': mimeType })
        res.end(data)
      })
    }
  } else if (req.url.startsWith('/proxy/')) {
    // 处理资源代理请求
    handleResourceProxy(req, res)
  } else {
    res.writeHead(404)
    res.end('页面未找到')
  }
})

// 处理文件上传
function handleFileUpload(req, res) {
  console.log('收到文件上传请求')

  let body = Buffer.alloc(0)
  let boundary = null

  // 获取boundary
  const contentType = req.headers['content-type']
  if (contentType && contentType.includes('multipart/form-data')) {
    const boundaryMatch = contentType.match(/boundary=(.+)$/)
    if (boundaryMatch) {
      boundary = '--' + boundaryMatch[1]
    }
  }

  if (!boundary) {
    res.writeHead(400, { 'Content-Type': 'application/json' })
    return res.end(JSON.stringify({ error: '无效的Content-Type' }))
  }

  req.on('data', chunk => {
    body = Buffer.concat([body, chunk])
  })

  req.on('end', () => {
    try {
      const files = parseMultipartData(body, boundary)

      if (files.length === 0) {
        res.writeHead(400, { 'Content-Type': 'application/json' })
        return res.end(JSON.stringify({ error: '没有找到文件' }))
      }

      // 保存文件到共享目录
      const savedFiles = []

      files.forEach(file => {
        const filePath = path.join(SHARED_DIR, file.filename)
        fs.writeFileSync(filePath, file.data)
        savedFiles.push({
          name: file.filename,
          size: file.data.length,
          path: filePath
        })
        console.log(`文件已保存: ${file.filename} (${file.data.length} bytes)`)
      })

      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({
        success: true,
        message: `成功上传 ${savedFiles.length} 个文件`,
        files: savedFiles
      }))

    } catch (error) {
      console.error('文件上传处理失败:', error)
      res.writeHead(500, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ error: '文件上传失败: ' + error.message }))
    }
  })

  req.on('error', error => {
    console.error('上传请求错误:', error)
    res.writeHead(500, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ error: '上传请求错误' }))
  })
}

// 解析multipart/form-data
function parseMultipartData(buffer, boundary) {
  const files = []

  // 将buffer按boundary分割
  const boundaryBuffer = Buffer.from(boundary)
  const parts = []
  let start = 0

  while (true) {
    const index = buffer.indexOf(boundaryBuffer, start)
    if (index === -1) break

    if (start > 0) {
      parts.push(buffer.slice(start, index))
    }
    start = index + boundaryBuffer.length
  }

  for (const part of parts) {
    if (part.length === 0) continue

    // 查找头部结束位置
    const headerEnd = Buffer.from('\r\n\r\n')
    const headerEndIndex = part.indexOf(headerEnd)

    if (headerEndIndex === -1) continue

    const headers = part.slice(0, headerEndIndex).toString('utf8')
    const data = part.slice(headerEndIndex + 4, part.length - 2) // 去掉最后的\r\n

    console.log('解析到的headers:', headers)

    // 解析Content-Disposition头，支持UTF-8编码的文件名
    const dispositionMatch = headers.match(/Content-Disposition: form-data; name="([^"]+)"(?:; filename="([^"]+)")?/)

    if (dispositionMatch && dispositionMatch[2]) { // 有filename说明是文件
      let filename = dispositionMatch[2]

      // 处理UTF-8编码的文件名
      try {
        // 尝试多种解码方式
        if (filename.includes('%')) {
          // URL编码的文件名
          filename = decodeURIComponent(filename)
        } else {
          // 尝试从latin1转换为utf8
          const buffer = Buffer.from(filename, 'latin1')
          const utf8Filename = buffer.toString('utf8')

          // 检查转换后的文件名是否合理（包含中文字符）
          if (/[\u4e00-\u9fa5]/.test(utf8Filename)) {
            filename = utf8Filename
          }
        }
      } catch (e) {
        console.log('文件名解码失败，使用原始文件名:', filename, e.message)
      }

      console.log('解析到的文件名:', filename)

      files.push({
        fieldname: dispositionMatch[1],
        filename: filename,
        data: data
      })
    }
  }

  return files
}

// 处理资源代理请求
function handleResourceProxy(req, res) {
  // 从URL中提取目标资源URL
  const proxyUrl = req.url.replace('/proxy/', '')

  try {
    const targetUrl = decodeURIComponent(proxyUrl)
    console.log('代理请求资源:', targetUrl)

    // 验证URL格式
    if (!targetUrl.startsWith('http://') && !targetUrl.startsWith('https://')) {
      throw new Error('无效的URL格式')
    }

    const { URL } = require('url')
    const urlObj = new URL(targetUrl)
    const isHttps = urlObj.protocol === 'https:'
    const httpModule = isHttps ? require('https') : require('http')

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': `${urlObj.protocol}//${urlObj.host}/`
      },
      // 忽略SSL证书错误（仅用于开发环境）
      rejectUnauthorized: false
    }

    const proxyReq = httpModule.request(options, (proxyRes) => {
      console.log(`代理响应状态: ${proxyRes.statusCode} for ${targetUrl}`)

      // 设置CORS和缓存头
      const responseHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, max-age=3600'
      }

      // 保留原始内容类型
      if (proxyRes.headers['content-type']) {
        responseHeaders['Content-Type'] = proxyRes.headers['content-type']
      }

      // 如果是成功响应，转发内容
      if (proxyRes.statusCode >= 200 && proxyRes.statusCode < 400) {
        res.writeHead(proxyRes.statusCode, responseHeaders)

        // 处理压缩内容
        let stream = proxyRes
        const encoding = proxyRes.headers['content-encoding']

        if (encoding === 'gzip') {
          const zlib = require('zlib')
          stream = proxyRes.pipe(zlib.createGunzip())
        } else if (encoding === 'deflate') {
          const zlib = require('zlib')
          stream = proxyRes.pipe(zlib.createInflate())
        } else if (encoding === 'br') {
          const zlib = require('zlib')
          stream = proxyRes.pipe(zlib.createBrotliDecompress())
        }

        // 转发响应数据
        stream.pipe(res)

        stream.on('error', (error) => {
          console.error('流处理错误:', error)
          if (!res.headersSent) {
            res.writeHead(500)
            res.end('内容处理错误')
          }
        })

      } else {
        // 错误响应
        console.warn(`代理请求返回错误状态: ${proxyRes.statusCode} for ${targetUrl}`)
        res.writeHead(404, responseHeaders)
        res.end(`资源不可用 (${proxyRes.statusCode})`)
      }
    })

    proxyReq.on('error', (error) => {
      console.error('代理请求失败:', targetUrl, error.message)
      if (!res.headersSent) {
        res.writeHead(404, {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'text/plain'
        })
        res.end(`资源不可用: ${error.message}`)
      }
    })

    proxyReq.setTimeout(15000, () => {
      console.warn('代理请求超时:', targetUrl)
      proxyReq.destroy()
      if (!res.headersSent) {
        res.writeHead(408, {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'text/plain'
        })
        res.end('请求超时')
      }
    })

    proxyReq.end()

  } catch (error) {
    console.error('代理请求解析失败:', proxyUrl, error.message)
    if (!res.headersSent) {
      res.writeHead(400, {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'text/plain'
      })
      res.end(`无效的代理请求: ${error.message}`)
    }
  }
}

httpServer.listen(HTTP_PORT, () => {
  console.log(`HTTP服务已启动：http://localhost:${HTTP_PORT}/player.html`)
  console.log(`共享文件目录：${SHARED_DIR}`)
})

// 按键模拟命令处理函数
async function handleKeySimulateCommand(data) {
  try {
    const { action, params } = data
    let result

    switch (action) {
      case 'fullscreen':
        result = await keySimulator.toggleFullscreen(params?.mode || 'toggle')
        break
      case 'media':
        result = await keySimulator.mediaControl(params?.action || 'play')
        break
      case 'volume':
        result = await keySimulator.setVolume(params?.level || 50)
        break
      default:
        throw new Error(`未知的按键模拟动作: ${action}`)
    }

    // 发送成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'keySimulateResponse',
        success: true,
        action,
        result
      }))
    }

    console.log('按键模拟执行成功:', result)
  } catch (error) {
    console.error('按键模拟执行失败:', error)

    // 发送错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'error',
        message: '按键模拟执行失败',
        details: error.message
      }))
    }
  }
}

// 自动播放命令处理函数
async function handleAutoPlayCommand(data) {
  try {
    const { filePath, contentType } = data

    // 使用按键模拟器处理内容推送
    const result = await keySimulator.handleContentPush(contentType, filePath)

    // 发送成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'autoPlayResponse',
        success: true,
        result
      }))
    }

    console.log('自动播放处理成功:', result)
  } catch (error) {
    console.error('自动播放处理失败:', error)

    // 发送错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'error',
        message: '自动播放处理失败',
        details: error.message
      }))
    }
  }
}

// 音量控制命令处理函数
async function handleVolumeCommand(data) {
  try {
    const { volume } = data
    const result = await keySimulator.setVolume(volume)

    // 发送成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'setVolumeResponse',
        success: true,
        result
      }))
    }

    console.log('音量设置成功:', result)
  } catch (error) {
    console.error('音量设置失败:', error)

    // 发送错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'error',
        message: '音量设置失败',
        details: error.message
      }))
    }
  }
}

// 全屏控制命令处理函数
async function handleFullscreenCommand(data) {
  try {
    const { action } = data
    const result = await keySimulator.toggleFullscreen(action || 'toggle')

    // 发送成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'toggleFullscreenResponse',
        success: true,
        result
      }))
    }

    console.log('全屏控制成功:', result)
  } catch (error) {
    console.error('全屏控制失败:', error)

    // 发送错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'error',
        message: '全屏控制失败',
        details: error.message
      }))
    }
  }
}

// 调用本地应用打开文件的函数
function openFileWithApp(filePath, appType, callback) {
  // 始终使用系统关联程序
  const appCommand = 'start'

  // 构建完整命令
  let command = ''
  if (appCommand === 'start') {
    // 使用start命令时，不需要额外引号
    command = `start "" "${filePath}"`
  } else {
    // 其他命令需要引号包裹文件路径
    command = `${appCommand} "${filePath}"`
  }

  console.log('执行命令:', command)

  // 执行命令
  exec(command, async (error, stdout, stderr) => {
    if (error) {
      return callback(new Error(`执行命令失败: ${error.message}`))
    }
    if (stderr) {
      return callback(new Error(`命令执行错误: ${stderr}`))
    }

    // 文件打开成功后，自动执行PotPlayer的全屏和循环播放
    try {
      console.log('文件已打开，开始自动化处理...')

      // 等待应用启动
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 检查文件类型，如果是视频或图片，执行自动化处理
      const fileExt = path.extname(filePath).toLowerCase()
      const isVideo = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'].includes(fileExt)
      const isImage = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(fileExt)

      if (isVideo || isImage) {
        console.log(`检测到${isVideo ? '视频' : '图片'}文件，执行PotPlayer自动化...`)

        // 1. 触发全屏 (Enter键 - PotPlayer的全屏快捷键)
        console.log('触发PotPlayer全屏 (Enter键)...')
        await keySimulator.executePowerShell('mediacontrol', 'enter')
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 2. 如果是视频，启用循环播放
        if (isVideo) {
          console.log('启用PotPlayer循环播放...')
          // PotPlayer的循环播放快捷键通常是 Ctrl+R
          await keySimulator.executePowerShell('mediacontrol', 'ctrl_r')
          await new Promise(resolve => setTimeout(resolve, 500))
        }

        console.log('PotPlayer自动化处理完成')
      }

    } catch (autoError) {
      console.error('自动化处理失败:', autoError)
      // 即使自动化失败，也不影响文件打开的成功状态
    }

    callback(null)
  })
}

// 工具函数：计算到指定时间的秒数
function getSecondsUntilTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number)
  const now = new Date()
  const targetTime = new Date()
  targetTime.setHours(hours, minutes, 0, 0)

  // 如果目标时间已过，设置为明天
  if (targetTime <= now) {
    targetTime.setDate(targetTime.getDate() + 1)
  }

  return Math.floor((targetTime - now) / 1000)
}

// 处理看板获取命令
async function handleFetchDashboard(data) {
  try {
    const { loginConfig, dashboard } = data

    if (!loginConfig || !loginConfig.baseUrl) {
      throw new Error('缺少网站地址配置')
    }

    if (!dashboard || !dashboard.url) {
      throw new Error('缺少看板信息')
    }

    console.log(`开始获取看板: ${dashboard.name} (${dashboard.url})`)

    // 构建完整的看板URL
    const dashboardFullUrl = loginConfig.baseUrl + dashboard.url
    console.log(`完整看板URL: ${dashboardFullUrl}`)

    // 生成HTML文件名
    const fileName = `dashboard_${dashboard.id}_${Date.now()}.html`
    const filePath = path.join(SHARED_DIR, fileName)

    // 创建一个简单的iframe包装页面，直接显示原始网站内容
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${dashboard.name} - 看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }

        .dashboard-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .loading-url {
            font-size: 12px;
            opacity: 0.7;
            max-width: 80%;
            text-align: center;
            word-break: break-all;
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            display: none;
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载看板内容...</div>
            <div class="loading-url">${dashboardFullUrl}</div>
            <div class="error-message" id="errorMessage">
                看板加载失败，可能是网络问题或需要登录验证
            </div>
        </div>

        <iframe
            id="dashboardFrame"
            class="dashboard-iframe"
            src="${dashboardFullUrl}"
            frameborder="0"
            allowfullscreen
            allow="fullscreen; autoplay; encrypted-media; camera; microphone; geolocation"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads allow-top-navigation allow-top-navigation-by-user-activation allow-pointer-lock allow-presentation"
        ></iframe>
    </div>

    <script>
        const iframe = document.getElementById('dashboardFrame');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const errorMessage = document.getElementById('errorMessage');
        let loadTimeout;

        // 检测SSO登录页面
        let ssoDetected = false;

        // 设置加载超时
        loadTimeout = setTimeout(() => {
            console.warn('看板加载超时');
            if (!ssoDetected) {
                errorMessage.style.display = 'block';
                errorMessage.innerHTML = \`
                    看板加载超时，可能需要登录验证。<br>
                    <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        在新窗口中登录
                    </button>
                \`;
            }
        }, 15000); // 15秒超时

        // iframe加载完成
        iframe.addEventListener('load', function() {
            console.log('看板加载完成');
            clearTimeout(loadTimeout);

            // 检测是否是SSO登录页面
            try {
                const iframeSrc = iframe.contentWindow.location.href;
                if (iframeSrc.includes('sso.') || iframeSrc.includes('login') || iframeSrc.includes('auth')) {
                    console.log('检测到SSO登录页面');
                    ssoDetected = true;
                    errorMessage.style.display = 'block';
                    errorMessage.innerHTML = \`
                        检测到需要登录验证。<br>
                        <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            在新窗口中登录
                        </button>
                        <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                            登录完成后，请重新获取看板内容
                        </div>
                    \`;
                    return;
                }
            } catch (e) {
                console.log('无法检测iframe URL（跨域限制）');
            }

            // 延迟隐藏加载界面，确保内容完全加载
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }, 2000);
        });

        // iframe加载错误
        iframe.addEventListener('error', function() {
            console.error('看板加载失败');
            clearTimeout(loadTimeout);
            errorMessage.style.display = 'block';
            errorMessage.innerHTML = \`
                看板加载失败，可能是跨域限制。<br>
                <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    在新窗口中打开
                </button>
            \`;
        });

        // 在新窗口中打开看板
        window.openInNewWindow = function() {
            window.open('${dashboardFullUrl}', '_blank', 'fullscreen=yes,scrollbars=yes,resizable=yes');
        };

        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 禁用某些快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });

        // 尝试与iframe内容通信（如果同源）
        try {
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        console.log('可以访问iframe内容');

                        // 隐藏iframe内可能的滚动条
                        const style = iframeDoc.createElement('style');
                        style.textContent = \`
                            ::-webkit-scrollbar { display: none; }
                            body { overflow: hidden; }
                        \`;
                        iframeDoc.head.appendChild(style);
                    }
                } catch (e) {
                    console.log('无法访问iframe内容（跨域限制）:', e.message);
                }
            });
        } catch (e) {
            console.log('iframe通信设置失败:', e.message);
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏');
            } else {
                console.log('页面显示');
            }
        });

        console.log('看板页面初始化完成');
        console.log('目标URL:', '${dashboardFullUrl}');
        console.log('看板名称:', '${dashboard.name}');
    </script>
</body>
</html>`

    // 保存HTML文件
    fs.writeFileSync(filePath, htmlContent, 'utf8')
    console.log(`看板HTML文件已保存: ${filePath}`)

    // 返回成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'fetchDashboardResponse',
        success: true,
        dashboard: dashboard,
        fileName: fileName,
        filePath: filePath,
        message: '看板内容获取成功'
      }))
    }

  } catch (error) {
    console.error('获取看板失败:', error)

    // 返回错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'fetchDashboardResponse',
        success: false,
        message: error.message,
        details: error.stack
      }))
    }
  }
}

// 处理看板推送命令
async function handlePushDashboard(data) {
  try {
    const { dashboard, fileName } = data

    if (!dashboard) {
      throw new Error('缺少看板信息')
    }

    if (!fileName) {
      throw new Error('缺少文件名')
    }

    console.log(`开始推送看板: ${dashboard.name}`)

    // 检查HTML文件是否存在
    const filePath = path.join(SHARED_DIR, fileName)
    if (!fs.existsSync(filePath)) {
      throw new Error('看板HTML文件不存在，请先获取看板内容')
    }

    // 自动打开播放器页面
    const playerUrl = `http://localhost:${HTTP_PORT}/player.html`
    console.log('自动打开播放器页面:', playerUrl)

    exec(`start "" "${playerUrl}"`, (error) => {
      if (error) {
        console.error('打开播放器页面失败:', error)
      } else {
        console.log('播放器页面已打开')

        // 播放器打开后，延迟触发全屏
        setTimeout(async () => {
          try {
            console.log('触发浏览器全屏...')
            const fullscreenResult = await keySimulator.triggerBrowserFullscreen()
            console.log('浏览器全屏结果:', fullscreenResult)
          } catch (error) {
            console.error('触发浏览器全屏失败:', error)
          }
        }, 2000)
      }
    })

    // 等待播放器连接后再发送内容
    setTimeout(() => {
      sendDashboardToPlayer(dashboard, fileName)
    }, 3000)

    // 立即返回成功响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'pushDashboardResponse',
        success: true,
        dashboard: dashboard,
        message: '看板推送成功'
      }))
    }

  } catch (error) {
    console.error('推送看板失败:', error)

    // 返回错误响应
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'pushDashboardResponse',
        success: false,
        message: error.message,
        details: error.stack
      }))
    }
  }
}

// 发送看板内容到播放器
function sendDashboardToPlayer(dashboard, fileName) {
  // 检查播放器连接状态
  if (!playerSocket || playerSocket.readyState !== WebSocket.OPEN) {
    console.warn('播放器未连接，等待连接...')

    // 等待播放器连接，最多等待10秒
    let attempts = 0
    const maxAttempts = 20

    const waitForConnection = setInterval(() => {
      attempts++

      if (playerSocket && playerSocket.readyState === WebSocket.OPEN) {
        clearInterval(waitForConnection)
        console.log('播放器已连接，发送看板内容')
        sendDashboardNow(dashboard, fileName)
      } else if (attempts >= maxAttempts) {
        clearInterval(waitForConnection)
        console.error('等待播放器连接超时')
        if (padSocket && padSocket.readyState === WebSocket.OPEN) {
          padSocket.send(JSON.stringify({
            type: 'error',
            message: '播放器连接超时，请手动打开播放器页面'
          }))
        }
      }
    }, 500)

    return
  }

  sendDashboardNow(dashboard, fileName)
}

// 立即发送看板内容到播放器
function sendDashboardNow(dashboard, fileName) {
  try {
    // 构建播放器消息
    const playerMessage = {
      type: 'html',
      src: fileName,
      name: dashboard.name,
      autoFullscreen: true
    }

    playerSocket.send(JSON.stringify(playerMessage))
    console.log('看板内容已推送到播放器:', playerMessage)

  } catch (err) {
    console.error('推送看板内容失败:', err)
    if (padSocket && padSocket.readyState === WebSocket.OPEN) {
      padSocket.send(JSON.stringify({
        type: 'error',
        message: '推送看板内容失败',
        details: err.message
      }))
    }
  }
}