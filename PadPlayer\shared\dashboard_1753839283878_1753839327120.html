
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>十二车间监控看板 - 看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }

        .dashboard-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .loading-url {
            font-size: 12px;
            opacity: 0.7;
            max-width: 80%;
            text-align: center;
            word-break: break-all;
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            display: none;
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载看板内容...</div>
            <div class="loading-url">https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=19&userName=80415865&token=d6Ms7lqJeAf0Wp%2BgH1uAOBOnjXrXOO01o1pRuqEPMIUD06D9BG0oBC%2BC0Sx1MnKCzsG7vMZWuY%2FYyiJKOveJf%2BpOfgivNIxI1iz37FheWnNn5IV5UuQIvb%2BTVowiLVEUaDLiEDguJAyf3X%2FhT%2BRUvcZXQselgL6Fz29KadPcGBSZGVyrMe8dSzrhTao%2BPDIKGGApfEUDRPJol0SmjLzkyUK7Hsb4af9Qi6OyLInsN0VV0hM7WODK0%2B9XFmGL4dpibRXBEQoKQjDJzLgTkoiJWWFFTjfhW7FZgSOgY2W2Q4PVJnToqj0Engo9362fRW0M0F%2FugkKuxscI2sa71SaCsjt4QtxvUcmQ9UzEvM7rExdet%2B6Tzyb0g2WFm4j3G%2F3lkyNEPMNhQROIziEh%2FwoVwmlTQINkt8FtYEw7%2B%2B4uwA5wR4bU4Ebzzd1jFVcilbPJUpkyWpwhLyjY948INahdtTnrrM8S0DSbdyu%2FNwtAMz2h6y8gW9CLEdmBMKouugzRTuEm5vRu7MNmBlxSXiocdXl%2BkdYA6lV9Z4eRZTMmpZPPyGKm63aFCMvXfUlD7DCEmjEvQ%2B1OKTOjnqMpv8YLUFMfWLfa7%2FTOyC65CyUvfFtWw1HsKuNLp7J0vV3eUgPTu%2BjDW4r750jRg8ffcI9rZZPRMPrSQwrmjUE9LLHuUFurwKVAdhKuNpG0w9xyMmLM4ZJk58%2Bn5a4tLoi%2B6pHVUu7rv6LZ1ZpnRhX7LexNPjomBfPuwde6fBzXcFznsE0b1vDtPbeFugRJtX0ml6Qs68MuVTx61xwyXdYj3Bu9%2BzIZqd6f3Sk5YmgepXnF7OJTvmd5eFYV6nBP2Z%2BW9%2BmbPw%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1</div>
            <div class="error-message" id="errorMessage">
                看板加载失败，可能是网络问题或需要登录验证
            </div>
        </div>

        <iframe
            id="dashboardFrame"
            class="dashboard-iframe"
            src="https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=19&userName=80415865&token=d6Ms7lqJeAf0Wp%2BgH1uAOBOnjXrXOO01o1pRuqEPMIUD06D9BG0oBC%2BC0Sx1MnKCzsG7vMZWuY%2FYyiJKOveJf%2BpOfgivNIxI1iz37FheWnNn5IV5UuQIvb%2BTVowiLVEUaDLiEDguJAyf3X%2FhT%2BRUvcZXQselgL6Fz29KadPcGBSZGVyrMe8dSzrhTao%2BPDIKGGApfEUDRPJol0SmjLzkyUK7Hsb4af9Qi6OyLInsN0VV0hM7WODK0%2B9XFmGL4dpibRXBEQoKQjDJzLgTkoiJWWFFTjfhW7FZgSOgY2W2Q4PVJnToqj0Engo9362fRW0M0F%2FugkKuxscI2sa71SaCsjt4QtxvUcmQ9UzEvM7rExdet%2B6Tzyb0g2WFm4j3G%2F3lkyNEPMNhQROIziEh%2FwoVwmlTQINkt8FtYEw7%2B%2B4uwA5wR4bU4Ebzzd1jFVcilbPJUpkyWpwhLyjY948INahdtTnrrM8S0DSbdyu%2FNwtAMz2h6y8gW9CLEdmBMKouugzRTuEm5vRu7MNmBlxSXiocdXl%2BkdYA6lV9Z4eRZTMmpZPPyGKm63aFCMvXfUlD7DCEmjEvQ%2B1OKTOjnqMpv8YLUFMfWLfa7%2FTOyC65CyUvfFtWw1HsKuNLp7J0vV3eUgPTu%2BjDW4r750jRg8ffcI9rZZPRMPrSQwrmjUE9LLHuUFurwKVAdhKuNpG0w9xyMmLM4ZJk58%2Bn5a4tLoi%2B6pHVUu7rv6LZ1ZpnRhX7LexNPjomBfPuwde6fBzXcFznsE0b1vDtPbeFugRJtX0ml6Qs68MuVTx61xwyXdYj3Bu9%2BzIZqd6f3Sk5YmgepXnF7OJTvmd5eFYV6nBP2Z%2BW9%2BmbPw%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1"
            frameborder="0"
            allowfullscreen
            allow="fullscreen; autoplay; encrypted-media; camera; microphone; geolocation"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads allow-top-navigation allow-top-navigation-by-user-activation allow-pointer-lock allow-presentation"
        ></iframe>
    </div>

    <script>
        const iframe = document.getElementById('dashboardFrame');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const errorMessage = document.getElementById('errorMessage');
        let loadTimeout;

        // 检测SSO登录页面
        let ssoDetected = false;

        // 设置加载超时
        loadTimeout = setTimeout(() => {
            console.warn('看板加载超时');
            if (!ssoDetected) {
                errorMessage.style.display = 'block';
                errorMessage.innerHTML = `
                    看板加载超时，可能需要登录验证。<br>
                    <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        在新窗口中登录
                    </button>
                `;
            }
        }, 15000); // 15秒超时

        // iframe加载完成
        iframe.addEventListener('load', function() {
            console.log('看板加载完成');
            clearTimeout(loadTimeout);

            // 检测是否是SSO登录页面
            try {
                const iframeSrc = iframe.contentWindow.location.href;
                if (iframeSrc.includes('sso.') || iframeSrc.includes('login') || iframeSrc.includes('auth')) {
                    console.log('检测到SSO登录页面');
                    ssoDetected = true;
                    errorMessage.style.display = 'block';
                    errorMessage.innerHTML = `
                        检测到需要登录验证。<br>
                        <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            在新窗口中登录
                        </button>
                        <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                            登录完成后，请重新获取看板内容
                        </div>
                    `;
                    return;
                }
            } catch (e) {
                console.log('无法检测iframe URL（跨域限制）');
            }

            // 延迟隐藏加载界面，确保内容完全加载
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }, 2000);
        });

        // iframe加载错误
        iframe.addEventListener('error', function() {
            console.error('看板加载失败');
            clearTimeout(loadTimeout);
            errorMessage.style.display = 'block';
            errorMessage.innerHTML = `
                看板加载失败，可能是跨域限制。<br>
                <button onclick="openInNewWindow()" style="margin-top: 10px; padding: 8px 16px; background: #007aff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    在新窗口中打开
                </button>
            `;
        });

        // 在新窗口中打开看板
        window.openInNewWindow = function() {
            window.open('https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=19&userName=80415865&token=d6Ms7lqJeAf0Wp%2BgH1uAOBOnjXrXOO01o1pRuqEPMIUD06D9BG0oBC%2BC0Sx1MnKCzsG7vMZWuY%2FYyiJKOveJf%2BpOfgivNIxI1iz37FheWnNn5IV5UuQIvb%2BTVowiLVEUaDLiEDguJAyf3X%2FhT%2BRUvcZXQselgL6Fz29KadPcGBSZGVyrMe8dSzrhTao%2BPDIKGGApfEUDRPJol0SmjLzkyUK7Hsb4af9Qi6OyLInsN0VV0hM7WODK0%2B9XFmGL4dpibRXBEQoKQjDJzLgTkoiJWWFFTjfhW7FZgSOgY2W2Q4PVJnToqj0Engo9362fRW0M0F%2FugkKuxscI2sa71SaCsjt4QtxvUcmQ9UzEvM7rExdet%2B6Tzyb0g2WFm4j3G%2F3lkyNEPMNhQROIziEh%2FwoVwmlTQINkt8FtYEw7%2B%2B4uwA5wR4bU4Ebzzd1jFVcilbPJUpkyWpwhLyjY948INahdtTnrrM8S0DSbdyu%2FNwtAMz2h6y8gW9CLEdmBMKouugzRTuEm5vRu7MNmBlxSXiocdXl%2BkdYA6lV9Z4eRZTMmpZPPyGKm63aFCMvXfUlD7DCEmjEvQ%2B1OKTOjnqMpv8YLUFMfWLfa7%2FTOyC65CyUvfFtWw1HsKuNLp7J0vV3eUgPTu%2BjDW4r750jRg8ffcI9rZZPRMPrSQwrmjUE9LLHuUFurwKVAdhKuNpG0w9xyMmLM4ZJk58%2Bn5a4tLoi%2B6pHVUu7rv6LZ1ZpnRhX7LexNPjomBfPuwde6fBzXcFznsE0b1vDtPbeFugRJtX0ml6Qs68MuVTx61xwyXdYj3Bu9%2BzIZqd6f3Sk5YmgepXnF7OJTvmd5eFYV6nBP2Z%2BW9%2BmbPw%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1', '_blank', 'fullscreen=yes,scrollbars=yes,resizable=yes');
        };

        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 禁用某些快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });

        // 尝试与iframe内容通信（如果同源）
        try {
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        console.log('可以访问iframe内容');

                        // 隐藏iframe内可能的滚动条
                        const style = iframeDoc.createElement('style');
                        style.textContent = `
                            ::-webkit-scrollbar { display: none; }
                            body { overflow: hidden; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                } catch (e) {
                    console.log('无法访问iframe内容（跨域限制）:', e.message);
                }
            });
        } catch (e) {
            console.log('iframe通信设置失败:', e.message);
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏');
            } else {
                console.log('页面显示');
            }
        });

        console.log('看板页面初始化完成');
        console.log('目标URL:', 'https://scada-ca.myoas.com/WebDesigner/Designer/RunTime.html?projectId=19&userName=80415865&token=d6Ms7lqJeAf0Wp%2BgH1uAOBOnjXrXOO01o1pRuqEPMIUD06D9BG0oBC%2BC0Sx1MnKCzsG7vMZWuY%2FYyiJKOveJf%2BpOfgivNIxI1iz37FheWnNn5IV5UuQIvb%2BTVowiLVEUaDLiEDguJAyf3X%2FhT%2BRUvcZXQselgL6Fz29KadPcGBSZGVyrMe8dSzrhTao%2BPDIKGGApfEUDRPJol0SmjLzkyUK7Hsb4af9Qi6OyLInsN0VV0hM7WODK0%2B9XFmGL4dpibRXBEQoKQjDJzLgTkoiJWWFFTjfhW7FZgSOgY2W2Q4PVJnToqj0Engo9362fRW0M0F%2FugkKuxscI2sa71SaCsjt4QtxvUcmQ9UzEvM7rExdet%2B6Tzyb0g2WFm4j3G%2F3lkyNEPMNhQROIziEh%2FwoVwmlTQINkt8FtYEw7%2B%2B4uwA5wR4bU4Ebzzd1jFVcilbPJUpkyWpwhLyjY948INahdtTnrrM8S0DSbdyu%2FNwtAMz2h6y8gW9CLEdmBMKouugzRTuEm5vRu7MNmBlxSXiocdXl%2BkdYA6lV9Z4eRZTMmpZPPyGKm63aFCMvXfUlD7DCEmjEvQ%2B1OKTOjnqMpv8YLUFMfWLfa7%2FTOyC65CyUvfFtWw1HsKuNLp7J0vV3eUgPTu%2BjDW4r750jRg8ffcI9rZZPRMPrSQwrmjUE9LLHuUFurwKVAdhKuNpG0w9xyMmLM4ZJk58%2Bn5a4tLoi%2B6pHVUu7rv6LZ1ZpnRhX7LexNPjomBfPuwde6fBzXcFznsE0b1vDtPbeFugRJtX0ml6Qs68MuVTx61xwyXdYj3Bu9%2BzIZqd6f3Sk5YmgepXnF7OJTvmd5eFYV6nBP2Z%2BW9%2BmbPw%3D%3D&tenantId=04e0b545da32366947b0947a376e220e&orgId=1');
        console.log('看板名称:', '十二车间监控看板');
    </script>
</body>
</html>