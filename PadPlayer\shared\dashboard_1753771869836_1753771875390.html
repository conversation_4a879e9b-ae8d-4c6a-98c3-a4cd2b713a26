
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1 - 看板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }

        .dashboard-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .loading-url {
            font-size: 12px;
            opacity: 0.7;
            max-width: 80%;
            text-align: center;
            word-break: break-all;
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            display: none;
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载看板内容...</div>
            <div class="loading-url">https://meskanban.myoas.com/kanban/new/ca/smt/passage/cable</div>
            <div class="error-message" id="errorMessage">
                看板加载失败，可能是网络问题或需要登录验证
            </div>
        </div>

        <iframe
            id="dashboardFrame"
            class="dashboard-iframe"
            src="https://meskanban.myoas.com/kanban/new/ca/smt/passage/cable"
            frameborder="0"
            allowfullscreen
            allow="fullscreen; autoplay; encrypted-media"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads"
        ></iframe>
    </div>

    <script>
        const iframe = document.getElementById('dashboardFrame');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const errorMessage = document.getElementById('errorMessage');
        let loadTimeout;

        // 设置加载超时
        loadTimeout = setTimeout(() => {
            console.warn('看板加载超时');
            errorMessage.style.display = 'block';
            errorMessage.textContent = '看板加载超时，请检查网络连接或网站是否可访问';
        }, 30000); // 30秒超时

        // iframe加载完成
        iframe.addEventListener('load', function() {
            console.log('看板加载完成');
            clearTimeout(loadTimeout);

            // 延迟隐藏加载界面，确保内容完全加载
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }, 2000);
        });

        // iframe加载错误
        iframe.addEventListener('error', function() {
            console.error('看板加载失败');
            clearTimeout(loadTimeout);
            errorMessage.style.display = 'block';
            errorMessage.textContent = '看板加载失败，请检查网址是否正确或网站是否可访问';
        });

        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 禁用某些快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });

        // 尝试与iframe内容通信（如果同源）
        try {
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        console.log('可以访问iframe内容');

                        // 隐藏iframe内可能的滚动条
                        const style = iframeDoc.createElement('style');
                        style.textContent = `
                            ::-webkit-scrollbar { display: none; }
                            body { overflow: hidden; }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                } catch (e) {
                    console.log('无法访问iframe内容（跨域限制）:', e.message);
                }
            });
        } catch (e) {
            console.log('iframe通信设置失败:', e.message);
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏');
            } else {
                console.log('页面显示');
            }
        });

        console.log('看板页面初始化完成');
        console.log('目标URL:', 'https://meskanban.myoas.com/kanban/new/ca/smt/passage/cable');
        console.log('看板名称:', '1');
    </script>
</body>
</html>