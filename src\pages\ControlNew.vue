<template>
  <div class="control-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">批量设备控制中心</h1>
        <p class="page-description">批量控制设备播放和系统功能</p>
      </div>
    </div>

    <!-- 设备状态概览 -->
    <div class="overview-section">
      <div class="control-card">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ onlineDevices.length }}</div>
            <div class="stat-label">在线设备</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ offlineDevices.length }}</div>
            <div class="stat-label">离线设备</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ allDevices.length }}</div>
            <div class="stat-label">总设备数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 定时开关机 -->
    <div class="schedule-section">
      <div class="control-card">
        <h2 class="section-title">定时开关机</h2>
        <div class="schedule-controls">
          <div class="schedule-item">
            <label class="schedule-label">定时关机:</label>
            <input
              type="time"
              v-model="shutdownTime"
              class="time-input"
            />
            <button
              class="control-btn control-btn-danger"
              @click="scheduleShutdown"
              :disabled="!shutdownTime"
            >
              设置关机
            </button>
          </div>

          <div class="schedule-item">
            <label class="schedule-label">定时开机:</label>
            <input
              type="time"
              v-model="startupTime"
              class="time-input"
            />
            <button
              class="control-btn control-btn-success"
              @click="scheduleStartup"
              :disabled="!startupTime"
            >
              设置开机
            </button>
          </div>

          <div class="schedule-item">
            <button class="control-btn" @click="cancelSchedule">
              取消定时任务
            </button>
          </div>

          <!-- 定时任务状态 -->
          <div v-if="scheduleStatus" class="schedule-status">
            <div class="status-alert" :class="`alert-${scheduleStatus.type}`">
              <strong>{{ scheduleStatus.title }}</strong>
              <p>{{ scheduleStatus.message }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统一音量调节 -->
    <div class="volume-section">
      <div class="control-card">
        <h2 class="section-title">统一音量调节</h2>
        <div class="volume-controls">
          <div class="volume-slider">
            <label class="volume-label">音量: {{ globalVolume }}%</label>
            <input
              type="range"
              min="0"
              max="100"
              step="5"
              v-model="globalVolume"
              class="slider"
            />
          </div>
          <div class="volume-buttons">
            <button class="control-btn control-btn-primary" @click="applyVolumeToAll">
              应用到所有设备
            </button>
            <button class="control-btn control-btn-warning" @click="muteAllDevices">
              全部静音
            </button>
            <button class="control-btn control-btn-success" @click="unmuteAllDevices">
              取消静音
            </button>
          </div>

          <!-- 音量调节状态 -->
          <div v-if="volumeStatus" class="volume-status">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: volumeProgress + '%' }"></div>
            </div>
            <div class="status-text">{{ volumeStatusText }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧急消息广播 -->
    <div class="broadcast-section">
      <div class="control-card">
        <h2 class="section-title">紧急消息广播</h2>
        <div class="broadcast-controls">
          <div class="broadcast-input">
            <textarea
              v-model="broadcastMessage"
              placeholder="输入要广播的紧急消息..."
              rows="3"
              maxlength="500"
              class="broadcast-textarea"
            ></textarea>
            <div class="char-count">{{ broadcastMessage.length }}/500</div>
          </div>

          <div class="broadcast-options">
            <div class="option-group">
              <label class="option-label">消息类型:</label>
              <select v-model="broadcastType" class="option-select">
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="info">信息</option>
                <option value="success">成功</option>
              </select>
            </div>

            <div class="option-group">
              <label class="option-label">显示时长:</label>
              <select v-model="broadcastDuration" class="option-select">
                <option :value="3000">3秒</option>
                <option :value="5000">5秒</option>
                <option :value="10000">10秒</option>
                <option :value="30000">30秒</option>
                <option :value="60000">1分钟</option>
              </select>
            </div>
          </div>

          <div class="broadcast-actions">
            <button
              class="control-btn control-btn-danger control-btn-large"
              @click="sendBroadcast"
              :disabled="!broadcastMessage.trim() || broadcasting"
            >
              {{ broadcasting ? '广播中...' : '立即广播' }}
            </button>
            <button class="control-btn" @click="clearBroadcast">
              清空消息
            </button>
          </div>

          <!-- 广播状态 -->
          <div v-if="broadcastStatus" class="broadcast-status">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: broadcastProgress + '%' }"></div>
            </div>
            <div class="status-text">{{ broadcastStatusText }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备状态表格 -->
    <div class="devices-section">
      <div class="control-card">
        <h2 class="section-title">设备状态</h2>
        <div class="devices-table">
          <table class="device-table">
            <thead>
              <tr>
                <th>设备名称</th>
                <th>IP地址</th>
                <th>连接状态</th>
                <th>最后活动</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="device in allDevices"
                :key="`${device.ip}:${device.port}`"
                :class="{ 'device-online': device.connected }"
              >
                <td>{{ device.name || `设备 ${device.ip}` }}</td>
                <td>{{ device.ip }}:{{ device.port }}</td>
                <td>
                  <span class="status-badge" :class="{ 'status-online': device.connected }">
                    {{ device.connected ? '在线' : '离线' }}
                  </span>
                </td>
                <td>{{ device.lastSeen ? new Date(device.lastSeen).toLocaleString() : '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useDeviceStore } from '@/store/device'

const deviceStore = useDeviceStore()

// 响应式数据
const shutdownTime = ref(null)
const startupTime = ref(null)
const scheduleStatus = ref(null)

const globalVolume = ref(50)
const volumeStatus = ref(false)
const volumeProgress = ref(0)
const volumeStatusText = ref('')

const broadcastMessage = ref('')
const broadcastType = ref('warning')
const broadcastDuration = ref(5000)
const broadcasting = ref(false)
const broadcastStatus = ref(false)
const broadcastProgress = ref(0)
const broadcastStatusText = ref('')

// 计算属性
const allDevices = computed(() => deviceStore.devices)
const onlineDevices = computed(() => allDevices.value.filter(device => device.connected))
const offlineDevices = computed(() => allDevices.value.filter(device => !device.connected))

// 音量控制功能
const applyVolumeToAll = async () => {
  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    volumeStatus.value = true
    volumeProgress.value = 0
    volumeStatusText.value = `正在为 ${onlineCount} 台设备设置音量为 ${globalVolume.value}%...`

    let successCount = 0
    let warningCount = 0

    for (let i = 0; i < onlineDevices.value.length; i++) {
      const device = onlineDevices.value[i]
      try {
        const result = await sendCommandToDevice(device, 'setVolume', { level: globalVolume.value })
        if (result.message && result.message.includes('超时')) {
          warningCount++
        } else {
          successCount++
        }
      } catch (error) {
        console.error(`设备 ${device.ip} 设置音量失败:`, error)
      }

      volumeProgress.value = Math.round(((i + 1) / onlineDevices.value.length) * 100)
      volumeStatusText.value = `正在设置音量... (${i + 1}/${onlineDevices.value.length})`
    }

    if (successCount > 0 || warningCount > 0) {
      let message = `音量设置完成！`
      if (successCount > 0) message += ` 成功: ${successCount} 台`
      if (warningCount > 0) message += ` 超时但可能已执行: ${warningCount} 台`
      volumeStatusText.value = message
      console.log(`音量设置完成，成功: ${successCount}，超时: ${warningCount}`)
    } else {
      volumeStatusText.value = '音量设置失败，所有设备都无法设置'
    }

    setTimeout(() => {
      volumeStatus.value = false
    }, 3000)
  } catch (error) {
    console.error('音量设置失败:', error)
    volumeStatus.value = false
    alert('音量设置失败')
  }
}

const muteAllDevices = async () => {
  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    volumeStatus.value = true
    volumeProgress.value = 0
    volumeStatusText.value = `正在为 ${onlineCount} 台设备设置静音...`

    let successCount = 0
    let warningCount = 0

    for (let i = 0; i < onlineDevices.value.length; i++) {
      const device = onlineDevices.value[i]
      try {
        const result = await sendCommandToDevice(device, 'mute')
        if (result.message && result.message.includes('超时')) {
          warningCount++
        } else {
          successCount++
        }
      } catch (error) {
        console.error(`设备 ${device.ip} 静音失败:`, error)
      }

      volumeProgress.value = Math.round(((i + 1) / onlineDevices.value.length) * 100)
      volumeStatusText.value = `正在设置静音... (${i + 1}/${onlineDevices.value.length})`
    }

    if (successCount > 0 || warningCount > 0) {
      let message = `静音设置完成！`
      if (successCount > 0) message += ` 成功: ${successCount} 台`
      if (warningCount > 0) message += ` 超时但可能已执行: ${warningCount} 台`
      volumeStatusText.value = message
      console.log(`静音设置完成，成功: ${successCount}，超时: ${warningCount}`)
    } else {
      volumeStatusText.value = '静音设置失败，所有设备都无法设置'
    }

    setTimeout(() => {
      volumeStatus.value = false
    }, 4000)
  } catch (error) {
    console.error('静音设置失败:', error)
    volumeStatus.value = false
    alert('静音设置失败')
  }
}

const unmuteAllDevices = async () => {
  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    volumeStatus.value = true
    volumeProgress.value = 0
    volumeStatusText.value = `正在为 ${onlineCount} 台设备取消静音...`

    let successCount = 0
    let warningCount = 0

    for (let i = 0; i < onlineDevices.value.length; i++) {
      const device = onlineDevices.value[i]
      try {
        const result = await sendCommandToDevice(device, 'unmute')
        if (result.message && result.message.includes('超时')) {
          warningCount++
        } else {
          successCount++
        }
      } catch (error) {
        console.error(`设备 ${device.ip} 取消静音失败:`, error)
      }

      volumeProgress.value = Math.round(((i + 1) / onlineDevices.value.length) * 100)
      volumeStatusText.value = `正在取消静音... (${i + 1}/${onlineDevices.value.length})`
    }

    if (successCount > 0 || warningCount > 0) {
      let message = `取消静音完成！`
      if (successCount > 0) message += ` 成功: ${successCount} 台`
      if (warningCount > 0) message += ` 超时但可能已执行: ${warningCount} 台`
      volumeStatusText.value = message
      console.log(`取消静音完成，成功: ${successCount}，超时: ${warningCount}`)
    } else {
      volumeStatusText.value = '取消静音失败，所有设备都无法设置'
    }

    setTimeout(() => {
      volumeStatus.value = false
    }, 4000)
  } catch (error) {
    console.error('取消静音失败:', error)
    volumeStatus.value = false
    alert('取消静音失败')
  }
}

const scheduleShutdown = async () => {
  if (!shutdownTime.value) return

  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    console.log('设置定时关机:', shutdownTime.value)



    let successCount = 0
    for (const device of onlineDevices.value) {
      try {
        await sendCommandToDevice(device, 'scheduleShutdown', { time: shutdownTime.value })
        successCount++
      } catch (error) {
        console.error(`设备 ${device.ip} 设置定时关机失败:`, error)
      }
    }

    if (successCount > 0) {
      scheduleStatus.value = {
        type: 'success',
        title: '定时关机设置成功',
        message: `已为 ${successCount} 台设备设置定时关机时间: ${shutdownTime.value}`
      }
      console.log(`定时关机设置成功，影响 ${successCount} 台设备`)
    } else {
      scheduleStatus.value = {
        type: 'error',
        title: '定时关机设置失败',
        message: '所有设备设置失败'
      }
    }
  } catch (error) {
    console.error('定时关机设置失败:', error)
    scheduleStatus.value = {
      type: 'error',
      title: '定时关机设置失败',
      message: error.message
    }
  }
}

const scheduleStartup = () => {
  if (!startupTime.value) return
  
  console.log('设置定时开机:', startupTime.value)
  
  const command = {
    type: 'scheduleStartup',
    time: startupTime.value
  }
  
  onlineDevices.value.forEach(device => {
    if (device.ws && device.ws.readyState === WebSocket.OPEN) {
      device.ws.send(JSON.stringify(command))
    }
  })
  
  alert(`已设置定时开机时间: ${startupTime.value}`)
}

const cancelSchedule = async () => {
  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    let successCount = 0
    for (const device of onlineDevices.value) {
      try {
        await sendCommandToDevice(device, 'cancelSchedule')
        successCount++
      } catch (error) {
        console.error(`设备 ${device.ip} 取消定时任务失败:`, error)
      }
    }

    if (successCount > 0) {
      scheduleStatus.value = {
        type: 'success',
        title: '取消定时任务成功',
        message: `已为 ${successCount} 台设备取消定时任务`
      }
      console.log(`取消定时任务成功，影响 ${successCount} 台设备`)
    } else {
      scheduleStatus.value = {
        type: 'error',
        title: '取消定时任务失败',
        message: '所有设备操作失败'
      }
    }
  } catch (error) {
    console.error('取消定时任务失败:', error)
    scheduleStatus.value = {
      type: 'error',
      title: '取消定时任务失败',
      message: error.message
    }
  }
}

// 广播功能
const sendBroadcast = async () => {
  if (!broadcastMessage.value.trim()) return

  try {
    const onlineCount = onlineDevices.value.length
    if (onlineCount === 0) {
      alert('没有在线设备')
      return
    }

    broadcasting.value = true
    broadcastStatus.value = true
    broadcastProgress.value = 0
    broadcastStatusText.value = `正在向 ${onlineCount} 台设备广播消息...`

    let successCount = 0
    for (let i = 0; i < onlineDevices.value.length; i++) {
      const device = onlineDevices.value[i]
      try {
        await sendCommandToDevice(device, 'broadcast', {
          message: broadcastMessage.value,
          messageType: broadcastType.value,
          duration: broadcastDuration.value
        })
        successCount++
      } catch (error) {
        console.error(`设备 ${device.ip} 广播失败:`, error)
      }

      broadcastProgress.value = Math.round(((i + 1) / onlineDevices.value.length) * 100)
      broadcastStatusText.value = `正在向设备广播... (${i + 1}/${onlineDevices.value.length})`
    }

    if (successCount > 0) {
      broadcastStatusText.value = `广播完成！成功发送到 ${successCount} 台设备`
      console.log(`消息广播成功，影响 ${successCount} 台设备`)

      // 清空消息
      setTimeout(() => {
        clearBroadcast()
      }, 2000)
    } else {
      broadcastStatusText.value = '广播失败，所有设备都无法接收消息'
    }

    setTimeout(() => {
      broadcastStatus.value = false
    }, 3000)

  } catch (error) {
    console.error('消息广播失败:', error)
    broadcastStatusText.value = '广播失败: ' + error.message
  } finally {
    broadcasting.value = false
  }
}

const clearBroadcast = () => {
  broadcastMessage.value = ''
  broadcastType.value = 'warning'
  broadcastDuration.value = 5000
  broadcastStatus.value = false
  broadcastProgress.value = 0
  broadcastStatusText.value = ''
}

// 工具函数
const sendCommandToDevice = async (device, command, data = {}) => {
  return new Promise((resolve, reject) => {
    if (!device.ws || device.ws.readyState !== WebSocket.OPEN) {
      reject(new Error('设备未连接'))
      return
    }

    const message = {
      type: command,
      ...data
    }

    try {
      device.ws.send(JSON.stringify(message))
      console.log(`向设备 ${device.ip} 发送命令:`, message)
    } catch (error) {
      reject(new Error('发送命令失败'))
      return
    }

    // 增加超时时间到10秒，并改进超时处理
    const timeout = setTimeout(() => {
      device.ws.removeEventListener('message', handleResponse)
      // 对于某些命令，超时不一定意味着失败
      if (['mute', 'unmute', 'setVolume'].includes(command)) {
        console.warn(`设备 ${device.ip} 命令 ${command} 超时，但可能已执行`)
        resolve({ success: true, message: '命令已发送，但设备响应超时' })
      } else {
        reject(new Error('命令超时'))
      }
    }, 10000)

    // 监听响应
    const handleResponse = (event) => {
      try {
        const response = JSON.parse(event.data)

        // 检查多种可能的响应格式
        const expectedResponseTypes = [
          `${command}Response`,
          command === 'setVolume' ? 'setVolumeResponse' : null,
          command === 'mute' ? 'muteResponse' : null,
          command === 'unmute' ? 'unmuteResponse' : null
        ].filter(Boolean)

        if (expectedResponseTypes.includes(response.type)) {
          clearTimeout(timeout)
          device.ws.removeEventListener('message', handleResponse)

          if (response.success !== false) {
            resolve(response)
          } else {
            reject(new Error(response.message || response.error || '命令执行失败'))
          }
        }
        // 如果收到错误响应
        else if (response.type === 'error' && response.command === command) {
          clearTimeout(timeout)
          device.ws.removeEventListener('message', handleResponse)
          reject(new Error(response.message || '设备返回错误'))
        }
      } catch (error) {
        // 忽略解析错误，可能是其他消息
      }
    }

    device.ws.addEventListener('message', handleResponse)
  })
}

// 生命周期
onMounted(() => {
  deviceStore.refreshAllDevices()
})
</script>

<style scoped>
/* ControlNew 页面样式 */
.control-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

/* 控制卡片通用样式 */
.control-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 其他样式省略，保持与MonitorNew.vue一致 */
</style>

<style scoped>
/* 播放控制页面样式 */
/* 控制卡片通用样式 */
.control-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 概览部分 */
.overview-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #f9f9f9;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.stat-label {
  font-size: 14px;
  color: #86868b;
  margin: 0;
}

/* 定时任务部分 */
.schedule-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 24px 0;
}

.schedule-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.schedule-label {
  font-size: 14px;
  color: #1d1d1f;
  white-space: nowrap;
}

.time-input {
  padding: 10px 12px;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  font-size: 14px;
  color: #1d1d1f;
  min-width: 120px;
}

/* 音量控制部分 */
.volume-section {
  margin-bottom: 24px;
}

.volume-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.volume-slider {
  flex: 1;
  min-width: 200px;
}

.volume-label {
  font-size: 14px;
  color: #1d1d1f;
  margin-bottom: 8px;
  display: block;
}

.slider {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: #e5e5e7;
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007aff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 8px rgba(0, 122, 255, 0.1);
}

.volume-buttons {
  display: flex;
  gap: 12px;
}

/* 广播部分 */
.broadcast-section {
  margin-bottom: 24px;
}

.broadcast-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.broadcast-input {
  width: 100%;
}

.broadcast-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  font-size: 14px;
  color: #1d1d1f;
  resize: vertical;
  min-height: 100px;
  box-sizing: border-box;
}

.char-count {
  font-size: 12px;
  color: #86868b;
  text-align: right;
  margin-top: 8px;
}

.broadcast-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.option-label {
  font-size: 14px;
  color: #1d1d1f;
}

.option-select {
  padding: 10px 12px;
  border: 1px solid #e5e5e7;
  border-radius: 8px;
  font-size: 14px;
  color: #1d1d1f;
}

.broadcast-actions {
  display: flex;
  gap: 12px;
}

/* 设备表格部分 */
.devices-section {
  margin-bottom: 24px;
}

.devices-table {
  overflow-x: auto;
}

.device-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-table th,
.device-table td {
  padding: 16px 20px;
  text-align: left;
}

.device-table th {
  background: #f5f5f7;
  font-size: 14px;
  font-weight: 600;
  color: #86868b;
  border-bottom: 1px solid #e5e5e7;
}

.device-table td {
  font-size: 14px;
  color: #1d1d1f;
  border-bottom: 1px solid #e5e5e7;
}

.device-table tr:last-child td {
  border-bottom: none;
}

.device-table tr:hover td {
  background-color: #f9f9f9;
}

/* 按钮样式 */
.control-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: #f5f5f7;
  border-color: #c7c7cc;
}

.control-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.control-btn-primary:hover {
  background: #0056cc;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.25);
}

.control-btn-success {
  background: #34c759;
  border-color: #34c759;
  color: white;
}

.control-btn-success:hover {
  background: #28a745;
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.25);
}

.control-btn-warning {
  background: #ff9500;
  border-color: #ff9500;
  color: white;
}

.control-btn-warning:hover {
  background: #e08300;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.25);
}

.control-btn-danger {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

.control-btn-danger:hover {
  background: #d93025;
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.25);
}

.control-btn-large {
  padding: 12px 24px;
  font-size: 16px;
}

/* 状态和进度 */
.schedule-status,
.volume-status,
.broadcast-status {
  margin-top: 16px;
  width: 100%;
}

.status-alert {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
}

.alert-success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert-info {
  background: #e0f2fe;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.alert-warning {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.alert-error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.progress-bar {
  height: 4px;
  background: #e5e5e7;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #007aff;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.status-text {
  font-size: 14px;
  color: #86868b;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-page {
    padding: 16px;
  }

  .schedule-controls,
  .volume-controls,
  .broadcast-options,
  .broadcast-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .schedule-item {
    width: 100%;
  }

  .device-table th,
  .device-table td {
    padding: 12px 16px;
  }

  .page-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .control-btn {
    width: 100%;
  }

  .schedule-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .schedule-label {
    margin-bottom: 4px;
  }
}
.control-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f7;
}

.action-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn-primary:hover {
  background: #0056cc;
}

/* 设备选择 */
.device-selection {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-tabs {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.device-tab {
  padding: 12px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-tab:hover {
  background: #f5f5f7;
}

.device-tab-active {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.device-tab-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #34c759;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
}

.no-devices {
  text-align: center;
  padding: 40px;
  color: #86868b;
}

.no-devices p {
  margin: 0 0 16px 0;
  font-size: 16px;
}

/* 播放控制区域 */
.control-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 24px 0;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.control-btn {
  padding: 16px 24px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.control-btn:hover {
  background: #f5f5f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.control-btn-play {
  background: #34c759;
  border-color: #34c759;
  color: white;
}

.control-btn-play:hover {
  background: #28a745;
}

.control-btn-pause {
  background: #ff9500;
  border-color: #ff9500;
  color: white;
}

.control-btn-pause:hover {
  background: #e6850e;
}

.control-btn-stop {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

.control-btn-stop:hover {
  background: #d70015;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn:disabled:hover {
  background: white;
  transform: none;
  box-shadow: none;
}

/* 音量控制 */
.volume-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.volume-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.volume-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e5e7;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007aff;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007aff;
  cursor: pointer;
  border: none;
}

.volume-value {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  min-width: 40px;
  text-align: center;
}

.volume-buttons {
  display: flex;
  gap: 12px;
}

.volume-btn {
  padding: 8px 16px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.volume-btn:hover {
  background: #f5f5f7;
}

.volume-btn-mute {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

.volume-btn-mute:hover {
  background: #d70015;
}

/* 状态显示 */
.status-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f7;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 16px;
  font-weight: 500;
  color: #1d1d1f;
}

.status-value {
  font-size: 16px;
  color: #86868b;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-playing {
  background: #d1fae5;
  color: #065f46;
}

.status-paused {
  background: #fef3c7;
  color: #92400e;
}

.status-stopped {
  background: #fee2e2;
  color: #991b1b;
}

/* 进度条 */
.progress-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e5e7;
  margin-bottom: 16px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #5ac8fa);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #86868b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .device-tabs {
    flex-direction: column;
  }

  .control-grid {
    grid-template-columns: 1fr;
  }

  .volume-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .volume-buttons {
    justify-content: center;
  }
}
</style>