param([string]$action = "toggle")

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class WindowController {
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

    [DllImport("user32.dll")]
    public static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll")]
    public static extern bool BringWindowToTop(IntPtr hWnd);

    public const int KEYEVENTF_KEYUP = 0x0002;
    public const int VK_F11 = 0x7A;
    public const int VK_ESCAPE = 0x1B;
    public const int VK_CONTROL = 0x11;
    public const int SW_MAXIMIZE = 3;
    public const int SW_RESTORE = 9;
    public const int SW_SHOWMAXIMIZED = 3;

    // 置顶相关常量
    public static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
    public static readonly IntPtr HWND_NOTOPMOST = new IntPtr(-2);
    public const uint SWP_NOMOVE = 0x0002;
    public const uint SWP_NOSIZE = 0x0001;
    public const uint SWP_SHOWWINDOW = 0x0040;

    public static void SendF11() {
        keybd_event(VK_F11, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event(VK_F11, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendCtrlF11() {
        // 按下Ctrl
        keybd_event(VK_CONTROL, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        // 按下F11
        keybd_event(VK_F11, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        // 释放F11
        keybd_event(VK_F11, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        System.Threading.Thread.Sleep(20);
        // 释放Ctrl
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void SendEscape() {
        keybd_event(VK_ESCAPE, 0, 0, UIntPtr.Zero);
        System.Threading.Thread.Sleep(50);
        keybd_event(VK_ESCAPE, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
    }

    public static void MaximizeWindow() {
        IntPtr hwnd = GetForegroundWindow();
        ShowWindow(hwnd, SW_MAXIMIZE);
    }

    public static void SetTopMost() {
        IntPtr hwnd = GetForegroundWindow();
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }

    public static void EnterFullscreenAndStayOnTop() {
        IntPtr hwnd = GetForegroundWindow();
        // 先确保窗口获得焦点
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
        // 等待窗口获得焦点
        System.Threading.Thread.Sleep(500);

        // 先置顶
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        // 等待窗口稳定
        System.Threading.Thread.Sleep(800);

        // 根据设备类型发送不同的全屏快捷键
        SendF11(); // 默认F11，将通过参数控制

        // 等待全屏生效
        System.Threading.Thread.Sleep(1500);

        // 再次确保置顶和焦点
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }

    public static void EnterFullscreenAndStayOnTopLaptop() {
        IntPtr hwnd = GetForegroundWindow();
        // 先确保窗口获得焦点
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
        // 等待窗口获得焦点
        System.Threading.Thread.Sleep(500);

        // 先置顶
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        // 等待窗口稳定
        System.Threading.Thread.Sleep(800);

        // 笔记本使用Ctrl+F11
        SendCtrlF11();

        // 等待全屏生效
        System.Threading.Thread.Sleep(1500);

        // 再次确保置顶和焦点
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW);
        BringWindowToTop(hwnd);
        SetForegroundWindow(hwnd);
    }
}
"@

switch ($action) {
    "enter" { [WindowController]::EnterFullscreenAndStayOnTop(); Write-Output "Fullscreen ON with TopMost" }
    "exit" { [WindowController]::SendEscape(); Write-Output "Fullscreen OFF" }
    "toggle" { [WindowController]::SendF11(); Write-Output "Fullscreen TOGGLE" }
    "maximize" { [WindowController]::MaximizeWindow(); Write-Output "Window MAXIMIZED" }
    "topmost" { [WindowController]::SetTopMost(); Write-Output "Window set to TOPMOST" }
    "fullscreen_topmost" { [WindowController]::EnterFullscreenAndStayOnTop(); Write-Output "Fullscreen with TopMost STABLE (F11)" }
    "fullscreen_topmost_laptop" { [WindowController]::EnterFullscreenAndStayOnTopLaptop(); Write-Output "Fullscreen with TopMost STABLE (Ctrl+F11)" }
    default { Write-Output "Invalid action" }
}
