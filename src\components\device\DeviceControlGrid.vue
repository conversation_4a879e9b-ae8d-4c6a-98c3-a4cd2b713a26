<template>
  <div class="device-control-grid">
    <div v-if="devices.length === 0" class="empty-state">
      <n-empty description="暂无设备">
        <template #icon>
          <n-icon size="48" color="#909090">
            <HardwareChipOutline />
          </n-icon>
        </template>
      </n-empty>
    </div>

    <n-grid v-else :cols="24" :x-gap="16" :y-gap="16">
      <n-grid-item 
        v-for="device in devices" 
        :key="device.id"
        :span="24" :sm="12" :md="8" :lg="6" :xl="4"
      >
        <n-card 
          class="device-control-card"
          :class="{ 
            'device-online': device.connected, 
            'device-offline': !device.connected,
            'device-playing': device.status === 'playing'
          }"
          hoverable
        >
          <template #header>
            <div class="device-header">
              <div class="device-info">
                <n-icon size="20" :color="device.connected ? '#34C759' : '#FF3B30'">
                  <HardwareChipOutline />
                </n-icon>
                <div class="device-details">
                  <div class="device-name">{{ device.name || '未命名设备' }}</div>
                  <div class="device-address">{{ device.ip }}:{{ device.port }}</div>
                </div>
              </div>
              <n-tag 
                size="small" 
                :type="device.connected ? 'success' : 'error'"
              >
                {{ device.connected ? '在线' : '离线' }}
              </n-tag>
            </div>
          </template>

          <!-- 设备状态显示 -->
          <div class="device-status">
            <div class="status-item">
              <span class="status-label">播放状态:</span>
              <n-tag 
                size="tiny" 
                :type="getStatusType(device.status)"
              >
                {{ getStatusLabel(device.status) }}
              </n-tag>
            </div>
            
            <div class="status-item" v-if="device.currentMedia">
              <span class="status-label">当前内容:</span>
              <span class="status-value">{{ device.currentMedia.name || '未知' }}</span>
            </div>
            
            <div class="status-item">
              <span class="status-label">音量:</span>
              <span class="status-value">{{ device.volume || 50 }}%</span>
            </div>
          </div>

          <!-- 控制按钮 -->
          <div class="control-actions">
            <n-space :size="8" vertical>
              <!-- 播放控制 -->
              <n-button-group>
                <n-button 
                  size="small"
                  type="primary"
                  @click="handleAction(device, 'play')"
                  :disabled="!device.connected"
                >
                  <template #icon>
                    <n-icon><PlayOutline /></n-icon>
                  </template>
                </n-button>
                
                <n-button 
                  size="small"
                  @click="handleAction(device, 'pause')"
                  :disabled="!device.connected"
                >
                  <template #icon>
                    <n-icon><PauseOutline /></n-icon>
                  </template>
                </n-button>
                
                <n-button 
                  size="small"
                  @click="handleAction(device, 'stop')"
                  :disabled="!device.connected"
                >
                  <template #icon>
                    <n-icon><StopOutline /></n-icon>
                  </template>
                </n-button>
              </n-button-group>

              <!-- 其他控制 -->
              <n-space :size="4">
                <n-button 
                  size="tiny"
                  @click="handleAction(device, 'fullscreen')"
                  :disabled="!device.connected"
                >
                  <template #icon>
                    <n-icon><ExpandOutline /></n-icon>
                  </template>
                  全屏
                </n-button>
                
                <n-button 
                  size="tiny"
                  @click="showVolumeModal(device)"
                  :disabled="!device.connected"
                >
                  <template #icon>
                    <n-icon><VolumeHighOutline /></n-icon>
                  </template>
                  音量
                </n-button>
              </n-space>

              <!-- 高级控制 -->
              <n-dropdown 
                :options="getAdvancedOptions(device)" 
                @select="handleAdvancedAction"
                :disabled="!device.connected"
              >
                <n-button size="small" block>
                  <template #icon>
                    <n-icon><SettingsOutline /></n-icon>
                  </template>
                  高级控制
                </n-button>
              </n-dropdown>
            </n-space>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 音量调节模态框 -->
    <n-modal v-model:show="showVolumeControl">
      <n-card
        style="width: 400px"
        :title="`音量控制 - ${currentVolumeDevice?.name || currentVolumeDevice?.ip}`"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div class="volume-control">
          <n-space align="center" :size="16">
            <n-icon size="24"><VolumeHighOutline /></n-icon>
            <n-slider 
              v-model:value="volumeValue" 
              :min="0" 
              :max="100" 
              :step="5"
              style="flex: 1;"
              @update:value="handleVolumeChange"
            />
            <span class="volume-text">{{ volumeValue }}%</span>
          </n-space>
          
          <div class="volume-presets">
            <n-space :size="8">
              <n-button 
                size="small" 
                @click="setVolume(0)"
              >
                静音
              </n-button>
              <n-button 
                size="small" 
                @click="setVolume(25)"
              >
                25%
              </n-button>
              <n-button 
                size="small" 
                @click="setVolume(50)"
              >
                50%
              </n-button>
              <n-button 
                size="small" 
                @click="setVolume(75)"
              >
                75%
              </n-button>
              <n-button 
                size="small" 
                @click="setVolume(100)"
              >
                100%
              </n-button>
            </n-space>
          </div>
        </div>

        <template #footer>
          <n-space justify="end">
            <n-button @click="showVolumeControl = false">关闭</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  HardwareChipOutline,
  PlayOutline,
  PauseOutline,
  StopOutline,
  ExpandOutline,
  VolumeHighOutline,
  SettingsOutline,
  RefreshOutline,
  InformationCircleOutline
} from '@vicons/ionicons5'

const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['device-action'])

const message = useMessage()

// 响应式数据
const showVolumeControl = ref(false)
const currentVolumeDevice = ref(null)
const volumeValue = ref(50)

// 方法
const handleAction = (device, action) => {
  emit('device-action', device, { type: action })
}

const getStatusType = (status) => {
  const typeMap = {
    playing: 'success',
    paused: 'warning',
    stopped: 'default',
    error: 'error'
  }
  return typeMap[status] || 'default'
}

const getStatusLabel = (status) => {
  const labelMap = {
    playing: '播放中',
    paused: '已暂停',
    stopped: '已停止',
    error: '错误',
    idle: '空闲'
  }
  return labelMap[status] || '未知'
}

const showVolumeModal = (device) => {
  currentVolumeDevice.value = device
  volumeValue.value = device.volume || 50
  showVolumeControl.value = true
}

const handleVolumeChange = (volume) => {
  if (currentVolumeDevice.value) {
    emit('device-action', currentVolumeDevice.value, { 
      type: 'setVolume', 
      volume 
    })
  }
}

const setVolume = (volume) => {
  volumeValue.value = volume
  handleVolumeChange(volume)
}

const getAdvancedOptions = (device) => [
  {
    label: '刷新状态',
    key: `refresh_${device.id}`,
    icon: () => h(RefreshOutline)
  },
  {
    label: '设备信息',
    key: `info_${device.id}`,
    icon: () => h(InformationCircleOutline)
  }
]

const handleAdvancedAction = (key) => {
  const [action, deviceId] = key.split('_')
  const device = props.devices.find(d => d.id === deviceId)
  
  if (!device) return
  
  if (action === 'refresh') {
    emit('device-action', device, { type: 'refresh' })
  } else if (action === 'info') {
    message.info(`设备信息: ${device.name || device.ip}:${device.port}`)
  }
}
</script>

<style scoped>
.device-control-grid {
  width: 100%;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.device-control-card {
  height: 100%;
  border-radius: 12px;
  border: 2px solid #e5e5e7;
  transition: all 0.3s ease;
}

.device-control-card.device-online {
  border-color: #34C759;
}

.device-control-card.device-offline {
  border-color: #FF3B30;
  opacity: 0.7;
}

.device-control-card.device-playing {
  border-color: #007AFF;
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.2);
}

.device-control-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.device-details {
  flex: 1;
}

.device-name {
  font-weight: 600;
  font-size: 14px;
  color: #1a1a1a;
}

.device-address {
  font-size: 12px;
  color: #666;
}

.device-status {
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 12px;
  color: #666;
}

.status-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.control-actions {
  margin-top: 16px;
}

.volume-control {
  padding: 16px 0;
}

.volume-text {
  min-width: 40px;
  text-align: center;
  font-weight: 500;
}

.volume-presets {
  margin-top: 16px;
  text-align: center;
}

@media (max-width: 1366px) {
  .device-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
