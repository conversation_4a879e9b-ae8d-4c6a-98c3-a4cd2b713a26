
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1 - 看板</title>
    <base href="https://scada-ca.myoas.com/WebDesigner/Designer/">
    <style>
        /* 确保全屏显示 */
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        /* 隐藏可能的滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        /* 确保内容适应屏幕 */
        body {
            transform-origin: 0 0;
        }

        /* 添加加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-family: Arial, sans-serif;
            flex-direction: column;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
    </style>
    <script>
        // 资源加载错误计数
        let errorCount = 0;
        const maxErrors = 10;

        // 页面加载完成后隐藏加载指示器
        window.addEventListener('load', function() {
            setTimeout(function() {
                const overlay = document.querySelector('.loading-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }, 2000); // 延迟2秒隐藏，确保内容加载完成
        });

        // 监听资源加载错误
        window.addEventListener('error', function(e) {
            errorCount++;
            console.warn('资源加载失败:', e.target.src || e.target.href || e.message);

            if (errorCount > maxErrors) {
                const overlay = document.querySelector('.loading-overlay');
                const errorMsg = document.querySelector('.error-message');
                if (overlay && errorMsg) {
                    errorMsg.style.display = 'block';
                    errorMsg.textContent = '部分资源加载失败，但看板内容可能仍然可用';
                }
            }
        }, true);

        // 自动刷新功能（可选）
        // setInterval(function() {
        //     window.location.reload();
        // }, 300000); // 5分钟刷新一次

        // 禁用右键菜单和某些快捷键
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        document.addEventListener('keydown', function(e) {
            // 禁用F12, Ctrl+Shift+I等开发者工具快捷键
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
</head>
<body>
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
        <div>正在加载看板内容...</div>
        <div class="error-message"></div>
    </div>
    ﻿<!DOCTYPE html>
<html>

<head>
    <title id="title">运行时</title>
    <link rel='icon' type='image/png' href='https://scada-ca.myoas.com/WebDesigner/Designer/images/scada.png' />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style>
        html,
        body {
            padding: 0;
            margin: 0;
            background:transparent;
            /*background-color: #581313;*/
        }
    </style>

    <script type="text/javascript">
        let touchSupported=localStorage.getItem("touchSupported")==="false" ? false : true;
        // 结果: 支持或不支持： true or false 
        htconfig = {
            Default: {
                highlight: "#1ABC9C",
                label: "#FFF",
                labelSelect: "#FFF",
                transparent: "rgba(0,0,0,0.35)",
                titleBackground: "#2C3E50",
                titleIconBackground: "#868686",
                headerBackground: "#000",
                headerIconBackground: "#868686",
                headerSeparator: "#868686",
                headerLine: "#000",
                background: "#000",
                disabledBackground: "rgba(255,255,255,0.65)",
                toolTipBackground: "#FFFFE0",
                toolbarBackground: '#2C3E50',
                toolbarBorder:"#D9D9D9",
                toolbarLabelColor:"#D9D9D9",
                rectSelectBorder: "#2C3E50",
                rectSelectBackground: "rgba(0,0,0,0.35)",
                editPointBorder: "#2C3E50",
                editPointBackground: "#D9D9D9",
                dash: "#2C3E50",
                groupBackground: "#000",
                groupTitleBackground: "#2C3E50",
                gridBackground: "#000",
                gridCellBorderColor: "#868686",
                gridBlockColor: "#868686",
                reverse: "#868686",
                contentIconBackground: "#868686",
                contentLine: "#000",
                widgetBackground: "#ECF0F1",
                widgetBorder: "#D9D9D9",
                widgetIconBackground: "#868686",
                widgetIconBorder: "#868686",
                widgetIconGradient: "#D9D9D9",
                widgetIconHighlight: "#43AFF1",
                imageBackground: "#3498DB",
                imageGradient: "#000",
                widgetTitleHeight:0,
                toolTipDelay: 10,
                isTouchable:touchSupported
            }
        };
    </script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>

    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>

    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>

    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>

    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>


    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <!-- <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script> -->
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <!--“连线”交互器-->
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <!--“不规则图形”交互器-->
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <!--json图片-->
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <link href="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241" rel="stylesheet">
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>
    <script src="http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241"></script>

    <script type="text/javascript">
        //动态添加自定义字体样式    
        let style = document.createElement('style');
        style.type = "text/css";
        style.innerText = `@font-face {font-family:'Pangmenzhengdao';src:url('${document.location.pathname.replace('HT-2D-Editor.html','')}lib/fonts/Pangmenzhengdao.ttf')}`;
        document.getElementsByTagName('head')[0].appendChild(style);

        // 数据容器 承载Data数据的模型
        dataModel = new ht.DataModel();
        let scriptFormat=new ScriptFormat();
        let gaphViewUtil=new GaphViewUtil();
        saveDataAsExcel=function (data,name) {
            var sheet = XLSX.utils.json_to_sheet(data);
            let xlsxToolTemp = new xlsxTool();
            xlsxToolTemp.openDownloadDialog(xlsxToolTemp.sheet2blob(sheet),name+".xlsx");
        };

        readDataFromExcel=function (readDoneCallBack) {
            let xlsxToolTemp = new xlsxTool();
            xlsxToolTemp.importExcel(readDoneCallBack);
        };

        addListener = function() {
            var div = this.g2d.getView();
            let g2dTemp=this.g2d;
            div.style.background="rgba(0,0,0,0)";
            div.addEventListener('mousemove', function (e) {
                var node = self.g2d.getDataAt(e)

                if (node != undefined) {

                }
            });
            gaphViewUtil.initEvent(this.g2d);
        };
        let init=function() {
            // tooltip 文字提示的 div 样式编辑
            var tooltipDiv = ht.Default.getToolTipDiv();
            tooltipDiv.style.borderRadius = '5px';
            tooltipDiv.style.border = '1px solid #eee';

            // 拓扑组件
            var  g2d = this.g2d  = window.gv= new ht.graph.GraphView(dataModel);
            g2d.enableToolTip();
            g2d.setEditable(false);
            g2d.selectable=false;
            g2d.movable=false;
            g2d.pannable=false;
            g2d.pinchable=false;
            g2d.editable=false;
            g2d.enableFlow();
            g2d.enableDashFlow();
            g2d.addToDOM();
            g2d.setScrollBarVisible(false);
            addListener();
            g2d.fitContent(true,0,false);
            g2d.onBackgroundClicked=documentOnclick;
            createFullScreenButton();
        };
        let btnFullScreen;
       let  createFullScreenButton=function (){
           btnFullScreen = document.createElement('button');
           btnFullScreen.style['position']='fixed';
           btnFullScreen.style['bottom']=0;
           btnFullScreen.style['right']=0;
           btnFullScreen.style['border-radius']='5px';
           btnFullScreen.style['border-width']='0px';
           btnFullScreen.style['background']="rgba(0,0,0,0.1)";
           btnFullScreen.style['color']='#ffffff';
           btnFullScreen.style['font-size']='14px';
           btnFullScreen.style['height']='28px';
           btnFullScreen.style['width']='60px';
           btnFullScreen.innerText="全屏";
           btnFullScreen.style['display'] = 'none';
           btnFullScreen.onclick=function (e) {
               btnFullScreen.style['display'] = 'none';
               fullScreen();
           }
           document.body.appendChild(btnFullScreen);
        }


        fullScreen=function() {

             /*判断是否全屏*/
             var isFullscreen = document.fullScreenElement//W3C
                 || document.msFullscreenElement //IE
                 || document.mozFullScreenElement //火狐
                 || document.webkitFullscreenElement //谷歌
                 || false;
             if (!isFullscreen) {
                 var el = document.documentElement;
                 if (el.requestFullscreen) {
                     el.requestFullscreen();
                 } else if (el.mozRequestFullScreen) {
                     el.mozRequestFullScreen();
                 } else if (el.webkitRequestFullscreen) {
                     el.webkitRequestFullscreen();
                 } else if (el.msRequestFullscreen) {
                     el.msRequestFullscreen();
                 }
             } else {
                 if (document.exitFullscreen) {
                     document.exitFullscreen();
                 } else if (document.msExitFullscreen) {
                     document.msExitFullscreen();
                 } else if (document.mozCancelFullScreen) {
                     document.mozCancelFullScreen();
                 } else if (document.webkitCancelFullScreen) {
                     document.webkitCancelFullScreen();
                 }
             }
         };

        let documentOnclick=function(){
            if(btnFullScreen) {
                btnFullScreen.style['display'] = 'block';
                setTimeout(function () {
                    btnFullScreen.style['display'] = 'none';
                }, 3000);
            }
        }

        $(document).ready(function(){
            var obj=GetRequest("RunTime");
            if(obj!=null&&obj!=undefined&&obj.userName!=null) {
              var aa = document.getElementById('title');
              aa.innerText= langInstance.lang.runtime;
               updateInfos(obj);
               if(obj.projectId){
               loadProjectRunById(obj.projectId,dataModel);
               }else if(obj.objectId){
                   loadProjectHmi(obj.projectName,dataModel);
               }else if(obj.userName&&!obj.objectId){
                   loadProject(obj.projectName,dataModel);
               }
               let div= ht.Default.getToolTipDiv();
               div.style['white-space']='pre-wrap';
            }
            // if(obj&&obj.templateId){
            //     var aa = document.getElementById('title');
            //     aa.innerText= langInstance.lang.runtime;
            //     updateInfos(obj);
            //     if(obj.projectId){
            //         loadProjectRunById(obj.projectId,dataModel);
            //     }else if(obj.templateId){
            //         loadProjectHmi(obj.templateId,obj.deviceId,obj.deviceCode,dataModel);
            //     }else if(obj.userName){
            //         loadProject(obj.projectName,dataModel);
            //     }
            //     let div= ht.Default.getToolTipDiv();
            //     div.style['white-space']='pre-wrap';
            // }

        });
        $(window).resize(function(){
            // location.reload(true);
        });
    </script>

</head>

<body onclick="documentOnclick"  onload="init();" >

</body>
<style>
    @font-face {
        font-family: Pangmenzhengdao;
        src: url("http://localhost:9004/proxy/https%3A%2F%2Fscada-ca.myoas.com%2FWebDesigner%2FDesigner%2F%241");
    }
</style>
</html>

</body>
</html>
    