# WebSocket 服务端模拟器

这是一个简单的 WebSocket 服务端模拟器，用于演示 Pad 控制端的完整工作流程。

## 🚀 快速启动

### 1. 安装依赖
```bash
cd server-mock
npm install
```

### 2. 启动服务器
```bash
npm start
```

服务器将在 `ws://localhost:8080` 启动。

## 📋 功能特性

### 支持的命令

1. **heartbeat** - 心跳检测
   ```json
   { "type": "heartbeat" }
   ```

2. **listFiles** - 获取文件列表
   ```json
   { "type": "listFiles" }
   ```

3. **playFile** - 播放文件
   ```json
   {
     "type": "playFile",
     "file": {
       "path": "/shared/videos/demo.mp4",
       "name": "演示视频.mp4",
       "type": "video"
     }
   }
   ```

4. **播放控制** - play/pause/stop
   ```json
   { "type": "play" }
   { "type": "pause" }
   { "type": "stop" }
   ```

5. **音量控制** - volume
   ```json
   { "type": "volume", "value": 50 }
   ```

### 模拟文件列表

服务器包含以下模拟文件：
- 公司介绍视频.mp4 (156.8 MB)
- 产品演示.mp4 (89.2 MB)
- 培训课件.pptx (12.5 MB)
- 公司Logo.png (512 KB)
- 用户手册.pdf (8.9 MB)
- 宣传图片.jpg (2.1 MB)

## 🔧 使用方法

### 1. 启动服务端
```bash
cd server-mock
npm start
```

### 2. 启动 Pad 控制端
```bash
cd ..
npm run dev
```

### 3. 连接测试
1. 在 Pad 控制端的"设备管理"页面添加设备：
   - IP: `localhost` 或 `127.0.0.1`
   - 端口: `8080`

2. 点击"连接"建立 WebSocket 连接

3. 在"内容管理"页面：
   - 选择已连接的设备
   - 点击"获取文件列表"
   - 浏览并选择文件播放

## 📊 日志输出

服务器会输出详细的日志信息：
```
🚀 WebSocket 服务器启动在端口 8080
📁 模拟共享文件夹包含 6 个文件
📱 新的 Pad 控制端连接: ::ffff:127.0.0.1
📨 收到命令: { type: 'listFiles' }
📂 返回共享文件夹内容
🎬 播放文件: 公司介绍视频.mp4
📍 文件路径: /shared/videos/company-intro.mp4
```

## 🛠 开发模式

使用 nodemon 自动重启：
```bash
npm run dev
```

## 🔍 故障排除

### 连接失败
- 确认服务器已启动
- 检查端口 8080 是否被占用
- 确认防火墙设置

### 命令无响应
- 查看服务器日志
- 确认消息格式正确
- 检查 WebSocket 连接状态

## 📞 技术支持

如有问题，请查看：
1. 服务器控制台日志
2. 浏览器开发者工具 WebSocket 面板
3. Pad 控制端控制台日志

---

**模拟服务器** - 让开发和测试变得简单！
