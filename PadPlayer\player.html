<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大屏播放器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #fff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            overflow: hidden;
        }

        #player-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #video-player {
            width: 100vw;
            height: 100vh;
            object-fit: cover; /* 铺满全屏，保持比例 */
        }

        #image-player {
            width: 100vw;
            height: 100vh;
            object-fit: cover; /* 铺满全屏，保持比例 */
        }

        #html-player {
            width: 100vw;
            height: 100vh;
            border: none;
        }

        /* 全屏播放时的样式 */
        .fullscreen-content {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9998 !important;
            background: #000 !important;
        }

        .hidden {
            display: none !important;
        }

        /* 通知样式 */
        #notification-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            pointer-events: none;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification {
            background: rgba(0, 0, 0, 0.95);
            border-radius: 20px;
            padding: 60px 80px;
            margin: 0;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
            border: 4px solid;
            animation: slideIn 0.8s ease-out;
            max-width: 90vw;
            max-height: 80vh;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 常驻通知样式 */
        .notification.persistent {
            animation: slideIn 0.8s ease-out;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 1;
        }

        .notification.warning {
            border-color: #ff9800;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(0, 0, 0, 0.95));
            box-shadow: 0 20px 60px rgba(255, 152, 0, 0.3);
        }

        .notification.error {
            border-color: #f44336;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(0, 0, 0, 0.95));
            box-shadow: 0 20px 60px rgba(244, 67, 54, 0.3);
        }

        .notification.info {
            border-color: #2196f3;
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(0, 0, 0, 0.95));
            box-shadow: 0 20px 60px rgba(33, 150, 243, 0.3);
        }

        .notification.success {
            border-color: #4caf50;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(0, 0, 0, 0.95));
            box-shadow: 0 20px 60px rgba(76, 175, 80, 0.3);
        }

        .notification-title {
            font-size: calc(100vh / 12);  /* 屏幕高度的1/12 */
            font-weight: bold;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .notification-message {
            font-size: calc(100vh / 8);   /* 屏幕高度的1/8，约占屏幕1/3 */
            line-height: 1.2;
            color: #fff;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            word-wrap: break-word;
            max-width: 100%;
        }

        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5) rotateY(90deg);
            }
            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1.05) rotateY(0deg);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1) rotateY(0deg);
            }
        }

        @keyframes slideOut {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1) rotateY(0deg);
            }
            50% {
                opacity: 0.5;
                transform: translate(-50%, -50%) scale(1.05) rotateY(-45deg);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5) rotateY(-90deg);
            }
        }

        .notification.slide-out {
            animation: slideOut 0.6s ease-in forwards;
        }

        /* 响应式字体大小 */
        @media (max-width: 768px) {
            .notification-title {
                font-size: calc(100vh / 10);
            }
            .notification-message {
                font-size: calc(100vh / 6);
            }
        }

        @media (min-width: 1920px) {
            .notification-title {
                font-size: calc(100vh / 15);
            }
            .notification-message {
                font-size: calc(100vh / 10);
            }
        }

        /* 通知容器居中定位 */
        .notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* 连接状态指示器 */
        #connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }

        #connection-status.connected {
            background: #4caf50;
            color: white;
        }

        #connection-status.disconnected {
            background: #f44336;
            color: white;
        }

        #connection-status.connecting {
            background: #ff9800;
            color: white;
        }





        /* 用户交互触发器样式 */
        .interaction-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            cursor: pointer;
            transition: opacity 0.5s ease;
        }

        .interaction-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .interaction-content {
            text-align: center;
            color: white;
            user-select: none;
        }

        .interaction-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .interaction-content p {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .click-hint {
            font-size: 2rem;
            animation: bounce 2s infinite;
            color: #007AFF;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* 自动播放提示 */
        .autoplay-hint {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            z-index: 9999;
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
    </style>
</head>
<body>
    <div id="connection-status" class="disconnected">未连接</div>

    <div id="notification-container"></div>

    <!-- 用户交互触发器 -->
    <div id="interaction-trigger" class="interaction-overlay">
        <div class="interaction-content">
            <h2>🖥️ 大屏播放器</h2>
            <p>点击任意位置开始播放</p>
            <div class="click-hint">👆 点击屏幕</div>
        </div>
    </div>

    <div id="player-container">
        <!-- 视频播放器 -->
        <video id="video-player" class="hidden" controls autoplay loop>
            您的浏览器不支持视频播放
        </video>

        <!-- 图片播放器 -->
        <img id="image-player" class="hidden" alt="图片内容">

        <!-- HTML内容播放器 -->
        <iframe id="html-player" class="hidden"></iframe>
    </div>

    <script>
        class BigScreenPlayer {
            constructor() {
                this.ws = null
                this.reconnectAttempts = 0
                this.maxReconnectAttempts = 5
                this.reconnectDelay = 3000
                this.serverAddress = `${window.location.hostname}:9001`
                this.userInteracted = false
                this.pendingContent = null
                this.currentNotification = null

                this.initElements()
                this.initEventListeners()
                this.initUserInteraction()
                this.connect()
            }

            initElements() {
                this.statusEl = document.getElementById('connection-status')
                this.notificationContainer = document.getElementById('notification-container')
                this.interactionTrigger = document.getElementById('interaction-trigger')
                this.videoPlayer = document.getElementById('video-player')
                this.imagePlayer = document.getElementById('image-player')
                this.htmlPlayer = document.getElementById('html-player')
            }



            initEventListeners() {
                // 键盘事件监听
                document.addEventListener('keydown', (event) => {
                    switch (event.key) {
                        case 'Escape':
                            this.exitFullscreen()
                            this.removeFullscreenStyles()
                            break
                        case 'F11':
                            event.preventDefault()
                            if (document.fullscreenElement) {
                                this.exitFullscreen()
                                this.removeFullscreenStyles()
                            } else {
                                this.enterFullscreen()
                            }
                            break
                        case ' ': // 空格键暂停/播放
                            event.preventDefault()
                            if (!this.videoPlayer.classList.contains('hidden')) {
                                if (this.videoPlayer.paused) {
                                    this.videoPlayer.play()
                                } else {
                                    this.videoPlayer.pause()
                                }
                            }
                            break
                    }
                })

                // 全屏状态变化监听
                document.addEventListener('fullscreenchange', () => {
                    if (!document.fullscreenElement) {
                        this.removeFullscreenStyles()
                    }
                })

                // 双击进入/退出全屏
                document.addEventListener('dblclick', () => {
                    if (document.fullscreenElement) {
                        this.exitFullscreen()
                    } else {
                        this.enterFullscreen()
                    }
                })

                // 添加广播控制快捷键
                document.addEventListener('keydown', (event) => {
                    switch (event.key.toLowerCase()) {
                        case 'c': // C键清除当前通知
                        case 'escape': // ESC键也可以清除通知
                            if (event.key.toLowerCase() === 'c' || event.key === 'Escape') {
                                event.preventDefault()
                                this.dismissCurrentNotification()
                            }
                            break
                    }
                })
            }

            removeFullscreenStyles() {
                this.videoPlayer.classList.remove('fullscreen-content')
                this.imagePlayer.classList.remove('fullscreen-content')
                this.htmlPlayer.classList.remove('fullscreen-content')
            }

            initUserInteraction() {
                // 用户交互触发器点击事件
                this.interactionTrigger.addEventListener('click', (event) => {
                    console.log('用户点击了交互触发器')
                    event.preventDefault()
                    event.stopPropagation()
                    this.handleUserInteraction()
                })

                // 监听任何用户交互 - 使用捕获阶段确保能捕获到事件
                const interactionEvents = ['click', 'touchstart', 'keydown']
                interactionEvents.forEach(event => {
                    document.addEventListener(event, (e) => {
                        if (!this.userInteracted) {
                            console.log(`检测到用户交互: ${event}`)
                            this.handleUserInteraction()
                        }
                    }, { once: true, capture: true })
                })

                // 自动模拟用户交互（延迟执行，给用户时间手动点击）
                setTimeout(() => {
                    if (!this.userInteracted) {
                        console.log('5秒后自动模拟用户交互...')
                        this.simulateUserInteraction()
                    }
                }, 5000) // 5秒后自动模拟

                // 备用自动模拟（更短延迟）
                setTimeout(() => {
                    if (!this.userInteracted) {
                        console.log('2秒后备用自动模拟...')
                        this.forceUserInteraction()
                    }
                }, 2000) // 2秒后备用模拟
            }

            handleUserInteraction() {
                if (this.userInteracted) {
                    console.log('用户交互已经激活，跳过')
                    return
                }

                console.log('✅ 用户交互已触发，激活自动播放权限')
                this.userInteracted = true

                // 隐藏交互触发器
                this.interactionTrigger.classList.add('hidden')
                setTimeout(() => {
                    this.interactionTrigger.style.display = 'none'
                }, 500)

                // 立即测试自动播放权限
                this.testAutoplayPermission()

                // 如果有待播放的内容，立即播放
                if (this.pendingContent) {
                    console.log('播放待处理的内容:', this.pendingContent)
                    this.playPendingContent()
                }

                // 显示提示
                this.showAutoplayHint('✅ 用户交互已激活，可以自动播放和全屏')
            }

            testAutoplayPermission() {
                // 创建一个测试视频元素来验证自动播放权限
                const testVideo = document.createElement('video')
                testVideo.muted = true
                testVideo.style.display = 'none'
                document.body.appendChild(testVideo)

                testVideo.play().then(() => {
                    console.log('✅ 自动播放权限已获得')
                    document.body.removeChild(testVideo)
                }).catch((error) => {
                    console.log('❌ 自动播放权限仍然受限:', error)
                    document.body.removeChild(testVideo)
                })
            }

            simulateUserInteraction() {
                console.log('🤖 模拟用户点击事件...')

                try {
                    // 创建多种模拟事件
                    const events = [
                        new MouseEvent('mousedown', { bubbles: true, cancelable: true, view: window }),
                        new MouseEvent('mouseup', { bubbles: true, cancelable: true, view: window }),
                        new MouseEvent('click', { bubbles: true, cancelable: true, view: window }),
                        new TouchEvent('touchstart', { bubbles: true, cancelable: true }),
                        new KeyboardEvent('keydown', { bubbles: true, cancelable: true, key: 'Enter' })
                    ]

                    // 依次触发事件
                    events.forEach((event, index) => {
                        setTimeout(() => {
                            try {
                                this.interactionTrigger.dispatchEvent(event)
                                console.log(`模拟事件 ${index + 1}:`, event.type)
                            } catch (e) {
                                console.log(`模拟事件 ${index + 1} 失败:`, e.message)
                            }
                        }, index * 100)
                    })
                } catch (error) {
                    console.error('模拟用户交互失败:', error)
                    this.forceUserInteraction()
                }
            }

            forceUserInteraction() {
                console.log('🔧 强制激活用户交互...')

                // 直接设置用户交互状态
                if (!this.userInteracted) {
                    this.userInteracted = true
                    console.log('✅ 强制激活成功')

                    // 隐藏交互触发器
                    this.interactionTrigger.classList.add('hidden')
                    setTimeout(() => {
                        this.interactionTrigger.style.display = 'none'
                    }, 100)

                    // 如果有待播放的内容，立即播放
                    if (this.pendingContent) {
                        console.log('播放待处理的内容:', this.pendingContent)
                        this.playPendingContent()
                    }

                    this.showAutoplayHint('🔧 已强制激活播放权限')
                }
            }



            playVideoWithRetry(autoFullscreen, attempt = 0) {
                const maxAttempts = 3

                console.log(`尝试播放视频 (第${attempt + 1}次)`)

                this.videoPlayer.play().then(() => {
                    console.log('✅ 视频播放成功')
                    if (autoFullscreen) {
                        // 视频开始播放后尝试全屏
                        setTimeout(() => this.enterFullscreen(), 1000)
                    }
                }).catch(error => {
                    console.error(`视频播放失败 (第${attempt + 1}次):`, error.name, error.message)

                    if (error.name === 'NotAllowedError') {
                        // 用户交互权限问题
                        if (!this.userInteracted) {
                            console.log('❌ 缺少用户交互，强制激活...')
                            this.forceUserInteraction()

                            // 重试播放
                            setTimeout(() => {
                                if (attempt < maxAttempts) {
                                    this.playVideoWithRetry(autoFullscreen, attempt + 1)
                                }
                            }, 500)
                            return
                        }

                        // 尝试静音播放
                        console.log('🔇 尝试静音播放...')
                        this.videoPlayer.muted = true
                        this.videoPlayer.play().then(() => {
                            console.log('✅ 静音播放成功')
                            this.showAutoplayHint('🔇 已切换到静音播放模式')
                            if (autoFullscreen) {
                                setTimeout(() => this.enterFullscreen(), 1000)
                            }
                        }).catch(mutedError => {
                            console.error('❌ 静音播放也失败:', mutedError)
                            this.showNotification(`视频播放失败: ${mutedError.message}`, 'error', 5000)
                        })
                    } else {
                        // 其他错误，重试
                        if (attempt < maxAttempts) {
                            console.log(`⏳ ${1000}ms后重试...`)
                            setTimeout(() => {
                                this.playVideoWithRetry(autoFullscreen, attempt + 1)
                            }, 1000)
                        } else {
                            console.error('❌ 视频播放重试次数已达上限')
                            this.showNotification(`视频播放失败: ${error.message}`, 'error', 5000)
                        }
                    }
                })
            }

            playPendingContent() {
                if (!this.pendingContent) return

                const { type, src, autoFullscreen } = this.pendingContent
                this.pendingContent = null

                // 延迟一点时间确保用户交互生效
                setTimeout(() => {
                    switch (type) {
                        case 'video':
                            this.playVideoNow(src, autoFullscreen)
                            break
                        case 'image':
                            this.showImageNow(src, autoFullscreen)
                            break
                        case 'html':
                            this.showHtmlNow(src, autoFullscreen)
                            break
                    }
                }, 100)
            }

            showAutoplayHint(message) {
                const hint = document.createElement('div')
                hint.className = 'autoplay-hint'
                hint.textContent = message
                document.body.appendChild(hint)

                setTimeout(() => {
                    hint.style.opacity = '0'
                    setTimeout(() => {
                        if (hint.parentNode) {
                            hint.parentNode.removeChild(hint)
                        }
                    }, 500)
                }, 3000)
            }

            connect() {
                try {
                    this.updateStatus('connecting', '连接中...')
                    this.ws = new WebSocket(`ws://${this.serverAddress}`)
                    
                    this.ws.onopen = () => {
                        console.log('✅ 播放器已连接到服务器')
                        this.updateStatus('connected', '已连接')
                        this.reconnectAttempts = 0
                    }

                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data)
                            this.handleMessage(data)
                        } catch (error) {
                            console.error('解析消息失败:', error)
                        }
                    }

                    this.ws.onclose = () => {
                        console.log('❌ 播放器连接已断开')
                        this.updateStatus('disconnected', '连接断开')
                        this.scheduleReconnect()
                    }

                    this.ws.onerror = (error) => {
                        console.error('WebSocket错误:', error)
                        this.updateStatus('disconnected', '连接错误')
                    }
                } catch (error) {
                    console.error('连接失败:', error)
                    this.updateStatus('disconnected', '连接失败')
                    this.scheduleReconnect()
                }
            }

            scheduleReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++
                    console.log(`🔄 ${this.reconnectDelay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
                    setTimeout(() => this.connect(), this.reconnectDelay)
                } else {
                    console.log('❌ 达到最大重连次数，停止重连')
                    this.updateStatus('disconnected', '连接失败')
                }
            }

            updateStatus(status, text) {
                this.statusEl.className = status
                this.statusEl.textContent = text
            }

            handleMessage(data) {
                console.log('收到消息:', data)

                switch (data.type) {
                    case 'video':
                        this.playVideo(data.src, data.autoFullscreen)
                        break
                    case 'image':
                        this.showImage(data.src, data.autoFullscreen)
                        break
                    case 'html':
                        this.showHtml(data.src, data.autoFullscreen)
                        break
                    case 'showNotification':
                        this.showNotification(data.message, data.messageType, data.duration)
                        break
                    default:
                        console.log('未知消息类型:', data.type)
                }
            }

            hideAllPlayers() {
                this.videoPlayer.classList.add('hidden')
                this.imagePlayer.classList.add('hidden')
                this.htmlPlayer.classList.add('hidden')
            }

            playVideo(src, autoFullscreen = false) {
                console.log('播放视频:', src, '自动全屏:', autoFullscreen)

                // 如果用户还没有交互，暂存内容
                if (!this.userInteracted) {
                    console.log('用户尚未交互，暂存视频内容')
                    this.pendingContent = { type: 'video', src, autoFullscreen }
                    this.showAutoplayHint('请点击屏幕以开始播放视频')
                    return
                }

                this.playVideoNow(src, autoFullscreen)
            }

            playVideoNow(src, autoFullscreen = false) {
                console.log('立即播放视频:', src, '自动全屏:', autoFullscreen)
                this.hideAllPlayers()

                // 构建完整的视频URL
                const videoUrl = src.startsWith('http') ? src : `http://${window.location.hostname}:9004/shared/${encodeURIComponent(src)}`
                this.videoPlayer.src = videoUrl
                this.videoPlayer.classList.remove('hidden')

                // 设置视频属性
                this.videoPlayer.loop = true // 自动循环播放
                this.videoPlayer.muted = false // 取消静音

                // 添加全屏样式
                if (autoFullscreen) {
                    this.videoPlayer.classList.add('fullscreen-content')
                    // 延迟进入全屏，确保视频加载
                    setTimeout(() => this.enterFullscreen(), 500)
                }

                // 播放视频 - 增强错误处理
                this.playVideoWithRetry(autoFullscreen)

                // 视频加载完成后的处理
                this.videoPlayer.onloadeddata = () => {
                    console.log('视频数据加载完成')
                    if (autoFullscreen) {
                        setTimeout(() => this.enterFullscreen(), 500)
                    }
                }

                // 视频可以播放时的处理
                this.videoPlayer.oncanplay = () => {
                    console.log('视频可以播放')
                    if (autoFullscreen) {
                        setTimeout(() => this.enterFullscreen(), 200)
                    }
                }
            }

            showImage(src, autoFullscreen = false) {
                console.log('显示图片:', src, '自动全屏:', autoFullscreen)

                // 如果用户还没有交互，暂存内容
                if (!this.userInteracted) {
                    console.log('用户尚未交互，暂存图片内容')
                    this.pendingContent = { type: 'image', src, autoFullscreen }
                    this.showAutoplayHint('请点击屏幕以显示图片')
                    return
                }

                this.showImageNow(src, autoFullscreen)
            }

            showImageNow(src, autoFullscreen = false) {
                console.log('立即显示图片:', src, '自动全屏:', autoFullscreen)
                this.hideAllPlayers()

                // 构建完整的图片URL
                const imageUrl = src.startsWith('http') ? src : `http://${window.location.hostname}:9004/shared/${encodeURIComponent(src)}`
                this.imagePlayer.src = imageUrl
                this.imagePlayer.classList.remove('hidden')

                // 添加全屏样式
                if (autoFullscreen) {
                    this.imagePlayer.classList.add('fullscreen-content')
                    setTimeout(() => this.enterFullscreen(), 300)
                }

                // 图片加载完成后确保全屏
                this.imagePlayer.onload = () => {
                    console.log('图片加载完成')
                    if (autoFullscreen) {
                        setTimeout(() => this.enterFullscreen(), 200)
                    }
                }

                this.imagePlayer.onerror = () => {
                    console.error('图片加载失败:', src)
                    this.showNotification(`图片加载失败: ${src}`, 'error', 3000)
                }
            }

            showHtml(src, autoFullscreen = false) {
                console.log('显示HTML:', src, '自动全屏:', autoFullscreen)

                // 如果用户还没有交互，暂存内容
                if (!this.userInteracted) {
                    console.log('用户尚未交互，暂存HTML内容')
                    this.pendingContent = { type: 'html', src, autoFullscreen }
                    this.showAutoplayHint('请点击屏幕以显示网页')
                    return
                }

                this.showHtmlNow(src, autoFullscreen)
            }

            showHtmlNow(src, autoFullscreen = false) {
                console.log('立即显示HTML:', src, '自动全屏:', autoFullscreen)
                this.hideAllPlayers()

                // 构建完整的HTML URL
                const htmlUrl = src.startsWith('http') ? src : `http://${window.location.hostname}:9004/shared/${encodeURIComponent(src)}`
                this.htmlPlayer.src = htmlUrl
                this.htmlPlayer.classList.remove('hidden')

                // 添加全屏样式
                if (autoFullscreen) {
                    this.htmlPlayer.classList.add('fullscreen-content')
                    setTimeout(() => this.enterFullscreen(), 500)
                }

                // HTML加载完成后确保全屏
                this.htmlPlayer.onload = () => {
                    console.log('HTML加载完成')
                    if (autoFullscreen) {
                        setTimeout(() => this.enterFullscreen(), 300)
                    }
                }
            }

            // 进入全屏模式 - 增强版本
            enterFullscreen() {
                if (!this.userInteracted) {
                    console.log('❌ 用户尚未交互，无法进入全屏，强制激活...')
                    this.forceUserInteraction()

                    // 延迟后重试全屏
                    setTimeout(() => {
                        this.enterFullscreen()
                    }, 500)
                    return
                }

                console.log('🖥️ 尝试进入全屏模式...')

                // 尝试API全屏
                this.attemptFullscreen(0)
            }

            attemptFullscreen(attempt) {
                const maxAttempts = 5

                if (attempt >= maxAttempts) {
                    console.log('❌ 全屏尝试次数已达上限')
                    this.showAutoplayHint('⚠️ 无法进入全屏模式，请手动按F11')
                    return
                }

                console.log(`🖥️ 全屏尝试 ${attempt + 1}/${maxAttempts}`)

                try {
                    let fullscreenPromise = null

                    // 尝试不同的全屏API
                    if (document.documentElement.requestFullscreen) {
                        fullscreenPromise = document.documentElement.requestFullscreen({ navigationUI: "hide" })
                    } else if (document.documentElement.webkitRequestFullscreen) {
                        fullscreenPromise = document.documentElement.webkitRequestFullscreen()
                    } else if (document.documentElement.msRequestFullscreen) {
                        fullscreenPromise = document.documentElement.msRequestFullscreen()
                    } else if (document.documentElement.mozRequestFullScreen) {
                        fullscreenPromise = document.documentElement.mozRequestFullScreen()
                    }

                    if (fullscreenPromise && typeof fullscreenPromise.then === 'function') {
                        fullscreenPromise.then(() => {
                            console.log('✅ 全屏模式已激活')
                            this.showAutoplayHint('✅ 已进入全屏模式')
                        }).catch((error) => {
                            console.error(`❌ 全屏尝试 ${attempt + 1} 失败:`, error.name, error.message)

                            if (error.name === 'NotAllowedError' && !this.userInteracted) {
                                console.log('🔧 缺少用户交互，强制激活后重试...')
                                this.forceUserInteraction()
                            }

                            // 延迟后重试
                            setTimeout(() => {
                                this.attemptFullscreen(attempt + 1)
                            }, 1500)
                        })
                    } else {
                        console.log('📤 全屏请求已发送（无Promise）')
                        // 检查是否成功进入全屏
                        setTimeout(() => {
                            if (!this.isFullscreen()) {
                                console.log(`❌ 全屏检查失败，尝试重试 ${attempt + 1}`)
                                this.attemptFullscreen(attempt + 1)
                            } else {
                                console.log('✅ 全屏模式已激活（检查确认）')
                            }
                        }, 1500)
                    }
                } catch (error) {
                    console.error(`❌ 全屏尝试 ${attempt + 1} 异常:`, error)
                    setTimeout(() => {
                        this.attemptFullscreen(attempt + 1)
                    }, 1500)
                }
            }

            isFullscreen() {
                return !!(document.fullscreenElement || document.webkitFullscreenElement ||
                         document.msFullscreenElement || document.mozFullScreenElement)
            }



            // 退出全屏模式
            exitFullscreen() {
                try {
                    if (document.exitFullscreen) {
                        document.exitFullscreen()
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen()
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen()
                    }
                    console.log('已退出全屏模式')
                } catch (error) {
                    console.error('退出全屏失败:', error)
                }
            }

            showNotification(message, type = 'info', duration = 0) {
                console.log('显示广播通知:', { message, type })

                // 清除现有通知（新消息覆盖旧消息）
                if (this.currentNotification) {
                    this.clearCurrentNotification()
                }

                const notification = document.createElement('div')
                notification.className = `notification ${type} persistent`

                // 只显示消息内容，让消息更突出
                notification.innerHTML = `
                    <div class="notification-message">${message}</div>
                `

                this.notificationContainer.appendChild(notification)
                this.currentNotification = notification

                // 播放提示音
                this.playNotificationSound(type)

                // 永久显示，直到下一条消息覆盖
                console.log('广播消息永久显示，直到下一条消息覆盖')
            }

            clearCurrentNotification() {
                if (this.currentNotification && this.currentNotification.parentNode) {
                    this.currentNotification.parentNode.removeChild(this.currentNotification)
                    this.currentNotification = null
                }
            }

            // 手动清除当前通知的方法（可以通过键盘或其他方式触发）
            dismissCurrentNotification() {
                console.log('手动清除当前通知')
                this.clearCurrentNotification()
            }

            playNotificationSound(type) {
                // 可以根据通知类型播放不同的提示音
                try {
                    // 创建音频上下文播放提示音
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)()
                    const oscillator = audioContext.createOscillator()
                    const gainNode = audioContext.createGain()

                    oscillator.connect(gainNode)
                    gainNode.connect(audioContext.destination)

                    // 根据通知类型设置不同频率
                    const frequencies = {
                        warning: 800,
                        error: 400,
                        info: 600,
                        success: 1000
                    }

                    oscillator.frequency.setValueAtTime(frequencies[type] || 600, audioContext.currentTime)
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)

                    oscillator.start(audioContext.currentTime)
                    oscillator.stop(audioContext.currentTime + 0.3)
                } catch (error) {
                    // 忽略音频播放错误
                    console.log('提示音播放失败:', error.message)
                }
            }
        }

        // 启动播放器
        window.addEventListener('DOMContentLoaded', () => {
            const player = new BigScreenPlayer()

            // 页面加载后立即尝试激活用户交互
            setTimeout(() => {
                if (!player.userInteracted) {
                    console.log('🚀 页面加载完成，尝试激活用户交互...')
                    player.forceUserInteraction()
                }
            }, 1000)
        })

        // 页面可见性变化时重新激活
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                console.log('📱 页面重新可见，检查用户交互状态...')
                // 可以在这里添加重新激活逻辑
            }
        })
    </script>
</body>
</html>
