# Pad 控制端使用指南

## 🚀 快速开始

### 1. 启动服务端
```bash
# 在服务端机器上运行
node server.js
```
服务端将启动以下服务：
- WebSocket 控制端口：9000
- WebSocket 播放器端口：9001  
- HTTP 文件服务端口：9004

### 2. 启动 Pad 控制端
```bash
npm run dev
```
在浏览器中访问：`http://localhost:5173`

## 📱 功能使用

### 设备管理

#### 添加设备
1. 进入"设备管理"页面
2. 点击"添加设备"按钮
3. 输入设备信息：
   - **设备名称**：自定义名称（如：会议室电脑）
   - **IP地址**：服务端IP（如：***************）
   - **端口**：9000（控制端口）
4. 点击"确认添加"

#### 连接设备
1. 在设备列表中找到目标设备
2. 点击"连接"按钮
3. 等待连接状态变为"已连接"

### 内容管理

#### 获取文件列表
1. 进入"内容管理"页面
2. 选择已连接的设备
3. 点击"获取文件列表"按钮
4. 系统将显示服务端共享文件夹中的所有文件

#### 文件分类浏览
- **全部文件**：显示所有类型的文件
- **视频**：.mp4 等视频文件
- **图片**：.jpg、.png 等图片文件  
- **文档**：.pdf、.pptx、.html 等文档文件

#### 推送到播放器
1. 在文件列表中选择要播放的文件
2. 点击"推送到播放器"按钮
3. 文件将被发送到播放器端播放

#### 用本地应用打开
1. 在文件列表中选择要打开的文件
2. 点击"本地应用打开"按钮
3. 服务端将用默认应用打开该文件

### 播放控制

#### 选择控制设备
1. 进入"播放控制"页面
2. 点击"选择设备"按钮
3. 选择要控制的设备

#### 播放控制操作
- **播放**：开始播放当前内容
- **暂停**：暂停播放
- **停止**：停止播放
- **全屏**：切换全屏模式

#### 音量控制
- 使用滑块调节音量（0-100%）
- 点击音量图标切换静音状态

#### 播放模式
- **单次播放**：播放一次后停止
- **顺序播放**：按顺序播放列表
- **循环播放**：循环播放当前内容

## 🔧 高级功能

### WebSocket 通信协议

#### 获取文件列表
```json
// 发送
{
  "type": "listFiles"
}

// 接收
{
  "type": "fileList",
  "files": {
    "video": [...],
    "image": [...],
    "html": [...],
    "ppt": [...],
    "text": [...],
    "word": [...],
    "pdf": [...],
    "excel": [...]
  }
}
```

#### 推送内容到播放器
```json
// 发送
{
  "type": "pushContent",
  "content": {
    "type": "video",
    "path": "http://***************:9004/shared/demo.mp4",
    "name": "演示视频.mp4"
  }
}

// 接收
{
  "type": "pushContentResponse",
  "success": true,
  "content": {...}
}
```

#### 用本地应用打开
```json
// 1. 获取共享路径
{
  "type": "getSharedPath"
}

// 2. 打开本地应用
{
  "type": "openWithLocalApp",
  "filePath": "C:\\shared\\demo.mp4",
  "appType": "video"
}
```

#### 播放控制
```json
{
  "type": "play"    // 播放
}
{
  "type": "pause"   // 暂停
}
{
  "type": "stop"    // 停止
}
{
  "type": "volume", // 音量控制
  "value": 50
}
```

## 🎯 使用场景

### 会议室演示
1. 连接会议室电脑
2. 获取演示文件列表
3. 选择PPT推送到播放器
4. 使用播放控制进行演示

### 数字标牌管理
1. 连接显示设备
2. 获取广告素材列表
3. 选择视频内容播放
4. 远程控制播放状态

### 教学场景
1. 连接教学电脑
2. 获取课件资源
3. 选择教学视频播放
4. 控制播放进度和音量

## ❗ 注意事项

### 网络要求
- Pad 控制端和服务端需在同一网络
- 确保防火墙允许端口 9000、9001、9004

### 文件格式支持
- **视频**：MP4（推荐）、AVI、MKV 等
- **图片**：JPG、PNG、GIF 等
- **文档**：PDF、PPT、Word、Excel、HTML 等

### 性能建议
- 大文件建议使用"本地应用打开"
- 网络播放适合中小型文件
- 确保服务端性能足够

## 🔍 故障排除

### 连接失败
1. 检查服务端是否启动
2. 确认IP地址和端口正确
3. 检查网络连通性
4. 查看防火墙设置

### 文件列表为空
1. 确认共享文件夹有文件
2. 检查文件权限
3. 查看服务端日志

### 播放失败
1. 确认播放器已连接
2. 检查文件格式支持
3. 验证网络带宽

### 本地应用打开失败
1. 确认文件路径正确
2. 检查文件关联程序
3. 验证文件权限

---

**Pad Controller** - 让远程控制变得简单高效！
