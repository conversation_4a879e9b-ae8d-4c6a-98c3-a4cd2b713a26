<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多屏控制中心</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body class="font-inter bg-gray-900 text-gray-100 min-h-screen flex flex-col">
  <!-- 顶部导航栏 -->
  <header class="bg-gray-800/80 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-gray-700">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <div class="bg-primary/20 p-1.5 rounded-lg">
          <i class="fa fa-television text-primary text-xl"></i>
        </div>
        <h1 class="text-xl font-bold bg-gradient-to-r from-primary to-secondary text-transparent bg-clip-text">多屏控制中心</h1>
      </div>
      <div class="flex items-center space-x-4">
        <button id="refresh-status-btn" class="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center">
          <i class="fa fa-refresh mr-2"></i>刷新状态
        </button>
        <button id="settings-btn" class="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors">
          <i class="fa fa-cog"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <!-- 设备状态概览 -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl shadow-lg p-5 mb-6 border border-gray-700">
      <div class="flex justify-between items-center mb-5">
        <h2 class="text-xl font-semibold bg-gradient-to-r from-blue-400 to-cyan-400 text-transparent bg-clip-text">设备状态</h2>
        <div class="flex items-center space-x-4">
          <div class="flex items-center bg-gray-700/50 px-3 py-1.5 rounded-lg">
            <span class="w-2.5 h-2.5 rounded-full bg-success mr-2"></span>
            <span id="online-count" class="text-sm font-medium">0</span>
            <span class="text-sm text-gray-400 mx-1">/</span>
            <span id="total-count" class="text-sm font-medium">0</span>
            <span class="text-sm text-gray-400 ml-1">在线</span>
          </div>
        </div>
      </div>
      
      <!-- 设备网格 -->
      <div id="device-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <!-- 设备卡片将通过JavaScript动态生成 -->
      </div>
    </div>

    <!-- 内容控制面板 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 内容选择区 -->
      <div class="lg:col-span-1">
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl shadow-lg p-5 h-full border border-gray-700">
          <h2 class="text-lg font-semibold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 text-transparent bg-clip-text">内容管理</h2>
          
          <!-- 内容类型选项卡 -->
          <div class="flex border-b border-gray-700 mb-5">
            <button id="tab-video" class="py-2 px-4 text-blue-400 border-b-2 border-blue-400 font-medium">视频</button>
            <button id="tab-image" class="py-2 px-4 text-gray-400 hover:text-blue-400 transition-colors">图片</button>
            <button id="tab-html" class="py-2 px-4 text-gray-400 hover:text-blue-400 transition-colors">HTML看板</button>
          </div>
          
          <!-- 内容列表占位 -->
          <div id="content-list" class="space-y-3">
            <div class="text-center py-10">
              <div class="w-16 h-16 mx-auto bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                <i class="fa fa-folder-open-o text-gray-400 text-3xl"></i>
              </div>
              <p class="text-gray-400">请先选择一台设备查看内容</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 控制操作区 -->
      <div class="lg:col-span-2">
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl shadow-lg p-5 h-full border border-gray-700">
          <h2 class="text-lg font-semibold mb-4 bg-gradient-to-r from-green-400 to-teal-400 text-transparent bg-clip-text">播放控制</h2>
          
          <!-- 选中设备信息 -->
          <div id="selected-device" class="mb-6 p-4 bg-gray-700/30 rounded-lg border border-gray-600 hidden">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="font-medium text-white mb-1">当前控制：<span id="current-device-name" class="text-blue-400">-</span></h3>
                <p class="text-sm text-gray-400 mb-1">IP地址：<span id="current-device-ip">-</span></p>
              </div>
              <span id="current-device-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                离线
              </span>
            </div>
          </div>
          
          <!-- 播放设置 -->
          <div id="control-panel" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 hidden">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1.5">播放模式</label>
              <select class="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white">
                <option>立即播放</option>
                <option>定时播放</option>
                <option>循环播放</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-1.5">音量</label>
              <div class="flex items-center">
                <input type="range" min="0" max="100" value="70" class="w-full h-1.5 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500">
                <span class="ml-2 text-sm text-gray-400">70%</span>
              </div>
            </div>
          </div>
          
          <!-- 控制按钮 -->
          <div id="control-buttons" class="flex flex-wrap gap-3 justify-center mb-6 hidden">
            <button id="play-btn" class="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center" disabled>
              <i class="fa fa-play mr-2"></i>播放
            </button>
            <button id="pause-btn" class="bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center" disabled>
              <i class="fa fa-pause mr-2"></i>暂停
            </button>
            <button id="stop-btn" class="bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center" disabled>
              <i class="fa fa-stop mr-2"></i>停止
            </button>
            <button id="refresh-btn" class="bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center">
              <i class="fa fa-refresh mr-2"></i>刷新
            </button>
          </div>
          
          <!-- 预览区域 -->
          <div id="preview-area" class="mt-6">
            <h3 class="font-medium text-gray-200 mb-3">内容预览</h3>
            <div class="bg-gray-700/30 rounded-lg p-4 h-64 flex items-center justify-center border border-gray-600">
              <div class="text-center">
                <div class="w-20 h-20 mx-auto bg-gray-600/50 rounded-full flex items-center justify-center mb-4">
                  <i class="fa fa-file-o text-gray-400 text-4xl"></i>
                </div>
                <p class="text-gray-400">选择内容后在此处预览</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-gray-800/80 backdrop-blur-md border-t border-gray-700 py-4 mt-6">
    <div class="container mx-auto px-4 text-center text-gray-400 text-sm">
      <p>© 2025 多屏控制中心 | 版本 1.0.0</p>
    </div>
  </footer>

  <script src="script.js"></script>
</body>
</html>
    