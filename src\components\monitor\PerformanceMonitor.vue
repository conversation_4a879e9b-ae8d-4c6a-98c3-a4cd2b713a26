<template>
  <div class="performance-monitor">
    <!-- 性能指标卡片 -->
    <div class="metrics-grid">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <n-grid-item :span="24" :md="12" :lg="6">
          <n-card class="metric-card">
            <template #header>
              <div class="metric-header">
                <n-icon size="20" color="#007AFF">
                  <HardwareChipOutline />
                </n-icon>
                <span>CPU 使用率</span>
              </div>
            </template>
            
            <div class="metric-content">
              <n-progress 
                type="circle" 
                :percentage="metrics.cpu"
                :color="getMetricColor(metrics.cpu)"
                :stroke-width="8"
                style="margin-bottom: 16px"
              >
                <span class="metric-value">{{ metrics.cpu }}%</span>
              </n-progress>
              
              <div class="metric-status">
                <n-tag 
                  :type="getMetricStatus(metrics.cpu).type"
                  size="small"
                >
                  {{ getMetricStatus(metrics.cpu).text }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="24" :md="12" :lg="6">
          <n-card class="metric-card">
            <template #header>
              <div class="metric-header">
                <n-icon size="20" color="#34C759">
                  <ServerOutline />
                </n-icon>
                <span>内存使用率</span>
              </div>
            </template>
            
            <div class="metric-content">
              <n-progress 
                type="circle" 
                :percentage="metrics.memory"
                :color="getMetricColor(metrics.memory)"
                :stroke-width="8"
                style="margin-bottom: 16px"
              >
                <span class="metric-value">{{ metrics.memory }}%</span>
              </n-progress>
              
              <div class="metric-status">
                <n-tag 
                  :type="getMetricStatus(metrics.memory).type"
                  size="small"
                >
                  {{ getMetricStatus(metrics.memory).text }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="24" :md="12" :lg="6">
          <n-card class="metric-card">
            <template #header>
              <div class="metric-header">
                <n-icon size="20" color="#FF9500">
                  <WifiOutline />
                </n-icon>
                <span>网络使用率</span>
              </div>
            </template>
            
            <div class="metric-content">
              <n-progress 
                type="circle" 
                :percentage="metrics.network"
                :color="getMetricColor(metrics.network)"
                :stroke-width="8"
                style="margin-bottom: 16px"
              >
                <span class="metric-value">{{ metrics.network }}%</span>
              </n-progress>
              
              <div class="metric-status">
                <n-tag 
                  :type="getMetricStatus(metrics.network).type"
                  size="small"
                >
                  {{ getMetricStatus(metrics.network).text }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>

        <n-grid-item :span="24" :md="12" :lg="6">
          <n-card class="metric-card">
            <template #header>
              <div class="metric-header">
                <n-icon size="20" color="#9C88FF">
                  <FolderOutline />
                </n-icon>
                <span>存储使用率</span>
              </div>
            </template>
            
            <div class="metric-content">
              <n-progress 
                type="circle" 
                :percentage="metrics.storage"
                :color="getMetricColor(metrics.storage)"
                :stroke-width="8"
                style="margin-bottom: 16px"
              >
                <span class="metric-value">{{ metrics.storage }}%</span>
              </n-progress>
              
              <div class="metric-status">
                <n-tag 
                  :type="getMetricStatus(metrics.storage).type"
                  size="small"
                >
                  {{ getMetricStatus(metrics.storage).text }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 性能趋势图表 -->
    <div class="performance-charts">
      <n-card title="性能趋势" class="chart-card">
        <div class="chart-container">
          <div class="chart-placeholder">
            <n-icon size="48" color="#ccc">
              <BarChartOutline />
            </n-icon>
            <p>性能趋势图表</p>
            <p class="chart-note">（此处可集成 ECharts 或其他图表库）</p>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 详细指标 -->
    <div class="detailed-metrics">
      <n-card title="详细指标" class="metrics-table-card">
        <n-table :bordered="false" :single-line="false">
          <thead>
            <tr>
              <th>指标名称</th>
              <th>当前值</th>
              <th>平均值</th>
              <th>峰值</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div class="metric-name">
                  <n-icon size="16" color="#007AFF" style="margin-right: 8px">
                    <HardwareChipOutline />
                  </n-icon>
                  CPU 使用率
                </div>
              </td>
              <td>{{ metrics.cpu }}%</td>
              <td>{{ getAverageMetric('cpu') }}%</td>
              <td>{{ getPeakMetric('cpu') }}%</td>
              <td>
                <n-tag :type="getMetricStatus(metrics.cpu).type" size="small">
                  {{ getMetricStatus(metrics.cpu).text }}
                </n-tag>
              </td>
            </tr>
            <tr>
              <td>
                <div class="metric-name">
                  <n-icon size="16" color="#34C759" style="margin-right: 8px">
                    <ServerOutline />
                  </n-icon>
                  内存使用率
                </div>
              </td>
              <td>{{ metrics.memory }}%</td>
              <td>{{ getAverageMetric('memory') }}%</td>
              <td>{{ getPeakMetric('memory') }}%</td>
              <td>
                <n-tag :type="getMetricStatus(metrics.memory).type" size="small">
                  {{ getMetricStatus(metrics.memory).text }}
                </n-tag>
              </td>
            </tr>
            <tr>
              <td>
                <div class="metric-name">
                  <n-icon size="16" color="#FF9500" style="margin-right: 8px">
                    <WifiOutline />
                  </n-icon>
                  网络使用率
                </div>
              </td>
              <td>{{ metrics.network }}%</td>
              <td>{{ getAverageMetric('network') }}%</td>
              <td>{{ getPeakMetric('network') }}%</td>
              <td>
                <n-tag :type="getMetricStatus(metrics.network).type" size="small">
                  {{ getMetricStatus(metrics.network).text }}
                </n-tag>
              </td>
            </tr>
            <tr>
              <td>
                <div class="metric-name">
                  <n-icon size="16" color="#9C88FF" style="margin-right: 8px">
                    <FolderOutline />
                  </n-icon>
                  存储使用率
                </div>
              </td>
              <td>{{ metrics.storage }}%</td>
              <td>{{ getAverageMetric('storage') }}%</td>
              <td>{{ getPeakMetric('storage') }}%</td>
              <td>
                <n-tag :type="getMetricStatus(metrics.storage).type" size="small">
                  {{ getMetricStatus(metrics.storage).text }}
                </n-tag>
              </td>
            </tr>
          </tbody>
        </n-table>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  HardwareChipOutline,
  ServerOutline,
  WifiOutline,
  FolderOutline,
  BarChartOutline
} from '@vicons/ionicons5'

const props = defineProps({
  metrics: {
    type: Object,
    default: () => ({
      cpu: 0,
      memory: 0,
      network: 0,
      storage: 0
    })
  }
})

// 方法
const getMetricColor = (value) => {
  if (value >= 90) return '#FF3B30'
  if (value >= 70) return '#FF9500'
  if (value >= 50) return '#FFCC02'
  return '#34C759'
}

const getMetricStatus = (value) => {
  if (value >= 90) return { type: 'error', text: '严重' }
  if (value >= 70) return { type: 'warning', text: '警告' }
  if (value >= 50) return { type: 'info', text: '正常' }
  return { type: 'success', text: '良好' }
}

const getAverageMetric = (type) => {
  // 模拟平均值计算
  const current = props.metrics[type]
  return Math.max(0, current - Math.floor(Math.random() * 20))
}

const getPeakMetric = (type) => {
  // 模拟峰值计算
  const current = props.metrics[type]
  return Math.min(100, current + Math.floor(Math.random() * 30))
}
</script>
