// OPPO 品牌主题配置
// 基于OPPO官网设计风格和品牌色彩系统

// OPPO 品牌色彩系统
export const oppoColors = {
  // 主色调 - OPPO绿
  primary: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#06b638', // OPPO主绿色
    600: '#059212',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b'
  },

  // 辅助色 - OPPO浅绿
  secondary: {
    50: '#f0fff4',
    100: '#dcfff0',
    200: '#bbffe0',
    300: '#86ffc7',
    400: '#4dffaa',
    500: '#2CFF73', // OPPO浅绿色
    600: '#00e65c',
    700: '#00cc52',
    800: '#00b347',
    900: '#009938'
  },

  // 中性色系
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  },

  // 功能色
  success: '#06b638',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
}

// 渐变色系统
export const oppoGradients = {
  primary: 'linear-gradient(135deg, #06b638 0%, #2CFF73 100%)',
  secondary: 'linear-gradient(135deg, #2CFF73 0%, #86efac 100%)',
  dark: 'linear-gradient(135deg, #171717 0%, #404040 100%)',
  light: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)'
}

// 字体系统
export const oppoTypography = {
  fontFamily: {
    sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
    mono: ['SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'monospace']
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px',
    '5xl': '48px'
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
}

// 间距系统
export const oppoSpacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px',
  '3xl': '64px',
  '4xl': '96px'
}

// 圆角系统
export const oppoBorderRadius = {
  none: '0',
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  '2xl': '24px',
  full: '9999px'
}

// 阴影系统
export const oppoShadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
}

// Naive UI 主题覆盖配置
export const themeOverrides = {
  common: {
    primaryColor: oppoColors.primary[500],
    primaryColorHover: oppoColors.primary[400],
    primaryColorPressed: oppoColors.primary[600],
    primaryColorSuppl: oppoColors.secondary[500],

    successColor: oppoColors.success,
    warningColor: oppoColors.warning,
    errorColor: oppoColors.error,
    infoColor: oppoColors.info,

    textColorBase: oppoColors.neutral[900],
    textColor1: oppoColors.neutral[900],
    textColor2: oppoColors.neutral[700],
    textColor3: oppoColors.neutral[500],
    textColorDisabled: oppoColors.neutral[400],

    placeholderColor: oppoColors.neutral[400],
    placeholderColorDisabled: oppoColors.neutral[300],

    iconColor: oppoColors.neutral[600],
    iconColorHover: oppoColors.primary[500],
    iconColorPressed: oppoColors.primary[600],
    iconColorDisabled: oppoColors.neutral[400],

    opacity1: '0.82',
    opacity2: '0.72',
    opacity3: '0.38',
    opacity4: '0.24',
    opacity5: '0.18',

    dividerColor: oppoColors.neutral[200],
    borderColor: oppoColors.neutral[200],

    closeIconColor: oppoColors.neutral[600],
    closeIconColorHover: oppoColors.neutral[800],
    closeIconColorPressed: oppoColors.neutral[900],

    clearColor: oppoColors.neutral[600],
    clearColorHover: oppoColors.neutral[800],
    clearColorPressed: oppoColors.neutral[900],

    scrollbarColor: oppoColors.neutral[300],
    scrollbarColorHover: oppoColors.neutral[400],

    progressRailColor: oppoColors.neutral[200],
    railColor: oppoColors.neutral[200],
    popoverColor: '#ffffff',
    tableColor: '#ffffff',
    cardColor: '#ffffff',
    modalColor: '#ffffff',
    bodyColor: '#ffffff',
    tagColor: oppoColors.neutral[100],
    avatarColor: oppoColors.neutral[200],
    invertedColor: oppoColors.neutral[900],
    inputColor: '#ffffff',
    codeColor: oppoColors.neutral[100],
    tabColor: oppoColors.neutral[100],
    actionColor: oppoColors.neutral[100],
    tableHeaderColor: oppoColors.neutral[50],
    hoverColor: oppoColors.neutral[50],
    tableColorHover: oppoColors.neutral[50],
    tableColorStriped: oppoColors.neutral[50],
    pressedColor: oppoColors.neutral[100],

    boxShadow1: oppoShadows.sm,
    boxShadow2: oppoShadows.md,
    boxShadow3: oppoShadows.lg,

    borderRadius: oppoBorderRadius.md,
    borderRadiusSmall: oppoBorderRadius.sm,

    fontSize: oppoTypography.fontSize.base,
    fontSizeMini: oppoTypography.fontSize.xs,
    fontSizeTiny: oppoTypography.fontSize.sm,
    fontSizeSmall: oppoTypography.fontSize.sm,
    fontSizeMedium: oppoTypography.fontSize.base,
    fontSizeLarge: oppoTypography.fontSize.lg,
    fontSizeHuge: oppoTypography.fontSize.xl,

    lineHeight: '1.6',
    fontFamily: oppoTypography.fontFamily.sans.join(', ')
  },

  Button: {
    textColor: oppoColors.neutral[700],
    textColorHover: oppoColors.primary[500],
    textColorPressed: oppoColors.primary[600],
    textColorFocus: oppoColors.primary[500],
    textColorDisabled: oppoColors.neutral[400],

    color: '#ffffff',
    colorHover: oppoColors.neutral[50],
    colorPressed: oppoColors.neutral[100],
    colorFocus: oppoColors.neutral[50],
    colorDisabled: oppoColors.neutral[100],

    border: `1px solid ${oppoColors.neutral[200]}`,
    borderHover: `1px solid ${oppoColors.primary[300]}`,
    borderPressed: `1px solid ${oppoColors.primary[400]}`,
    borderFocus: `1px solid ${oppoColors.primary[300]}`,
    borderDisabled: `1px solid ${oppoColors.neutral[200]}`,

    borderRadius: oppoBorderRadius.lg,

    // Primary button
    textColorPrimary: '#ffffff',
    textColorHoverPrimary: '#ffffff',
    textColorPressedPrimary: '#ffffff',
    textColorFocusPrimary: '#ffffff',
    textColorDisabledPrimary: '#ffffff',

    colorPrimary: oppoColors.primary[500],
    colorHoverPrimary: oppoColors.primary[400],
    colorPressedPrimary: oppoColors.primary[600],
    colorFocusPrimary: oppoColors.primary[400],
    colorDisabledPrimary: oppoColors.neutral[300],

    borderPrimary: `1px solid ${oppoColors.primary[500]}`,
    borderHoverPrimary: `1px solid ${oppoColors.primary[400]}`,
    borderPressedPrimary: `1px solid ${oppoColors.primary[600]}`,
    borderFocusPrimary: `1px solid ${oppoColors.primary[400]}`,
    borderDisabledPrimary: `1px solid ${oppoColors.neutral[300]}`
  },

  Card: {
    color: '#ffffff',
    colorModal: '#ffffff',
    colorPopover: '#ffffff',
    colorTarget: '#ffffff',
    colorEmbedded: oppoColors.neutral[50],
    textColor: oppoColors.neutral[900],
    titleTextColor: oppoColors.neutral[900],
    borderColor: oppoColors.neutral[200],
    actionColor: oppoColors.neutral[50],
    borderRadius: oppoBorderRadius.xl,
    paddingMedium: oppoSpacing.lg,
    paddingLarge: oppoSpacing.xl,
    paddingHuge: oppoSpacing['2xl']
  },

  Input: {
    color: '#ffffff',
    colorDisabled: oppoColors.neutral[50],
    colorFocus: '#ffffff',
    textColor: oppoColors.neutral[900],
    textColorDisabled: oppoColors.neutral[400],
    textDecorationColor: oppoColors.neutral[900],
    caretColor: oppoColors.primary[500],
    placeholderColor: oppoColors.neutral[400],
    placeholderColorDisabled: oppoColors.neutral[300],
    border: `1px solid ${oppoColors.neutral[200]}`,
    borderHover: `1px solid ${oppoColors.primary[300]}`,
    borderDisabled: `1px solid ${oppoColors.neutral[200]}`,
    borderFocus: `1px solid ${oppoColors.primary[500]}`,
    boxShadowFocus: `0 0 0 2px ${oppoColors.primary[200]}`,
    loadingColor: oppoColors.primary[500],
    borderRadius: oppoBorderRadius.lg
  },

  Menu: {
    color: '#ffffff',
    colorHover: oppoColors.neutral[50],
    colorActive: oppoColors.primary[50],
    textColor: oppoColors.neutral[700],
    textColorHover: oppoColors.primary[500],
    textColorActive: oppoColors.primary[600],
    borderRadius: oppoBorderRadius.lg
  },

  Tabs: {
    colorSegment: oppoColors.neutral[100],
    tabTextColorLine: oppoColors.neutral[600],
    tabTextColorActiveLine: oppoColors.primary[500],
    tabTextColorHoverLine: oppoColors.primary[400],
    paneTextColor: oppoColors.neutral[900],
    tabBorderColor: oppoColors.neutral[200],
    tabBorderColorActive: oppoColors.primary[500],
    barColor: oppoColors.primary[500]
  }
}

// 响应式断点
export const oppoBreakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// 动画配置
export const oppoAnimations = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  easing: {
    ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
}

export default {
  oppoColors,
  oppoGradients,
  oppoTypography,
  oppoSpacing,
  oppoBorderRadius,
  oppoShadows,
  themeOverrides,
  oppoBreakpoints,
  oppoAnimations
}