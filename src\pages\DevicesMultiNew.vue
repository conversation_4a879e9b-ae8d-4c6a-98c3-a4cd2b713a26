<template>
  <div class="devices-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">设备管理</h1>
        <p class="page-description">选择多个设备进行批量操作</p>
      </div>
      <div class="header-actions">
        <div class="mode-buttons">
          <button class="mode-btn" @click="$router.push('/devices')">单选模式</button>
          <button class="mode-btn mode-btn-active">多选模式</button>
        </div>
        <div class="action-buttons">
          <button class="action-btn" @click="showConnectionTester = true">连接测试</button>
          <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedDevices.length > 0" class="batch-actions">
      <div class="batch-content">
        <div class="batch-info">
          <span class="batch-count">已选择 {{ selectedDevices.length }} 个设备</span>
        </div>
        <div class="batch-buttons">
          <button class="action-btn" @click="batchConnect">批量连接</button>
          <button class="action-btn" @click="batchDisconnect">批量断开</button>
          <button class="action-btn action-btn-primary" @click="batchGoToContents">批量管理内容</button>
          <button class="action-btn" @click="clearSelection">清除选择</button>
        </div>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="devices-section">
      <div class="devices-grid">
        <div
          v-for="(device, index) in devices"
          :key="`${device.ip}:${device.port}`"
          class="device-card"
          :class="{ 
            'device-connected': device.connected,
            'device-connecting': device.connecting,
            'device-selected': selectedDevices.includes(index)
          }"
        >
          <!-- 选择框 -->
          <div class="device-checkbox">
            <input 
              type="checkbox" 
              :checked="selectedDevices.includes(index)"
              @change="toggleDeviceSelection(index)"
              class="checkbox-input"
            />
          </div>

          <!-- 设备状态指示器 -->
          <div class="device-status-indicator">
            <div 
              class="status-dot" 
              :class="{
                'status-connected': device.connected,
                'status-connecting': device.connecting,
                'status-disconnected': !device.connected && !device.connecting
              }"
            ></div>
          </div>

          <!-- 设备信息 -->
          <div class="device-info">
            <div class="device-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" :fill="device.connected ? '#000' : '#86868b'"/>
              </svg>
            </div>
            
            <h3 class="device-name">{{ device.name || `设备 ${index + 1}` }}</h3>
            <p class="device-address">{{ device.ip }}:{{ device.port }}</p>
            
            <div class="device-status">
              <span 
                class="status-badge"
                :class="{
                  'status-badge-connected': device.connected,
                  'status-badge-connecting': device.connecting,
                  'status-badge-disconnected': !device.connected && !device.connecting
                }"
              >
                {{ getStatusText(device) }}
              </span>
            </div>
          </div>

          <!-- 设备操作 -->
          <div class="device-actions">
            <button
              v-if="!device.connected && !device.connecting"
              class="connect-btn"
              @click="connectDevice(index)"
            >
              连接设备
            </button>
            
            <button
              v-else-if="device.connecting"
              class="connect-btn connect-btn-loading"
              disabled
            >
              连接中...
            </button>
            
            <div v-else class="connected-actions">
              <button
                class="action-btn action-btn-primary"
                @click="goToContents(index)"
              >
                内容管理
              </button>
              <button
                class="action-btn"
                @click="disconnectDevice(index)"
              >
                断开连接
              </button>
            </div>
          </div>

          <!-- 设备菜单 -->
          <div class="device-menu">
            <button class="menu-btn" @click="handleDeviceMenu(index)">⋯</button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="devices.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="#86868b"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无设备</h3>
        <p class="empty-description">请先添加设备以开始使用</p>
        <button class="action-btn action-btn-primary" @click="showAddDialog = true">添加设备</button>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <div v-if="showAddDialog" class="modal-overlay" @click="showAddDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">添加设备</h2>
          <button class="modal-close" @click="showAddDialog = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">设备名称</label>
            <input v-model="newDevice.name" class="form-input" placeholder="请输入设备名称" />
          </div>
          <div class="form-group">
            <label class="form-label">IP地址</label>
            <input v-model="newDevice.ip" class="form-input" placeholder="请输入IP地址" />
          </div>
          <div class="form-group">
            <label class="form-label">端口</label>
            <input v-model="newDevice.port" type="number" class="form-input" placeholder="请输入端口号" />
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showAddDialog = false">取消</button>
          <button class="action-btn action-btn-primary" @click="handleAddDevice">添加</button>
        </div>
      </div>
    </div>

    <!-- 连接测试器 -->
    <ConnectionTester v-model="showConnectionTester" />

    <!-- 批量连接成功对话框 -->
    <div v-if="showSuccessModal" class="modal-overlay" @click="showSuccessModal = false">
      <div class="modal-content success-modal" @click.stop>
        <div class="modal-header">
          <div class="success-header">
            <div class="success-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#34c759" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <h2 class="modal-title">批量连接成功</h2>
          </div>
          <button class="modal-close" @click="showSuccessModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="success-content">
            <p class="success-message">已成功连接 <strong>{{ connectedDeviceCount }}</strong> 个设备！</p>
            <p class="success-question">是否立即进入内容管理页面获取多个设备的内容？</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn" @click="showSuccessModal = false">稍后再说</button>
          <button class="action-btn action-btn-primary" @click="goToContentsFromModal">立即进入</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDeviceStore } from '@/store/device'
import ConnectionTester from '@/components/ConnectionTester.vue'

const router = useRouter()
const deviceStore = useDeviceStore()
const { devices, addDevice, connectDevice: storeConnectDevice, disconnectDevice: storeDisconnectDevice } = deviceStore

// 响应式数据
const showAddDialog = ref(false)
const showConnectionTester = ref(false)
const showSuccessModal = ref(false)
const selectedDevices = ref([])
const connectedDeviceCount = ref(0)
const newDevice = ref({
  name: '',
  ip: '',
  port: 9000
})

// 方法
const getStatusText = (device) => {
  if (device.connecting) return '连接中'
  if (device.connected) return '已连接'
  return '未连接'
}

const toggleDeviceSelection = (index) => {
  const selectedIndex = selectedDevices.value.indexOf(index)
  if (selectedIndex > -1) {
    selectedDevices.value.splice(selectedIndex, 1)
  } else {
    selectedDevices.value.push(index)
  }
}

const clearSelection = () => {
  selectedDevices.value = []
}

const connectDevice = async (index) => {
  const device = devices[index]
  device.connecting = true
  
  try {
    const wsConnection = storeConnectDevice(index)
    if (wsConnection) {
      wsConnection.connect()
    }
  } catch (error) {
    device.connecting = false
    console.error('连接失败:', error)
  }
}

const disconnectDevice = (index) => {
  try {
    storeDisconnectDevice(index)
  } catch (error) {
    console.error('断开失败:', error)
  }
}

const batchConnect = () => {
  let connectedCount = 0
  const totalSelected = selectedDevices.value.length

  selectedDevices.value.forEach(index => {
    if (!devices[index].connected && !devices[index].connecting) {
      connectDevice(index)

      // 监听连接成功
      const checkConnection = setInterval(() => {
        if (devices[index].connected) {
          clearInterval(checkConnection)
          connectedCount++

          // 如果所有选中的设备都连接完成，显示成功模态框
          if (connectedCount === totalSelected) {
            showBatchConnectionSuccessModal(connectedCount)
          }
        }
      }, 500)
    }
  })
}

const batchDisconnect = () => {
  selectedDevices.value.forEach(index => {
    if (devices[index].connected) {
      disconnectDevice(index)
    }
  })
}

const batchGoToContents = () => {
  router.push('/contents')
}

const goToContents = () => {
  router.push('/contents')
}

const showBatchConnectionSuccessModal = (connectedCount) => {
  showSuccessModal.value = true
  connectedDeviceCount.value = connectedCount
}

const goToContentsFromModal = () => {
  showSuccessModal.value = false
  router.push('/contents')
}

const handleAddDevice = () => {
  if (!newDevice.value.name || !newDevice.value.ip || !newDevice.value.port) {
    alert('请填写完整的设备信息')
    return
  }
  
  // 检查设备是否已存在
  const exists = devices.some(d => d.ip === newDevice.value.ip && d.port === newDevice.value.port)
  if (exists) {
    alert('该设备已存在')
    return
  }
  
  addDevice({
    ...newDevice.value,
    connected: false,
    connecting: false,
    connectionState: 'disconnected'
  })
  
  showAddDialog.value = false
  
  // 重置表单
  newDevice.value = {
    name: '',
    ip: '',
    port: 9000
  }
}

const handleDeviceMenu = (index) => {
  // 设备菜单功能
  console.log('设备菜单:', index)
}
</script>

<style scoped>
/* 多设备管理页面样式 */
.devices-page {
  min-height: 100vh;
  background: #f5f5f7;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #86868b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.mode-buttons {
  display: flex;
  background: #f5f5f7;
  border-radius: 8px;
  padding: 4px;
}

.mode-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #86868b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-btn-active {
  background: white;
  color: #007aff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border: 1px solid #d2d2d7;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f5f5f7;
}

.action-btn-primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn-primary:hover {
  background: #0056cc;
}

/* 批量操作栏 */
.batch-actions {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 1px solid #007aff;
  border-radius: 12px;
  padding: 16px 24px;
  margin-bottom: 24px;
  animation: slideDown 0.3s ease;
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-count {
  font-size: 16px;
  font-weight: 600;
  color: #007aff;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.batch-buttons .action-btn {
  padding: 8px 16px;
  font-size: 13px;
}

/* 设备列表 */
.devices-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.device-card {
  position: relative;
  background: #f9f9f9;
  border: 1px solid #e5e5e7;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.device-connected {
  border-color: #34c759;
  background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
}

.device-connecting {
  border-color: #ff9500;
  background: linear-gradient(135deg, #fffbf0 0%, #fef3c7 100%);
}

.device-selected {
  border-color: #007aff;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.2);
}

.device-checkbox {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.device-status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d2d2d7;
}

.status-connected {
  background: #34c759;
}

.status-connecting {
  background: #ff9500;
  animation: pulse 2s infinite;
}

.status-disconnected {
  background: #d2d2d7;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.device-info {
  text-align: center;
  margin: 40px 0 20px 0;
}

.device-icon {
  margin-bottom: 12px;
}

.device-name {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.device-address {
  font-size: 14px;
  color: #86868b;
  font-family: 'SF Mono', Monaco, monospace;
  margin: 0 0 12px 0;
}

.device-status {
  margin-bottom: 16px;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge-connected {
  background: #d1fae5;
  color: #065f46;
}

.status-badge-connecting {
  background: #fef3c7;
  color: #92400e;
}

.status-badge-disconnected {
  background: #f3f4f6;
  color: #6b7280;
}

.device-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.connect-btn {
  width: 100%;
  padding: 12px;
  border: none;
  background: #007aff;
  color: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connect-btn:hover {
  background: #0056cc;
}

.connect-btn-loading {
  background: #86868b;
  cursor: not-allowed;
}

.connected-actions {
  display: flex;
  gap: 8px;
}

.connected-actions .action-btn {
  flex: 1;
  text-align: center;
  padding: 10px 12px;
}

.device-menu {
  position: absolute;
  bottom: 16px;
  right: 16px;
}

.menu-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  font-size: 16px;
  color: #86868b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-btn:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #86868b;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  margin: 0 0 24px 0;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f7;
  border-radius: 50%;
  font-size: 18px;
  color: #86868b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #e5e5e7;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e5e7;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  font-size: 16px;
  color: #1d1d1f;
  background: white;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #007aff;
}

.form-input::placeholder {
  color: #86868b;
}

/* 成功模态框 */
.success-modal .modal-header {
  border-bottom: none;
  padding-bottom: 0;
}

.success-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.success-icon {
  width: 32px;
  height: 32px;
  background: #d1fae5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-content {
  text-align: center;
}

.success-message {
  font-size: 16px;
  color: #1d1d1f;
  margin: 0 0 12px 0;
}

.success-question {
  font-size: 14px;
  color: #86868b;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .devices-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .devices-grid {
    grid-template-columns: 1fr;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style>
