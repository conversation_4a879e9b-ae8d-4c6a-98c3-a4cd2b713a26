/* OPPO 品牌全局样式 */
/* 基于OPPO官网设计风格 */

/* CSS 变量定义 */
:root {
  /* OPPO 品牌色彩 */
  --oppo-primary-50: #f0fdf4;
  --oppo-primary-100: #dcfce7;
  --oppo-primary-200: #bbf7d0;
  --oppo-primary-300: #86efac;
  --oppo-primary-400: #4ade80;
  --oppo-primary-500: #06b638;
  --oppo-primary-600: #059212;
  --oppo-primary-700: #047857;
  --oppo-primary-800: #065f46;
  --oppo-primary-900: #064e3b;

  --oppo-secondary-50: #f0fff4;
  --oppo-secondary-100: #dcfff0;
  --oppo-secondary-200: #bbffe0;
  --oppo-secondary-300: #86ffc7;
  --oppo-secondary-400: #4dffaa;
  --oppo-secondary-500: #2CFF73;
  --oppo-secondary-600: #00e65c;
  --oppo-secondary-700: #00cc52;
  --oppo-secondary-800: #00b347;
  --oppo-secondary-900: #009938;

  /* 中性色 */
  --oppo-neutral-50: #fafafa;
  --oppo-neutral-100: #f5f5f5;
  --oppo-neutral-200: #e5e5e5;
  --oppo-neutral-300: #d4d4d4;
  --oppo-neutral-400: #a3a3a3;
  --oppo-neutral-500: #737373;
  --oppo-neutral-600: #525252;
  --oppo-neutral-700: #404040;
  --oppo-neutral-800: #262626;
  --oppo-neutral-900: #171717;

  /* 功能色 */
  --oppo-success: #06b638;
  --oppo-warning: #f59e0b;
  --oppo-error: #ef4444;
  --oppo-info: #3b82f6;

  /* 渐变色 */
  --oppo-gradient-primary: linear-gradient(135deg, #06b638 0%, #2CFF73 100%);
  --oppo-gradient-secondary: linear-gradient(135deg, #2CFF73 0%, #86efac 100%);
  --oppo-gradient-dark: linear-gradient(135deg, #171717 0%, #404040 100%);
  --oppo-gradient-light: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);

  /* 间距 */
  --oppo-spacing-xs: 4px;
  --oppo-spacing-sm: 8px;
  --oppo-spacing-md: 16px;
  --oppo-spacing-lg: 24px;
  --oppo-spacing-xl: 32px;
  --oppo-spacing-2xl: 48px;
  --oppo-spacing-3xl: 64px;
  --oppo-spacing-4xl: 96px;

  /* 圆角 */
  --oppo-radius-sm: 4px;
  --oppo-radius-md: 8px;
  --oppo-radius-lg: 12px;
  --oppo-radius-xl: 16px;
  --oppo-radius-2xl: 24px;
  --oppo-radius-full: 9999px;

  /* 阴影 */
  --oppo-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --oppo-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --oppo-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --oppo-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --oppo-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* 字体 */
  --oppo-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --oppo-font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', monospace;

  /* 字体大小 */
  --oppo-text-xs: 12px;
  --oppo-text-sm: 14px;
  --oppo-text-base: 16px;
  --oppo-text-lg: 18px;
  --oppo-text-xl: 20px;
  --oppo-text-2xl: 24px;
  --oppo-text-3xl: 30px;
  --oppo-text-4xl: 36px;
  --oppo-text-5xl: 48px;

  /* 动画 */
  --oppo-duration-fast: 150ms;
  --oppo-duration-normal: 300ms;
  --oppo-duration-slow: 500ms;
  --oppo-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--oppo-font-family);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  background-color: var(--oppo-neutral-50);
  color: var(--oppo-neutral-900);
  font-size: var(--oppo-text-base);
  overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--oppo-neutral-100);
  border-radius: var(--oppo-radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--oppo-neutral-300);
  border-radius: var(--oppo-radius-full);
  transition: background-color var(--oppo-duration-fast) var(--oppo-easing);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--oppo-neutral-400);
}

/* 选择文本样式 */
::selection {
  background-color: var(--oppo-primary-200);
  color: var(--oppo-primary-900);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--oppo-primary-500);
  outline-offset: 2px;
  border-radius: var(--oppo-radius-sm);
}

/* 通用工具类 */
.oppo-gradient-primary {
  background: var(--oppo-gradient-primary);
}

.oppo-gradient-secondary {
  background: var(--oppo-gradient-secondary);
}

.oppo-gradient-text {
  background: var(--oppo-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.oppo-shadow-sm {
  box-shadow: var(--oppo-shadow-sm);
}

.oppo-shadow-md {
  box-shadow: var(--oppo-shadow-md);
}

.oppo-shadow-lg {
  box-shadow: var(--oppo-shadow-lg);
}

.oppo-shadow-xl {
  box-shadow: var(--oppo-shadow-xl);
}

/* 动画类 */
.oppo-transition {
  transition: all var(--oppo-duration-normal) var(--oppo-easing);
}

.oppo-transition-fast {
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
}

.oppo-transition-slow {
  transition: all var(--oppo-duration-slow) var(--oppo-easing);
}

/* 悬停效果 */
.oppo-hover-lift {
  transition: transform var(--oppo-duration-fast) var(--oppo-easing),
              box-shadow var(--oppo-duration-fast) var(--oppo-easing);
}

.oppo-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--oppo-shadow-lg);
}

/* 卡片样式 */
.oppo-card {
  background: white;
  border-radius: var(--oppo-radius-xl);
  box-shadow: var(--oppo-shadow-sm);
  border: 1px solid var(--oppo-neutral-200);
  transition: all var(--oppo-duration-normal) var(--oppo-easing);
}

.oppo-card:hover {
  box-shadow: var(--oppo-shadow-md);
  border-color: var(--oppo-primary-200);
}

/* 按钮样式增强 */
.oppo-btn-primary {
  background: var(--oppo-gradient-primary);
  border: none;
  color: white;
  font-weight: 500;
  border-radius: var(--oppo-radius-lg);
  padding: var(--oppo-spacing-sm) var(--oppo-spacing-lg);
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
  cursor: pointer;
}

.oppo-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--oppo-shadow-md);
}

.oppo-btn-secondary {
  background: white;
  border: 1px solid var(--oppo-primary-500);
  color: var(--oppo-primary-500);
  font-weight: 500;
  border-radius: var(--oppo-radius-lg);
  padding: var(--oppo-spacing-sm) var(--oppo-spacing-lg);
  transition: all var(--oppo-duration-fast) var(--oppo-easing);
  cursor: pointer;
}

.oppo-btn-secondary:hover {
  background: var(--oppo-primary-50);
  transform: translateY(-1px);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .oppo-mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .oppo-desktop-hidden {
    display: none !important;
  }
}

/* 平板优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .oppo-tablet-optimized {
    padding: var(--oppo-spacing-lg);
  }

  .oppo-tablet-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--oppo-spacing-lg);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .oppo-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .oppo-hover-lift:hover {
    transform: none;
  }
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--oppo-duration-normal) var(--oppo-easing);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 加载动画 */
@keyframes oppo-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.oppo-pulse {
  animation: oppo-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes oppo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.oppo-spin {
  animation: oppo-spin 1s linear infinite;
}

/* 状态指示器 */
.oppo-status-online {
  color: var(--oppo-success);
}

.oppo-status-offline {
  color: var(--oppo-neutral-400);
}

.oppo-status-error {
  color: var(--oppo-error);
}

.oppo-status-warning {
  color: var(--oppo-warning);
}

/* 徽章样式 */
.oppo-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--oppo-spacing-xs) var(--oppo-spacing-sm);
  border-radius: var(--oppo-radius-full);
  font-size: var(--oppo-text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.oppo-badge-success {
  background: var(--oppo-primary-100);
  color: var(--oppo-primary-700);
}

.oppo-badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.oppo-badge-error {
  background: #fee2e2;
  color: #991b1b;
}

.oppo-badge-info {
  background: #dbeafe;
  color: #1e40af;
}